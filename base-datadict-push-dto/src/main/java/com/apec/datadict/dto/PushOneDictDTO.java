package com.apec.datadict.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PushOneDictDTO {
    private String exchangeId;
    private String subSystemCode;
    private String dictCode;
    private String dictValue;
    private String dictEnValue;
    private String dictType;
    private String dictLevel;
    private String dictStatus;
    private String img;
    private String imgId;
    private String subSystemBizType;
    private List<PushOneDictSubDTO> dictSubList;
}
