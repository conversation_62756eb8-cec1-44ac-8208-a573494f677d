package com.apec.datadict.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
/**
 * 类 编 号：
 * 类 名 称：DataDictSubDTO
 * 内容摘要：多条件查询
 * 创建日期：2018/08/09
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataDictSubDTO {
    private String dictCode;
    private String dictSubCode;
    private String dictSubValue;
    private String dictSubEnValue;
    private String dictStatus;
    private String imgId;
    private String img;
    private String id;
    private String status;
}