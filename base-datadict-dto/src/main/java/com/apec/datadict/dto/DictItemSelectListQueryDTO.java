package com.apec.datadict.dto;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 类 编 号：
 * 类 名 称：DictItemSelectListQueryDTO
 * 内容摘要：多条件查询
 * 创建日期：2018/08/09
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DictItemSelectListQueryDTO
{
    private List<DataDictSubDTO>  listData;
    private String dictStatus;
}
