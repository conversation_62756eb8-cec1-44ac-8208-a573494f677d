package com.apec.framework.common.constant;

/**
 * 会员常量属性
 * 
 * <AUTHOR>
 * 
 */
public class UserConstants
{

	/**
	 * 会员密码
	 */
	public static final String U_PASSWORD = "password";
	/**
	 * 会员密码
	 */
	public static final String U_PAY_PWD = "payPwd";
	/**
	 * 会员账号
	 */
	public static final String U_ACCOUNT = "account";
	/**
	 * 会员名称
	 */
	public static final String U_NAME = "name";
	/**
	 * 会员id
	 */
	public static final String U_ID = "userId";
	/**
	 * 会员编码
	 */
	public static final String U_USER_CODE = "mainUserCode";
	/**
	 * 会员父id
	 */
	public static final String U_PARENT_ID = "parentId";
	/**
	 * TA参与人编码(托管账户)
	 */
	public static final String U_TA_PART_CODE = "taPartCode";
	/**
	 * 托管账户名称
	 */
	public static final String U_TA_PART_NAME = "taPartName";
	/**
	 * 参与人编码
	 */
	public static final String U_PART_CODE = "partCode";
	/**
	 * 结算帐号
	 */
	public static final String U_SETTLE_ACCOUNT = "settleAccount";
	/**
	 * 参与人名称
	 */
	public static final String U_PART_NAME = "partFullName";
	/**
	 * 参与人简称
	 */
	public static final String U_PART_SHORT_NAME = "partShortName";
	/**
	 * 上次登录时间
	 */
	public static final String U_LAST_LOGIN_TIME = "lastLoginTime";
	/**
	 * 上次登录ip
	 */
	public static final String U_LAST_LOGIN_IP = "lastLoginIp";
	/**
	 * 本次登录ip
	 */
	public static final String U_CURRENT_LOGIN_IP = "currentLoginIp";
	/**
	 * 本次登录ip
	 */
	public static final String U_CURRENT_LOGIN_TIME = "currentLoginTime";
	/**
	 * 登录次数
	 */
	public static final String U_LOGIN_NUM = "loginNum";
	/**
	 * 会员注册时间
	 */
	public static final String U_GMT_CREATE = "gmtCreate";
	/**
	 * 会员状态
	 */
	public static final String U_STATUS = "status";
	/**
	 * 会员状态
	 */
	public static final String U_EXCLUDE_STATUS = "excludedStatus";
	/**
	 * 是否是操作员
	 */
	public static final String U_IS_OPERATOR = "isOperator";
	/**
	 * 部门
	 */
	public static final String U_DEPAREMENT = "department";
	/**
	 * 邮箱
	 */
	public static final String U_EMAIL = "email";
	/**
	 * 固定电话
	 */
	public static final String U_PHONE = "phone";
	/**
	 * 手机
	 */
	public static final String U_MOBILE = "mobile";

	/**
	 * reg手机
	 */
	public static final String U_REG_MOBILE = "cellPhone";

	/**
	 * reg邮箱
	 */
	public static final String U_REG_EMAIL = "perEmail";

	/**
	 * 传真
	 */
	public static final String U_FAX = "fax";
	/**
	 * 备注
	 */
	public static final String U_REMARK = "remark";
	/**
	 * 会员类型
	 */
	public static final String U_TYPE = "userType";
	/**
	 * 会员头像
	 */
	public static final String U_PORTRAIT_URL = "portraitUrl";
	/**
	 * 审批时间
	 */
	public static final String U_GMT_AUDIT = "gmtAudit";
	/**
	 * 审批备注
	 */
	public static final String U_AUDIT_REMARK = "auditRemark";

	/**
	 * 会员角色
	 */
	public static final String U_USER_ROLE = "userRole";

	/**
	 * 表单id
	 */
	public static final String U_FORM_ID = "formId";

	/**
	 * 表单CODE
	 */
	public static final String U_FORM_CODE = "formCode";

	/**
	 * 资料变更申请的查看
	 */
	public static final String U_INFO_APPLY = "infoApply";

	/**
	 * 主会员用户密码
	 */
	public static final String M_USER_PASSWORD = "password";

	/**
	 * 会员注册附件文件类型
	 */
	public static final String U_MEMBER_FILE = "memberFile";

	/**
	 * 机构code
	 */
	public static final String U_AGENT_CODE = "agencyCode";

	/**
	 * 机构名称
	 */
	public static final String U_AGENT_NAME = "agencyName";
	/**
	 * 是否三证合一标志位
	 */
	public static final String U_PARTCATEGORIES = "partCategories";
	/**
	 * 会员类别二级
	 */
	public static final String U_PARTCATEGORIES1 = "partCategories1";
	/**
	 * 会员类别三级
	 */
	public static final String U_PARTCATEGORIES2 = "partCategories2";
	/**
	 * 会员类别全名
	 */
	public static final String U_FULL_PARTCATEGORIES = "fullPartCategories";

	/**
	 * 预注册状态key
	 */
	public static final String U_PREREG_STATUS = "prereg_status";

	/**
	 * 入会时间
	 */
	public static final String U_PART_SINCE = "partSince";

	/**
	 * 账户对应的会员编号
	 */
	public static final String U_MEMBER_CODE = "memberCode";

	/**
	 * 上级机构会员编号
	 */
	public static final String U_PARENT_ORG_ID = "parentOrgId";

	/**
	 * 工作流节点名称
	 */
	public static final String U_ACTIVITY_NAME = "activityName";

	/**
	 * 会员表的节点名称排序
	 */
	public static final String ACTIVITY_SORT_NO = "ACTIVITY_SORT_NO";

	/**
	 * 注册来源
	 */
	public static final String U_REG_SOURCE = "regSource";

	/**
	 * 业务角色code
	 */
	public static final String U_ROLE_CODE = "roleCode";

	/**
	 * 审核流水表的申请时间
	 */
	public static final String U_APPLY_TIME = "applyTime";

	/**
	 * 审核流水表的审核时间
	 */
	public static final String U_AUDIT_TIME = "auditTime";

	/**
	 * 审核流水表的申请
	 */
	public static final String U_AUDIT_APPLY = "auditApply";

	/**
	 * 审核流水表的审核类型
	 */
	public static final String U_AUDIT_TYPE = "auditType";

	/**
	 * 根会员id
	 */
	public static final String U_ROOT_USER_ID = "rootUserId";

	/**
	 * 只查操作员的标志位
	 */
	public static final String U_OPT_ONLY = "optOnly";
	
	/**
	 * 操作员编号
	 */
	public static final String U_OPERATOR_CODE = "optUserId";

	/**
	 * 查询会员账户的标志位
	 */
	public static final String U_IS_USER_ACC = "isUserAccount";

	public static final String U_EXCLUDE_USER_ID = "excludedUserId";

	/**
	 * 额外的查询sql
	 */
	public static final String EXT_QUERY_STR = "extendQueryStr";

	/**
	 * 排序sql
	 */
	public static final String ORDER_STR = "orderStr";

	/**
	 * 业务操作类型(add,edit,query,save)
	 */
	public static final String BIZ_OPT_TYPE = "bizOptType";

	/**
	 * 业务表单页面配置key
	 */
	public static final String BIZ_PAGE_CONFIG = "bizPageConfig";

	/**
	 * ucDataSet
	 */
	public static final String UC_DATA_SET = "ucDataSet";

	public static final String USR_AGENT_ID = "userAgentId";

	public static final String USR_AGENT_ROOT_ID = "userAgentRootUserId";

	public static final String USR_AGENT_ACC = "usrAgentAcc";

	public static final String USR_AGENT_NAME = "usrAgentName";

	public static final String USR_AGENT_COMPANY_CN = "usrAgentCompanyCn";

	public static final String AUDIT_RESULT = "auditResult";

	public static final String U_PARENT_RELATE_ID = "parentRelateId";

	/**
	 * 查看类型，新增，修改，详情，列表。EnumAttrViewType枚举
	 */
	public static final String U_VIEW_TYPE = "viewType";

	/**
	 * 显示位置类型，前台、后台。EnumAttrShowPosition枚举
	 */
	public static final String U_VIEW_POS = "viewPosition";

	public static final String U_CREATOR_ID = "createrId";

	/**
	 * 持有人账户用户userId
	 */
	public static final String U_HOLDER_ACC_USR_ID = "holderAccountUserId";

	/**
	 * 持有人账户
	 */
	public static final String U_HOLDER_ACC = "holderAccount";
	/**
	 * 持有人账户状态
	 */
	public static final String U_HOLDER_ACC_STATUS = "holderAccStatus";
	/**
	 * 持有人账户名称
	 */
	public static final String U_HOLDER_NAME = "holderName";

	/**
	 * 资金账号
	 */
	public static final String U_FUND_ACC = "fundAccount";

	/**
	 * TA账号
	 */
	public static final String U_TA_ACC = "taAccount";

	/**
	 * 是否管理员
	 */
	public static final String U_IS_ADMIN = "isAdmin";

	/**
	 * 主会员信息
	 */
	public static final String U_MAIN_USER_INFO = "mainUserInfo";

	/**
	 * 审核流水表id
	 */
	public static final String U_PART_AUDIT_INFO_ID = "partAuditInfoId";

	/**
	 * 会员登录有效的token
	 */
	public static final String U_VALID_TOKEN = "validToken";

	/**
	 * 企业id
	 */
	public static final String U_COMP_ID = "compId";

	/**
	 * 所属经纪商账号
	 */
	public static final String U_PARENT_AGENT_ACC = "parentAgentAccount";

	/**
	 * 分页当前页
	 */
	public static final String U_CURR_PAGE = "currentPage";

	/**
	 * 分页条数
	 */
	public static final String U_PAGE_SIZE = "pageSize";

	/**
	 * 总条数
	 */
	public static final String U_TOTAL_ITEM = "totalItem";

	/**
	 * 证件号
	 */
	public static final String U_ID_NO = "certNo";
	/**
	 * 证件类型
	 */
	public static final String U_ID_TYPE = "certType";

	/**
	 * 会话id属性
	 */
	public static final String U_SESSION_ID = "sessionId";

	public final static String ACTIVE_INIT = "0";

	public final static String ACTIVE_USED = "1";

	public final static String ACTIVE_FORGET = "forget";

	/**
	 * 是否邮箱认证
	 */
	public static final String IS_EMAIL_VERIFY = "isEmailVerify";
	/**
	 * 是否实名认证
	 */
	public static final String IS_REAL_NAME_VERIFY = "isRealNameVerify";
	/**
	 * 是否手机认证
	 */
	public static final String IS_MOBILE_VERIFY = "isMobileVerify";

	/**
	 * 用户头像
	 */
	public static final String USER_AVATAR_ATTR = "userAvatar";

	/**
	 * 证件号属性
	 */
	public static final String CERT_NO_ATTR = "certNo";

	public static final String U_IS_LOGOUT = "isLogout";

	/**
	 * 当前登录用户的sessionId集合的缓存key
	 */
	public static final String CACHE_LOGINED_IDS = "loginedSessionIds";

	/**
	 * 手机号
	 */
	public static final String CELL_PHONE = "cellPhone";
	/**
	 * 推荐用户手机号
	 */
	public static final String RECOMMEND_MOBILE = "recommendMobile";
	/**
	 * 推荐用户
	 */
	public static final String RECOMMEND_ACCOUNT = "recommendAccount";
	/**
	 * 推荐用户
	 */
	public static final String RECOMMEND_ID = "recommendID";
	/**
	 * 运营模式
	 */
	public static final String TRADE_TYPE = "tradeType";
	/**
	 * 币种
	 */
	public static final String MONEY_TYPE = "moneyType";
	/**
	 * 市场
	 */
	public static final String MARKET_ID = "marketId";
	
	
	public static final String CARRIER_CODE = "carrierCode";
	
	/**
	 * 账户类型
	 */
	public static final String ACOUNT_TYPE = "accountType";
	
	/**
	 * 手机号
	 */
	public static final String JBR_CERT_NO = "jbrCertNo";
	/**
	 * 外围系统编码
	 */
	public static final String EXTSYSNO="extSysNo";
	
	/**
	 * 是否存在与交易商的关联关系
	 */
	public static final String ISRELATEUSER="isRelateUser";
	/**
	 * 审核理由
	 */
	public static final String AUDITRESAON="auditResaon";
	/**
	 * 手机激活code
	 */
	public static final String ACTIVE_CODE = "code";
	/**
	 * web端是否退出的标志位
	 */
	public static final String U_IS_WEB_LOGOUT_FLAG = "isWebLogoutFlag";
	/**
	 * 是否需要重新获取菜单权限标志位
	 */
	public static final String U_NEED_REFETCH_MENU_AUTH_FLAG = "needFetchMenuAuthFlag";
	/**
	 * 是否需要重新获取交易权限标志位
	 */
	public static final String U_NEED_REFETCH_TRADE_AUTH_FLAG = "needFetchTradeAuthFlag";
	/**
	 * 是否需要重新获取组织标志位
	 */
	public static final String U_NEED_REFETCH_MARKET_FLAG = "needFetchMarketFlag";
	/**
	 * 积分帐号
	 */
	public static final String INTEGRAL_ACCOUNT = "integralAccount";
	
	/**
	 * 法定代表人
	 */
	public static final String LEGAL_REPR = "legalRepr";
	
	/**
	 * 组织机构代码
	 */
	public static final String ORG_CODE = "orgCode";
	
	/**
	 * 税务登记证号码
	 */
	public static final String TAX_REGI_CERT = "taxRegiCert";
	
	/**
	 * 是否资料补全
	 */
	public static final String IS_INFO_COMPLETION = "isInfoCompletion";
	
	
	/**
	 * 是否同步会员
	 */
	public static final String U_IS_SYNC = "isSync";
	
	
	

}
