package com.apec.framework.common.context;

import com.apec.framework.common.vo.SensorsCommonVo;

/**
 * @author: liuc
 * @date: 2019/10/30
 * @description:
 **/
public class SensorsHolder {

    private static final ThreadLocal<SensorsCommonVo> sensors = new InheritableThreadLocal();

    public static SensorsCommonVo sensorsCommonVo(){
        SensorsCommonVo vo = sensors.get();
        if(vo == null){
            sensors.set(null);
        }
        return sensors.get();
    }

    public static void setSensors(SensorsCommonVo vo){
        if(vo == null){
            vo = new SensorsCommonVo();
        }
        sensors.set(vo);
    }

}
