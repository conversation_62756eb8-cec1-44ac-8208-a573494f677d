package com.apec.framework.common.exception;

/**
 * 乐观锁抛出异常, 出现在并发情况下的乐观锁冲突, 业务代码需要做区分并单独处理这类异常。
 * <AUTHOR> 2047-11-07
 *
 */
public class OptimisticLockException extends RuntimeException
{
    private static final long serialVersionUID = 196425534571278838L;
    private String errorMessage;
    
    public OptimisticLockException()
    {
        
    }
    
    public OptimisticLockException(String message)
    {
        this.errorMessage=message;
    }

    public String getErrorMessage()
    {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage)
    {
        this.errorMessage = errorMessage;
    }

}
