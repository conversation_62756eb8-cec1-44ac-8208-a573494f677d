package com.apec.framework.common.tools;

import java.security.DigestException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;

public class Sha1
{
    /**
     * SHA1 安全加密算法
     * @param maps 参数key-value map集合
     */
    public static String SHA1(Map<String, Object> maps) throws DigestException
    {
        // 获取信息摘要 - 参数字典排序后字符串
        String decrypt = getOrderByLexicographic(maps);
        try
        {
            String gelignite;
            MessageDigest md = MessageDigest.getInstance("SHA-1");
            // 对拼接后的字符串进行 sha1 加密
            byte[] digest = md.digest(decrypt.getBytes());
            gelignite = byteToStr(digest);
            return gelignite.toLowerCase();

        }
        catch (NoSuchAlgorithmException e)
        {
            e.printStackTrace();
            throw new DigestException("签名错误！");
        }
    }

    /**
     * 将字节数组转换为十六进制字符串
     * @param byteArray 字节数组
     */
    private static String byteToStr(byte[] byteArray)
    {
        StringBuilder strDigest = new StringBuilder();
        for (byte ba : byteArray) {
            strDigest.append(byteToHexStr(ba));
        }
        return strDigest.toString();
    }

    /**
     * 将字节转换为十六进制字符串
     * @param mByte 需要转换的字节
     */
    private static String byteToHexStr(byte mByte)
    {
        char[] Digit =
        {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};
        char[] tempArr = new char[2];
        tempArr[0] = Digit[(mByte >>> 4) & 0X0F];
        tempArr[1] = Digit[mByte & 0X0F];
        return new String(tempArr);
    }

    /** 
     * 获取参数的字典排序 
     * @param maps 参数key-value map集合 
     * @return String 排序后的字符串 
     */
    private static String getOrderByLexicographic(Map<String, Object> maps)
    {
        // System.out.println("开始排序:" + maps);
        return splitParams(lexicographicOrder(getParamsName(maps)), maps);
    }

    /** 
     * 获取参数名称 key 
     * @param maps 参数key-value map集合 
     */
    private static List<String> getParamsName(Map<String, Object> maps)
    {
        ArrayList<String> paramNames = new ArrayList<>();
        for(Map.Entry<String, Object> entry : maps.entrySet())
        {
            paramNames.add(entry.getKey());
        }
        return paramNames;
    }

    /** 
     * 参数名称按字典排序 
     * @param paramNames 参数名称List集合 
     * @return 排序后的参数名称List集合 
     */
    private static List<String> lexicographicOrder(List<String> paramNames)
    {
        Collections.sort(paramNames);
        // System.out.println("排序后的KEY:" + paramNames);
        return paramNames;
    }

    /** 
     * 拼接排序好的参数名称和参数值 
     * @param paramNames 排序后的参数名称集合 
     * @param maps 参数key-value map集合 
     * @return String 拼接后的字符串 
     */
    private static String splitParams(List<String> paramNames, Map<String, Object> maps)
    {
        StringBuilder paramStr = new StringBuilder();
        for(String paramName : paramNames)
        {
            paramStr.append(paramName).append("=");
            for(Map.Entry<String, Object> entry : maps.entrySet())
            {
                if(paramName.equals(entry.getKey()))
                {
                    paramStr.append(String.valueOf(entry.getValue())).append("&");
                }
            }
        }
        return paramStr.toString().substring(0, paramStr.toString().length() - 1);
    }

    // 测试用
    public static void main1(String[] args)
    {
        String noncestr = "Wm3WZYTPz0wzccnW";
        String jsapi_ticket = "sM4AOVdWfPE4DxkXGEs8VMCPGGVi4C3VM0P37wVUCFvkVAy_90u5h9nbSlYy3-Sl-HhTdfl2fzFy1AOcHKP7qg";
        String timestamp = "1414587457";
        String url = "http://mp.weixin.qq.com?params=value";
        Map<String, Object> map = new HashMap<>();
        map.put("jsapi_ticket", jsapi_ticket);
        map.put("url", url);
        map.put("timestamp", timestamp);
        map.put("noncestr", noncestr);
        String key = "";
        try
        {
            key = Sha1.SHA1(map);
        }
        catch (DigestException e)
        {
            e.printStackTrace();
        }
        System.out.println(key);
    }

}
