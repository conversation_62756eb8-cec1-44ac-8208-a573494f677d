/**
 * 版权所有：版权所有(C) 2016，中农网
 * 文件编号：BL_PU6020301_Constants
 * 文件名称：Constants.java
 * 系统编号：
 * 系统名称：
 * 组件编号：ICT_CJ002
 * 组件名称：
 * 设计作者：
 * 完成日期：2016-07-14
 * 设计文档：
 * 内容摘要：框架常量定义
 */
package com.apec.framework.common.constant;

/**
 * 类 编 号：BL_PU1010202_PageJSON
 * 类 名 称：FrameConsts
 * 内容摘要：框架常量定义
 * 完成日期：2016-07-14
 * 编码作者：
 */
public interface FrameConsts
{

    /**
     * 机器ID
     */
    String WORKER_ID = "workerId";

    String MAX_CONNECTIONS_PERHOST = "maxConnectionsPerHost";

    String MAX_TOTAL_CONNECTIONS = "maxTotalConnections";

    String SYSTEM_GENERATOR = "system_generator ";

    String IDENTITY = "identity";

    String FORM_JSON = "formJSON";
    /**
     * 外部请求的头信息
     */
    String FORM_HEADER = "formHeader";
    /**
     *  自定义ID
     */
    String ASSIGNED = "assigned";

    String UUID = "uuid";

    /**
     * 客户编号
     */
    String CUSTOMER_ID = "customerId";

    /**
     * 角色搜索限定
     */
    String ROLE_SEARCH_LIMIT = "limit";

    String USER_ID = "userId";

    String SUCCESS = "success";

    String FAIL = "fail";

    String SESSION_ID = "sessionId";

    String TOKEN = "token";

    String X_AUTH_TOKEN = "x-auth-token";

    /**
     * 用户编号
     */
    String USER_NO = "userNo";

    /**
     * 图片属性集合
     */
    String IMAGE_ITEMS = "imageItems";

    /**
     * 页面传过来的缩略图的长,宽
     */
    String TINY = "tiny";

    /**
     * 客户编号
     */
    String CUSTOMER_NO = "CU";

    /**
     *客户联系人
     */
    String CONTACT = "CO";

    /**
     * 物流
     */
    String LOGISTICS_NO = "YD";

    /**
     * 物流明细
     */
    String LOGISTICS_DETAIL_NO = "LD";

    String USER_INFO_NO = "UI";

    String USER_INFO = "userInfo";

    String USER_DETAIL_NO = "UT";

    String USER_DEPARTMENT_NO = "UD";

    String CLIENT_TYPE_PARAM = "source-type";

    String ANDROID = "android";

    String WEIXIN = "weixin";

    /** 角色 */
    String REQUEST_HEADER_PARAM_ROLETYPE = "role-type";
    /** 角色类型：客户 custom */
    String ROLETYPE_CS = "cs";
    /** 角色类型：管理 Management */
    String ROLETYPE_MANAGEMENT = "mgt";

    String ROLE_IDS="roleIds";

    String UA = "UA";

    String IMEI = "IMEI";

    String ID = "id";

    String WEB = "web";
    String APP = "app";

    String IOS = "ios";

    String QUESTION_MARK = "?";

    String COMMA = ",";

    String UNDERLINE = "_";

    String POINT = ".";

    String HTTP_COLON = "http:";

    String DOUBLE_SLASH = "//";

    String SINGLE_SLASH = "/";
    
    /*------------iweb中分页定义开始--------------*/

    /**
     * 默认每页显示记录数
     */
    int DEFAULT_FETCH_SIZE = 10;

    /**
     *  默认起始页
     */
    int DEFAULT_START_PAGE = 1;

    /**
     * 默认每页显示记录数
     */
    String DEFAULT_FETCHSIZE = "10";

    /**
     * 默认起始页
     */
    String DEFAULT_RANGESTART = "1";

    String STRING_ZERO = "0";

    String STRING_ONE = "1";

    String STRING_TWO = "2";

    String STRING_THREE = "3";

    String STRING_FOUR = "4";

    /*------------iweb中分页定义结束------------*/

    /**
     * 客户端重复提交参数
     */
    String CLIENT_DUPLICATE_ACT_PARAM = "repeat-token";

    /**
     * 重复提交键的前缀
     */
    String PREFIX_REPEAT = "repeat_";

    String REPEAT_UUID = "repeat_uuid";

    String REPEAT_KEY = "repeatKey";

    /**
     * 登陆服务名
     */
    String LOGIN_SERVER = "LOGIN-SERVICE";

    /**
     * 登陆方法，参数：LogonAccount、LogonPassword
     */
    String METHOD_DOLOGIN = "doLogin";

    /**
     * 校验登陆状态，参数：userNo、token
     */
    String METHOD_VALIDATATOKEN = "validateToken";

    String PREFIX_TOKEN_USERNO = "token_userNo_";

    String PREFIX_TOKEN = "token_";

    String PREFIX_SESSIONID = "sessionId_";

    /*解决魔法数子*/

    int NUMBER_FIVE_HUNDRED = 500;

    /*恒生session校验 start*/
    String SESSION_VALIDATE_TYPE = "DZ";

    String DZ_SESSION_VALIDATE_MS = "WEB_MS";

    String DZ_SESSION_VALIDATE_WS = "WEB_WS";

    String DZ_SESSION_VALIDATE_APP = "APP";

    String CLIENT_UA_INFO = "clientUAInfo";

    String UC_SESSION_ID = "r_userAgent_sessionId";

    String UC_ID = "r_userAgent_id";

    String COOKIE_VALID_TOKEN = "_v_t_";

    String UCWEB_SUB_CODE = "uc";

    String SESSION_PRESSIOM_CODE = "code";

    String SUC_SID = "sucsid";
    /*恒生session校验 end*/

    /*恒生session校验 start*/
     String MAGPIE_SESSION_VALIDATE="magpie";
    /*恒生session校验 end*/

    String SYSTEM_TYPE="system-type";

    String USER_MARKET="marketIds";

    String ACCOUNT_INFO = "accountInfo";
    String LOGIN_USER_TYPE= "loginUserType";
    String LOGIN_CLIENT_ID= "currentClientId";

    /** 神策 start */
    String PLATFORM_TYPE = "platform_type";
    String APP_NAME = "app_name";
    String PRODUCT_LINE = "product_line";
    /** 神策 end */

    /**
     * 上传码
     */
    String UPLOAD_CODE = "upload-code";

    /**
     * 系统编码
     */
    String UPLOAD_SYS_CODE="sysCode";
    /**
     * 模块编码
     */
    String UPLOAD_MODULE_CODE="moduleCode";
    /**
     * 方法编码
     */
    String UPLOAD_METHOD_CODE="methodCode";


    /**
     * 方法地址
     */
    String UPLOAD_MICRO_METHOD_PATH="microMethodPath";
    String UPLOAD_MICRO_SERVICE_NAME="microServiceName";
}
