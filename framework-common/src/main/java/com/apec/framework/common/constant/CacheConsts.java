package com.apec.framework.common.constant;

/**
 * 类 编 号：
 * 类 名 称：CacheConstants
 * 内容摘要：缓存常量定义
 * 创建日期：2017/10/9
 * 编码作者：hcl
 */
public interface CacheConsts
{

    String CACHE_USERINFO_PREFIX = "userInfo_";

    String CACHE_USERINFO_ITEM = CACHE_USERINFO_PREFIX + "item";

    public static final String SESSION_LOGGED_IN_USER_ID = "session_logged_in_user_id";

    /**
     * 缓存管道（做redis发布，订阅，pattern匹配）
     */
    String CACHE_SALESSTOCK_PATTERN="__key*__:cache_vmstock_*";
    String CACHE_DISPATCH_WHITE_IP_URL = "cache_dispatch_white_ip_url";
    String CACHE_DISPATCH_URI_WHITE_IP_PREFIX = "cache_dispatch_url_white_ip_";
}
