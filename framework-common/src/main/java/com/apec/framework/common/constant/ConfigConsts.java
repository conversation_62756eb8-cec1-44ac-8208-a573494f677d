package com.apec.framework.common.constant;

import java.nio.charset.Charset;

/**
 * 类 编 号：
 * 类 名 称：ConfigConsts
 * 内容摘要：系统配置相关常量定义
 * 创建日期：2017/10/9
 * 编码作者：hcl
 */
public interface ConfigConsts
{

    String XML_HEAD = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>";

    String DATE_PATTERN = "yyyy-MM-dd HH:mm:ss";

    String DATE_PATTERN_MM_DD_HH_MM = "MM-dd HH:mm";

    String DATE_PATTERN_YYYY_MM_DD = "yyyy-MM-dd";

    String DATE_PATTERN_YYYY_MM_DD_00 = "yyyy-MM-dd 00:00:00";

    String DATE_PATTERN_YYYY_MM_DD_23 = "yyyy-MM-dd 23:59:59";

    String DEFAULT_LANG = "zh-CN";

    String EN_DEFAULT_LANG = "en-US";

    String POST = "POST";

    String GET = "GET";

    String HTTP_COLON = "http:";

    String APPLICATION_JSON = "application/json";

    String APPLICATION_JSON_CHARSET = "application/json;charset=UTF-8";

    String SYSTEM_ENCODING = "UTF-8";

    Charset SYSTEM_CHARSET = Charset.forName(ConfigConsts.SYSTEM_ENCODING);

    String DOUBLE_SLASH = "//";

    String SINGLE_SLASH = "/";

    String JPA_CITY_REGEX = "";
}
