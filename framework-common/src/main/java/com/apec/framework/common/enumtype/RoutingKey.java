package com.apec.framework.common.enumtype;

public enum Routing<PERSON><PERSON> implements <PERSON>q<PERSON><PERSON><PERSON><PERSON><PERSON>
{
    TEST( "TEST" ), TEST_FOO( "TEST_FOO" ), TEST_BAR( "TEST_BAR" ), 
    /**
     * 点对点消息
     */
    ORDE_TO_LGS( "ORDE_TO_LGS" ), 
    LGS_SEND_TO_SORTING("LGS_SEND_TO_SORTING" ), 
    LGS_SEND_TO_WMS("LGS_SEND_TO_WMS" ),
    LGS_RMA_TO_WMS("LGS_RMA_TO_WMS" ),
    LOGISTICS_TO_PAY( "LOGISTICS_TO_PAY" ),
    LGS_SEND_TO_ORDER_2("LGS_SEND_TO_ORDER_2" ),
    SORTING_TO_LGS( "SORTING_TO_LGS" ),
    PAY_TO_LGS_STATUS("PAY_TO_LGS_STATUS" ), 
    PAY_TO_LGS_THIRDNO( "PAY_TO_LGS_THIRDNO" ),
    SORTING_TO_LGS2( "SORTING_TO_LGS2" ),
    COUPON_OWN_USERS("COUPON_OWN_USERS"),
    PRESALE_OWN_USERS("PRESALE_OWN_USERS"),
    PRESALE_RETURN_DEPOSIT("PRESALE_RETURN_DEPOSIT"),
    PAY_SEND_TO_ORDER("PAY_SEND_TO_ORDER"),
    WEB_TO_LOG( "WEB_TO_LOG" ), 
    GOODS_TO_SHOPCART( "GOODS_TO_SHOPCART" ),
    NEW_CUSTOMER_TO_RELATION("NEW_CUSTOMER_TO_RELATION"), 
    ORDER_ANALYSIS_TO_NEXUS( "ORDER_ANALYSIS_TO_NEXUS" ), 
    ORDER_STATUS_CANCEL("order.status.cancel"),
    CANCEL_ORDER_TO_BD("CANCEL_ORDER_TO_BD"),
    CESEN_TEST("CESEN_TEST"),
    SEND_TO_MESSAGE("SEND_TO_MESSAGE"),
    PURCHASE_TO_WMS("PURCHASE_TO_WMS"),
    BILL_TO_WMS("BILL_TO_WMS"),
    PURCHASE_TO_BILL("PURCHASE_TO_BILL"),
    CHANGE_RELATION("CHANGE_RELATION"),
	NEW_CUSTOMER_ADD("NEW_CUSTOMER_ADD"),
	CONTRACT_TO_ENTRUST("CONTRACT_TO_ENTRUST"),
	ORDER_TO_CONTRACT("ORDER_TO_CONTRACT_S"),
	CONTRACT_TO_ENTRUST_REMOVE("CONTRACT_TO_ENTRUST_REMOVE"),
    PURCHASE_RMA_TO_WMS("PURCHASE_RMA_TO_WMS"),
	CESEN_TO_STATIS_ORDER("CESEN_TO_STATIS_ORDER"),
    CESEN_TO_GOODS_QUOTED("CESEN_TO_GOODS_QUOTED"),
    CESEN_TO_GOODS_FOCUS("CESEN_TO_GOODS_FOCUS"),
    WAREHOUSE_TO_SALESSTOCK("WAREHOUSE_TO_SALESSTOCK"),
    PSI_SPU_NEW("PSI_SPU_NEW"),
    PSI_SPU_DELETE("PSI_SPU_DELETE"),
    WEB_GOODS_NEW("WEB_GOODS_NEW"),
    WEB_GOODS_DELETE("WEB_GOODS_DELETE"),
    NEW_CUSTOMER_SEND_COUPON("NEW_CUSTOMER_SEND_COUPON"),
    HHJ_ROLE_LGS_THIRD_MANAGER("HHJ_ROLE_LGS_THIRD_MANAGER"),
    WAVE_TO_LOGISTICS("WAVE_TO_LOGISTICS"),
    SALESSTOCK_SEND_STOCK_TO_GOODS("SALESSTOCK_SEND_STOCK_TO_GOODS"),
	WMS_TO_ALLOCATION_STOCK("WMS_TO_ALLOCATION_STOCK"),
	LGS_SIGN_TO_ALLOCATION_STOCK("LGS_SIGN_TO_ALLOCATION_STOCK"),
	ORDER_SAVE_TO_ALLOCATION("ORDER_SAVE_TO_ALLOCATION"),
	ORDER_CANCAL_TO_ALLOCATION("ORDER_CANCAL_TO_ALLOCATION"),
	ORDER_AUDIT_TO_ALLOCATION("ORDER_AUDIT_TO_ALLOCATION"),
	ORDER_SPLIT_TO_ALLOCATION("ORDER_SPLIT_TO_ALLOCATION"),
	SEND_TO_ALLOCATION_STOCK("SEND_TO_ALLOCATION_STOCK"),
    ORDER_ANTI_CHECK_TO_ALLOCATION("ORDER_ANTI_CHECK_TO_ALLOCATION");
	
    RoutingKey(String name)
    {
        this.name = name;
    }

    private final String name;

    public final String getName()
    {
        return name;
    }

    @Override
    public String getKeyName()
    {
        return name;
    }
}
