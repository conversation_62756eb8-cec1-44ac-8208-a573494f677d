package com.apec.framework.common.enumtype;

/**
 * 类 编 号：
 * 类 名 称：
 * 内容摘要：
 * 创建日期：2017/11/9 18:05
 * <AUTHOR>
 */
public enum DefaultMqExchange implements MqExchange
{
    /**
     * 默认的广播exchange
     */
    FANOUT_EXCHAGE("amq.fanout"),
    /**
     * 默认的Topic exchange
     */
    TOPIC_EXCHAGE("amq.topic"),
    /**
     * 默认的Direct exchange
     */
    DIRECT_EXCHAGE("amq.direct"),
    /**
     * 默认的Direct 空 exchange
     */
    DIRECT_BLANK_EXCHAGE("")
    ;

    private String name;

    DefaultMqExchange(String name)
    {
        this.name = name;
    }

    public String getExchangeName(){
        return name;
    }
}
