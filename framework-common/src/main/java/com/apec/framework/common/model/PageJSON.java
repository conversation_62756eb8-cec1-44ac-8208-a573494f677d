package com.apec.framework.common.model;

import lombok.Data;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.Map;

/**
 * 类 编 号：
 * 类 名 称：PageJSON
 * 内容摘要：封装页面过来的JSON
 * 完成日期：
 * 编码作者：
 */
@Data
public class PageJSON<T> implements Serializable
{
    private static final long serialVersionUID = 7672548110652952249L;

    @Valid
    private T formJSON = null;

    private Map<String, String> requestParameterMap;// 请求参数

    private Map<String, Object> requestAttrMap;// 请求属性

}
