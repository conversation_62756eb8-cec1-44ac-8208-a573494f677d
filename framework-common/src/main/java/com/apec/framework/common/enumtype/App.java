package com.apec.framework.common.enumtype;

import org.apache.commons.lang3.StringUtils;

/**
 * @author: liuc
 * @date: 2019/10/30
 * @description:
 **/
public enum App {

    SLT("丝路通"),
    ZNXY("中农小易");

    private String name;

    public String getName() {
        return name;
    }

    App(String name) {
        this.name = name;
    }

    public static String appName(String nameEn){
        if(StringUtils.isBlank(nameEn)){
            return null;
        }
        App apps[] = App.values();
        for(int i = 0; i != apps.length ; ++i){
            if(apps[i].name().equals(nameEn)){
                return apps[i].getName();
            }
        }
        return null;
    }
}
