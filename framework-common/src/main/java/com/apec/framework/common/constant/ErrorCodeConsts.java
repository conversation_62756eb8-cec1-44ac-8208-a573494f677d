package com.apec.framework.common.constant;

/**
 * 类 编 号：
 * 类 名 称：ErrorCodeConsts
 * 内容摘要：错误码常量定义
 * 创建日期：2016/11/22
 * 编码作者：huangcl
 */
public interface ErrorCodeConsts
{
    /** -----通用定义开始(从100001到100999)------*/
    /**通用成功标识**/
    String RETURN_SUCESS = "100001";

    /**系統异常**/
    String SYS_ERROR = "100002";

    /**参数不能为空**/
    String ERROR_100003 = "100003";

    /**数据执行错误**/
    String ERROR_100004 = "100004";

    /**服务调用服务异常**/
    String SERVER_RESEST_EXCEPTION = "100061";

    /**服务调用返回结果集为空**/
    String SERVER_RESEST_RESULT_IS_NULL= "100069";
    /**  */
    String ENABLE_NOT_NULL = "100011";

    /**主键不能为空**/
    String ID_NOTNULL = "100012";

    /**通过id查询数据不存在**/
    String ERROR_QUERY_DATA_NOT_EXIST_BY_ID = "100013";

    String USER_NOTNULL = "100005";

    String REPEAT_DO_ERROR = "100006";

    /**数据类型匹配异常错误码**/
    String ERROR_CODE_DATA_TYPE_NO_MATCH = "100009";

    /**时间类型匹配异常错误码**/
    String DATE_TYPE_NO_MATCH = "100010";

    /**参数不完整**/
    String COMMON_MISSING_PARAMS = "100021";

    /**参数格式错误**/
    String COMMON_ERROR_FORMAT_PARAMS = "100022";
    /**该请求{0}未在限定IP源,请咨询系统管理员**/
    String COMMON_ERROR_RUI_NOT_IN_WHITE_IP = "100023";

    /**账号密码错误**/
    String PASSWORD_ERROR = "200001";

    // ======================DAO操作错误码start======================

    String DAO_EXCEPTION = "100030";

    String DAO_SAVE_EXCEPTION = "100031";

    String DAO_DELETE_EXCEPTION = "100032";

    String DAO_UPDATE_EXCEPTION = "100033";

    String DAO_GET_EXCEPTION = "100034";

    String SAVE_EXCEPTION = "100041";

    String DELETE_EXCEPTION = "100042";

    String UPDATE_EXCEPTION = "100043";

    String GET_EXCEPTION = "100044";

    String PROCEDURE_EXCEPTION = "100045";

    // ======================DAO操作错误码end======================

    // ======================缓存错误码start======================

    String CACHE_EXCEPTION = "100050";

    String CACHE_SAVE_EXCEPTION = "100051";

    String CACHE_DELETE_EXCEPTION = "100052";

    String CACHE_UPDATE_EXCEPTION = "100053";

    String CACHE_GET_EXCEPTION = "100054";
    // ======================缓存错误码end======================

    // ======================文件上传错误码start======================

    String ERROR_FTP_LOGIN_FAILD = "600007";

    String ERROR_FTP_FILETYPE_FAILD = "600008";

    String ERROR_FTP_WORKINGDIR_FAILD = "600009";

    String ERROR_FTP_UPLOAD_FAILD = "600010";

    String ERROR_FTP_DOWNLOAD_FAILD = "600011";

    String ERROR_FTP_CONNECT_FAILD = "600006";
    /**
     * 上传文件失败,缺少参数{0}
     */
    String ERROR_UPLOAD_NFS_FAILD_LOAST_PARAM = "600013";

    // ======================文件上传错误码end======================

    // ======================dispatch错误码start======================

    /**session超时**/
    String ERROR_600001 = "600001";

    /**没有选择上传的图片**/
    String ERROR_600002 = "600002";

    /**文件上传失败**/
    String ERROR_600003 = "600003";

    /**文件上传失败**/
    String ERROR_600004 = "600004";

    String ERROR_X_AUTH_TOKEN = "600012";

    /**服务请求异常**/
    String REQ_SERVER_EXCEPTION = "600005";
    // ======================dispatch错误码end======================

    // ======================lock错误码start======================

    String LOCK_EXCEPTION = "100060";

    String LOCK_EXISTS_EXCEPTION = "100061";

    String LOCK_NOT_HELD_EXCEPTION = "100062";

    String NO_SUCK_LOCK_EXCEPTION = "100063";
    // ======================lock错误码end======================

    //======================hs Session校验 start===========================
    String HS_SESSION_ABATE = "-1001";
    String HS_NOT_PEREONAL_SETTLEMENT_ACCOUNT = "-1011";
    String HS_PERMISSION_NO_POWER = "-1005";
    String HS_FUNCTION_NO_EXIST = "-1006";
    String HS_CACHE_GET_ERROR = "-1002";
    String HS_LACK_PARAM = "-1008";
    String HS_TRADE_NO_POWER = "-1009";
    String HS_NOT_PEREONAL_FUND_ACCOUNT = "-1012";
    //======================hs Session校验 end===========================


    // ======================搜索索引错误码start======================

    String CREATE_INDEX_EXCEPTION = "100070";
    // ======================搜索索引错误码end======================
}
