
package com.apec.framework.common.enumtype;

import java.util.HashMap;
import java.util.Map;

public enum EnumFileUploadStatus {
    /**
     * 上传到服务器(网关)
     */
    UPLOAD_SERVER("uploadServer","上传到服务器")
    /**
     * 调用微服务(base-filemgt)
     */
    ,CALL_MICRO ("callMicro" , "调用微服务")
    /**
     * 微服务执行中(microServier)
     */
    ,MICRO_EXECING("microExecing" , "微服务执行中")
    /**
     * 微服务执行中(microServier)
     */
    ,MICRO_FINISH("microFinish" , "微服务执行中")
    /**
     * 上传完成(base-filemgt)
     */
    ,UPLOAD_SUCCESS("uploadSuccess" , "上传成功")
    /**
     * 上传失败(base-filemgt)
     */
    ,UPLOAD_FAIL("uploadFail" , "上传失败")
    ;

    private String code;
    private String name;

    private EnumFileUploadStatus(String code, String name) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public String getValue(){
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


    private static Map<String, EnumFileUploadStatus> pool = new HashMap<>();

    static {
        for (EnumFileUploadStatus e : EnumFileUploadStatus.values()) {
            pool.put(e.code, e);
        }
    }

    public static Map<String, String> getAllEnums() {
        Map<String, String> m = new HashMap<String, String>();
        for (EnumFileUploadStatus e : EnumFileUploadStatus.values()) {
            m.put(e.getCode(), e.getName());
        }
        return m;
    }

    public static EnumFileUploadStatus getByCode(String code) {
        for (EnumFileUploadStatus item : EnumFileUploadStatus.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

    public static String getByValue(String value) {
        for (EnumFileUploadStatus item : EnumFileUploadStatus.values()) {
            if (item.getName().equals(value)) {
                return item.getCode();
            }
        }
        return null;
    }
}
