package com.apec.framework.es.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 类 编 号：
 * 类 名 称：
 * 内容摘要：初始化信息
 * 创建日期：2018/9/28 14:03
 * <AUTHOR>
 */
//@Configuration
public class EsConfiguration
{
//    @Value("${etrali.indexname}")
//    private String indexName;
//
//    @Bean
//    public String indexName(){
//        return indexName;
//    }
}
