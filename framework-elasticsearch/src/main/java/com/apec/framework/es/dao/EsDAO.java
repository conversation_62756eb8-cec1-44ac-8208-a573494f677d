package com.apec.framework.es.dao;

import java.util.List;

import org.elasticsearch.index.query.BoolQueryBuilder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;

import com.apec.framework.es.dto.EsFields;
import com.apec.framework.es.dto.EsQueryDTO;
import com.apec.framework.es.dto.EsSort;
import com.apec.framework.es.model.EsBaseModel;

/**
 * 此为对Spring data elasticsearch 工具进行再包装通用DAO类
 * 保证该类方法中操作的实体类只能是EsBaseModel,不能使用其子类
 *
 * Created by KINGFS on 2017/8/28.
 * <AUTHOR>
 */
public interface EsDAO<T>
{
    /**
     * 删除索引
     * @param model 映射model实体
     * @return
     */
    boolean deleteIndex(EsBaseModel model);

    /**
     * 删除索引
     * @param esBaseModelClass
     * @return
     */
    boolean deleteIndex(Class<?> esBaseModelClass);

    /**
     * 创建索引然后进行映射
     * @param model 实体
     * @return
     */
    boolean addIndex(EsBaseModel model);

    /**
     * 创建索引然后进行映射
     * @param esBaseModelClass
     * @return
     */
    boolean addIndex(Class<?> esBaseModelClass);

    /**
     * 关键字查询
     * @param clazz
     * @param esQueryDTO
     * @param esFields
     * @param page
     * @param sorts
     * @param myQueryBuilder
     * @return
     */
    Page<T> searchString(Class<T> clazz, EsQueryDTO esQueryDTO, List<EsFields> esFields, Pageable page,
        List<EsSort> sorts, BoolQueryBuilder myQueryBuilder);

    /**
     * 所有条件都是and
     * @param esBaseModel
     * @param page
     * @param sorts
     * @param myQueryBuilder
     * @return
     */
    Page<T> searchForPage(EsBaseModel esBaseModel, Pageable page, List<EsSort> sorts, BoolQueryBuilder myQueryBuilder);

    /**
     * 条件查询
     * @param clazz
     * @param nativeSearchQueryBuilder
     * @return
     */
    Page<T> doSearch(Class<T> clazz, NativeSearchQueryBuilder nativeSearchQueryBuilder);

    /**
     * 不分页条件查询
     * @param baseModel
     * @param sorts
     * @param myQueryBuilder
     * @return
     */
    List<T> searchList(EsBaseModel baseModel, List<EsSort> sorts, BoolQueryBuilder myQueryBuilder);
}
