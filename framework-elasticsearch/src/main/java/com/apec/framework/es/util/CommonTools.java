/*
 *
 *  *
 *  *   @copyright      Copyright © 2017 . [深圳市中农易讯信息技术有限公司] All rights reserved.
 *  *   @project        Goods-Module
 *  *   <AUTHOR>
 *  *   @date           17-8-15 上午11:11
 *  *
 *  *   @lastModifie  17-8-15 上午11:10
 *  *
 *
 *
 */

package com.apec.framework.es.util;

import java.util.Collection;
import java.util.Date;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.apec.framework.common.util.BeanUtils;
import com.apec.framework.common.util.DateUtils;

/**

 * <p>类名称： CommonTools.class</p>
 * <p>内容摘要：判断字符串，集合，map是否为空的工具类</p>
 * <p>完成日期：2017/05/24 16:41</p>
 * <p>编码作者：warne</p>
 * <AUTHOR>
 */
public abstract class CommonTools
{

    /**
     * //# check str whether is null or length=0
     *
     * @param str
     */
    public static boolean isEmpty(String str)
    {
        return str == null || str.length() == 0;
    }

    /**
     * //# check str whether is not null and length !=0
     *
     * @param str
     */
    public static boolean isNotEmpty(String str)
    {
        return !StringUtils.isEmpty(str);
    }

    /**
     * //# check str whether is null or length !=0 or include space
     *
     * @param str
     */
    public static boolean isBlank(String str)
    {
        int strLen;
        if(str == null || (strLen = str.length()) == 0)
        {
            return true;
        }
        for(int i = 0; i < strLen; i++)
        {
            if((Character.isWhitespace(str.charAt(i)) == false))
            {
                return false;
            }
        }
        return true;
    }

    /**
     * //# check str whether is null and length !=0 and include space
     *
     * @param str
     */
    public static boolean isNotBlank(String str)
    {
        return !isBlank(str);
    }

    /**
     * //# generic, judge an array whether is null or its length=0
     *
     * @param <T>
     * @param array
     * @return boolean
     */
    public static <T> boolean isEmpty(T[] array)
    {
        return array == null || array.length == 0;
    }

    /**
     * //# generic, judge an array whether is not null and its length !=0
     *
     * @param <T>
     * @param array
     * @return boolean
     */
    public static <T> boolean isNotEmpty(T[] array)
    {
        return !isEmpty(array);
    }

    /**
     * //# judge a collection whether is null or empty
     *
     * @param collection
     * @return boolean
     */
    public static boolean isEmpty(Collection<?> collection)
    {
        return collection == null || collection.isEmpty();
    }

    /**
     * //# judge a collection whether is not null and non-null
     *
     * @param collection
     * @return boolean
     */
    public static boolean isNotEmpty(Collection<?> collection)
    {
        return !isEmpty(collection);
    }

    /**
     * //# generic, judge an map whether is not null and its length !=0
     *
     * @param map
     * @return boolean
     */
    public static boolean isEmpty(Map<?, ?> map)
    {
        return map == null || map.isEmpty();
    }

    /**
     * //# judge a map whether is not null and non-null
     *
     * @param map
     * @return boolean
     */
    public static boolean isNotEmpty(Map<?, ?> map)
    {
        return !isEmpty(map);
    }

    /**
     * bean copy
     * @param target
     * @param source
     */
    public static void copy(Object target, Object source)
    {
        if(null == target)
        {
            return;
        }
        BeanUtils.copyPropertiesIgnoreNullFilds(source, target);
    }

    public final static String EMPTY_STR = "";

    /**
     *  //# convert to java.uitl.date
     *
     * @param obj
     * @return
     */
    public static Date toDate(Object obj)
    {
        if(obj == null)
        {
            // null默认返回值
            return null;
        }
        if(obj instanceof Date)
        {
            return (Date)obj;
        }

        if(obj instanceof String)
        {
            return DateUtils.string2Date((String)obj);
        }

        // failed
        return null;
    }

    /**
     *  //# convert to int
     *
     * @param obj
     * @return
     */
    public static int toInt(Object obj)
    {
        return toInt(obj, 0);
    }

    /**
     * //# convert to int, include defalut value
     *
     * @param obj
     * @param defaultValue
     * @return
     */
    public static int toInt(Object obj, int defaultValue)
    {
        if(obj == null)
        {
            return defaultValue;
        }
        if(obj instanceof Number)
        {
            return ((Number)obj).intValue();
        }
        return NumberUtils.toInt(obj.toString(), defaultValue);
    }

    /**
     * //# convert to long
     *
     * @param obj
     * @return
     */
    public static long toLong(Object obj)
    {
        return toLong(obj, 0);
    }

    /**
     * //# convert to long, include defalut value
     *
     * @param obj
     * @param defaultValue
     * @return
     */
    public static long toLong(Object obj, long defaultValue)
    {
        if(obj == null)
        {
            return defaultValue;
        }
        if(obj instanceof Number)
        {
            return ((Number)obj).longValue();
        }
        return NumberUtils.toLong(obj.toString(), defaultValue);
    }

    /**
     * //# convert to double
     *
     * @param obj
     * @return
     */
    public static double toDouble(Object obj)
    {
        return toDouble(obj, 0);
    }

    /**
     * //# convert to Double,include defalut value
     *
     * @param obj
     * @param defaultValue
     * @return
     */
    public static double toDouble(Object obj, double defaultValue)
    {
        if(obj == null)
        {
            return defaultValue;
        }
        if(obj instanceof Number)
        {
            return ((Number)obj).doubleValue();
        }
        return NumberUtils.toDouble(obj.toString(), defaultValue);
    }

    /**
     * //# convert to String
     *
     * @param obj
     * @return
     */
    public static String toString(Object obj)
    {
        return toString(obj, EMPTY_STR);
    }

    /**
     * //# convert to String, include defalut value
     *
     * @param obj
     * @param defaultValue
     * @return
     */
    public static String toString(Object obj, String defaultValue)
    {
        if(obj == null)
        {
            return defaultValue;
        }

        String value = null;
        if(obj instanceof String)
        {
            value = (String)obj;
        }
        else if(obj instanceof Date)
        {
            value = DateUtils.formatDate((Date)obj, DateUtils.FORMAT_DATE_TIME);
        }
        else
        {
            value = obj.toString();
        }

        return value == null ? defaultValue : value;
    }

    /**
     * 下划线转化为驼峰式
     * @param destStr
     * @return
     */
    public static String line2CamelName(String destStr)
    {
        StringBuilder result = new StringBuilder();
        final String specKey = "_";
        if(isEmpty(destStr))
        {
            return "";
        }
        else if(!destStr.contains(specKey))
        {// 不含下划线，仅将首字母小写
            return destStr.toLowerCase();
        }
        // 用下划线将原始字符串分割
        String[] camels = destStr.split(specKey);
        for(String camel : camels)
        {
            // 跳过原始字符串中开头、结尾的下换线或双重下划线
            if(camel.isEmpty())
            {
                continue;
            }

            // 处理真正的驼峰片段
            if(result.length() == 0)
            {
                // 第一个驼峰片段，全部字母都小写
                result.append(camel.toLowerCase());
            }
            else
            {
                // 其他的驼峰片段，首字母大写
                result.append(camel.substring(0, 1).toUpperCase());
                result.append(camel.substring(1).toLowerCase());
            }
        }
        return result.toString();
    }

    protected static final Logger logger = LoggerFactory.getLogger(CommonTools.class);
}

