package com.apec.framework.es.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Created by KINGFS on 2017/9/14.
 * 注解：自定义注解
 * 作用：为ES的Model的字段配置权重boost，boost默认为1，boost值越大权重越大
 * <AUTHOR>
 */

@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD})
@Documented
@Inherited
public @interface FieldBoost
{
    float boost() default 1;
}
