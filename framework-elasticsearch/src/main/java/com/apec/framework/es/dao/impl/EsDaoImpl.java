package com.apec.framework.es.dao.impl;


import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.QueryStringQueryBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.SearchQuery;
import org.springframework.stereotype.Repository;

import java.util.List;

import com.apec.framework.es.common.EsConstant;
import com.apec.framework.es.dao.EsDAO;
import com.apec.framework.es.dto.EsFields;
import com.apec.framework.es.dto.EsQueryDTO;
import com.apec.framework.es.dto.EsSort;
import com.apec.framework.es.model.EsBaseModel;
import com.apec.framework.es.util.CommonTools;
import com.apec.framework.es.util.QueryBuilderUtil;

/**
 * 此为对Spring data elasticsearch 工具进行再包装通用DAO类
 * 保证该类方法中操作的实体类只能是EsBaseModel,不能使用其子类
 *
 * Created by KINGFS on 2017/8/28.
 * <AUTHOR>
 */
@Repository
public class EsDaoImpl implements EsDAO<T>
{
    @Autowired
    private ElasticsearchTemplate elasticsearchTemplate;

    private final static Logger LOGGER = LoggerFactory.getLogger(EsDaoImpl.class);

    /**
     * 删除索引
     * @param model 映射model实体
     * @return 删除结果
     */
    @Override
    public boolean deleteIndex(EsBaseModel model)
    {
        Class<? extends EsBaseModel> clz = model.getClass();

        return deleteIndex(clz);
    }

    @Override
    public boolean deleteIndex(Class<?> esBaseModelClass)
    {
        Document annoDoc = esBaseModelClass.getAnnotation(Document.class);
        String indexName = annoDoc.indexName();
        String type = annoDoc.type();
        LOGGER.info("index:{} type:{}, 执行删除!", indexName, type);
        return elasticsearchTemplate.deleteIndex(esBaseModelClass);
    }

    /**
     * 创建索引然后进行映射
     * @param model 实体
     * @return 新增结果
     */
    @Override
    public boolean addIndex(EsBaseModel model)
    {
        Class<? extends EsBaseModel> clz = model.getClass();
        return addIndex(clz);
    }

    @Override
    public boolean addIndex(Class<?> esBaseModelClass)
    {
        boolean success = false;
        if(elasticsearchTemplate.indexExists(esBaseModelClass))
        {
            Document annoDoc = esBaseModelClass.getAnnotation(Document.class);
            String indexName = annoDoc.indexName();
            String type = annoDoc.type();
            LOGGER.warn("index:{} type:{}, 已经存在, 如需覆盖原索引, 请先进行删除操作!", indexName, type);
            return false;
        }
        elasticsearchTemplate.createIndex(esBaseModelClass);
        success = elasticsearchTemplate.putMapping(esBaseModelClass);
        elasticsearchTemplate.refresh(esBaseModelClass);
        return success;
    }

    /**
     * 关键字查询
     * @param esQueryDTO 查询体
     * @param page 分页
     * @param sorts 排序
     * @param clzz model反射
     * @return 分页结果
     */
    @Override
    public Page<T> searchString(Class<T> clzz, EsQueryDTO esQueryDTO, List<EsFields> esFields, Pageable page, List<EsSort> sorts, BoolQueryBuilder myQueryBuilder)
    {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        if(null != myQueryBuilder && myQueryBuilder.hasClauses())
        {
            boolQuery.must(myQueryBuilder);
        }
        if(StringUtils.isNotEmpty(esQueryDTO.getQueryString()))
        {
            QueryStringQueryBuilder stringQuery = QueryBuilders.queryStringQuery(esQueryDTO.getQueryString())
                //                .analyzer(EsConstant.IK)  // 加入指定分词器后搜索结果有误
                .quoteAnalyzer(EsConstant.IK)
                .defaultOperator(QueryStringQueryBuilder.Operator.OR);

            if(CommonTools.isNotEmpty(esFields))
            {
                esFields.forEach(v -> stringQuery.field(v.getField(), v.getBoost()));
            }

            boolQuery.must(stringQuery.useDisMax(true));
        }

        //添加分页、排序
        NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder().withPageable(page).withQuery(boolQuery);
        QueryBuilderUtil.addSorts(nativeSearchQueryBuilder, sorts);
        return this.doSearch(clzz, nativeSearchQueryBuilder);

    }

    /**
     * 所有条件and
     * @param page
     * @param sorts
     * @return
     */
    @Override
    public Page<T> searchForPage(EsBaseModel baseModel, Pageable page, List<EsSort> sorts, BoolQueryBuilder myQueryBuilder)
    {
        //创建检索条件表达式
        BoolQueryBuilder boolQuery = QueryBuilderUtil.getBoolQueryBuilder(baseModel, elasticsearchTemplate);
        if(null != myQueryBuilder && myQueryBuilder.hasClauses())
        {
            boolQuery.must(myQueryBuilder);
        }

        // 添加分页、排序
        NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder().withQuery(boolQuery).withPageable(page);
        QueryBuilderUtil.addSorts(nativeSearchQueryBuilder, sorts);
        return this.doSearch((Class)baseModel.getClass(), nativeSearchQueryBuilder);
    }

    /**
     * 所有条件and
     * @param clazz
     * @param nativeSearchQueryBuilder
     * @return
     */
    @Override
    public Page<T> doSearch(Class<T> clazz, NativeSearchQueryBuilder nativeSearchQueryBuilder)
    {
        SearchQuery searchQuery = nativeSearchQueryBuilder.build();
        LOGGER.info("\n//// 查询条件:\n{}\n//// 排序条件:\n{}",
                    null == searchQuery.getQuery() ? "无" : searchQuery.getQuery().toString(),
                    null == searchQuery.getSort() ? "无" : searchQuery.getSort().toString());

        return elasticsearchTemplate.queryForPage(searchQuery, clazz);
    }

    /**
     * 所有条件and
     * @param sorts
     * @return
     */
    @Override
    public List<T> searchList(EsBaseModel baseModel, List<EsSort> sorts, BoolQueryBuilder myQueryBuilder)
    {
        //创建检索条件表达式
        BoolQueryBuilder boolQuery = QueryBuilderUtil.getBoolQueryBuilder(baseModel, elasticsearchTemplate);
        if(null != myQueryBuilder && myQueryBuilder.hasClauses())
        {
            boolQuery.must(myQueryBuilder);
        }

        // 添加分页、排序
        Pageable pageAble = new PageRequest(0, 10000);
        NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder().withQuery(boolQuery).withPageable(pageAble);
        QueryBuilderUtil.addSorts(nativeSearchQueryBuilder, sorts);
        SearchQuery searchQuery = nativeSearchQueryBuilder.build();
        LOGGER.info("\n//// 查询条件:\n{}\n//// 排序条件:\n{}",
                    null == searchQuery.getQuery() ? "无" : searchQuery.getQuery().toString(),
                    null == searchQuery.getSort() ? "无" : searchQuery.getSort().toString());
        return elasticsearchTemplate.queryForPage(searchQuery, (Class)baseModel.getClass()).getContent();
    }
}
