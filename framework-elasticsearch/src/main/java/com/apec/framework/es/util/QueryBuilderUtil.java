package com.apec.framework.es.util;

import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.admin.indices.analyze.AnalyzeResponse;
import org.elasticsearch.client.IndicesAdminClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.NestedQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.elasticsearch.annotations.FieldIndex;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;

import com.apec.framework.common.constant.ErrorCodeConsts;
import com.apec.framework.common.exception.IndexServerException;
import com.apec.framework.es.common.EsConstant;
import com.apec.framework.es.dto.EsQueryDTO;
import com.apec.framework.es.dto.EsSort;
import com.apec.framework.es.model.EsBaseModel;

/**
 * 创建自定义QueryBuilder
 * Created by KINGFS on 2017/10/24.
 *
 * <AUTHOR>
 */
public abstract class QueryBuilderUtil
{
    /**
     * 时间范围查询
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static void addDateQuery(BoolQueryBuilder boolQuery, String dateName, final Date startDate, final Date endDate) {
        if (null != startDate || null != endDate) {
            if (StringUtils.isNotEmpty(dateName)) {
                boolQuery.must(QueryBuilders.rangeQuery(dateName)
                        .from(startDate)
                        .to(endDate));
            }
        }
    }

    /**
     * 最大最小价格范围查询
     *
     * @param maxPrice
     * @param minPrice
     * @return
     */
    public static void addNumberRangeQuery(BoolQueryBuilder boolQuery, String numberName, final BigDecimal maxPrice, final BigDecimal minPrice) {
        if (null != maxPrice || null != minPrice) {
            if (maxPrice.intValue() > 0 || minPrice.intValue() > 0) {
                if (StringUtils.isNotEmpty(numberName)) {
                    boolQuery.must(QueryBuilders.rangeQuery(numberName)
                            .from(minPrice)
                            .to(maxPrice));
                }
            }
        }
    }

    /**
     * 过滤商品ID
     *
     * @param boolQuery
     * @param field
     * @param value
     */
    public static void filterField(BoolQueryBuilder boolQuery, String field, String value) {
        if (StringUtils.isNotEmpty(field)) {
            boolQuery.mustNot(QueryBuilders.termQuery(field, value));
        }
    }

    /**
     * 添加排序
     *
     * @param queryBuilder
     * @param sorts
     */
    public static void addSorts(NativeSearchQueryBuilder queryBuilder, List<EsSort> sorts) {
        if (CommonTools.isNotEmpty(sorts)) {
            sorts.forEach(v -> queryBuilder.withSort(SortBuilders.fieldSort(v.getSort()).order(SortOrder.valueOf(v.getSortType()))));
        }
    }

    /**
     * 生成BoolQueryBuilder
     *
     * @return
     */
    public static BoolQueryBuilder getBoolQuery(EsQueryDTO dto) {
//        final String status = "status";
//        final String cityIds = "cityIds";
//        final String oecdId = "oecdId";
//        final String goodsCategoryId = "goodsCategoryId";
//        final String showClient = "showClient";
//        final String shopId = "shopId";
//        final String shopName = "shopName";
//        final String shopTag = "shopTag";
//        final String shopType = "shopType";
//
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
//        if (StringUtils.isNotEmpty(dto.getStatus())) {
//            boolQuery.must(QueryBuilders.termQuery(status, dto.getStatus()));
//        }
//
//        if (StringUtils.isNotEmpty(dto.getCityIds())) {
//            boolQuery.must(QueryBuilders.matchQuery(cityIds, dto.getCityIds()));
//        }
//
//        if (StringUtils.isNotEmpty(dto.getOecdId())) {
//            boolQuery.must(QueryBuilders.matchQuery(oecdId, dto.getOecdId()));
//        }
//
//        if (StringUtils.isNotEmpty(dto.getGoodsCategoryId())) {
//            boolQuery.must(QueryBuilders.matchQuery(goodsCategoryId, dto.getGoodsCategoryId()));
//        }
//
//        if (StringUtils.isNotEmpty(dto.getShowClient())) {
//            boolQuery.must(QueryBuilders.matchQuery(showClient, dto.getShowClient()));
//        }
//
//        if (StringUtils.isNotEmpty(dto.getShopId())) {
//            boolQuery.must(QueryBuilders.matchQuery(shopId, dto.getShopId()));
//        }
//
//        if (StringUtils.isNotEmpty(dto.getShopName())) {
//            boolQuery.must(QueryBuilders.matchQuery(shopName, dto.getShopName()));
//        }
//
//        if (StringUtils.isNotEmpty(dto.getShopTag())) {
//            boolQuery.must(QueryBuilders.matchQuery(shopTag, dto.getShopTag()));
//        }
//
//        if (StringUtils.isNotEmpty(dto.getShopType())) {
//            boolQuery.must(QueryBuilders.matchQuery(shopType, dto.getShopType()));
//        }

        return boolQuery;
    }

    /**
     * 解析获取查询表达式
     *
     * @param baseModel
     * @return
     */
    public static BoolQueryBuilder getBoolQueryBuilder(EsBaseModel baseModel, ElasticsearchTemplate elasticsearchTemplate) {
        return getBoolQueryBuilder(baseModel, null, elasticsearchTemplate);
    }

    /**
     * 解析获取查询表达式
     *
     * @param baseModel
     * @param path      nest路径,默认为null即可
     * @return
     */
    private static BoolQueryBuilder getBoolQueryBuilder(EsBaseModel baseModel, String path, ElasticsearchTemplate elasticsearchTemplate) {
        // 获取指定的分词属性
        Class<? extends EsBaseModel> clazz = baseModel.getClass();
        List<String> nestedFiles = EsUtil.getFilesByFieldType(clazz, FieldType.Nested);
        List<String> analyzedFiles = EsUtil.getFilesByFieldIndex(clazz, FieldIndex.analyzed);
        List<String> notAnalyzedFiles = EsUtil.getFilesByFieldIndex(clazz, FieldIndex.not_analyzed);
        analyzedFiles.addAll(notAnalyzedFiles);

        Object val;
        String name, valStr;
        List<AnalyzeResponse.AnalyzeToken> tokens;
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        IndicesAdminClient indices = elasticsearchTemplate.getClient().admin().indices();
        PropertyDescriptor[] propertyDescriptors = BeanUtils.getPropertyDescriptors(clazz);

        try {
            for (PropertyDescriptor p : propertyDescriptors) {
                name = p.getName();
                val = p.getReadMethod().invoke(baseModel);
                //过滤null以及new、class关键字属性
                if (null == val || EsConstant.NEW_STR.equals(name) || EsConstant.CLASS_STR.equals(name)) {
                    continue;
                }

                // 判断是否属于指定属性
                if (analyzedFiles.contains(name) && p.getPropertyType() == String.class) {
                    valStr = (String) val;
                    if (StringUtils.isBlank(valStr)) {
                        continue;
                    }

                    //条件检索时，需要获取ik分词结果字符集，用以查询
                    tokens = indices.prepareAnalyze(valStr).setAnalyzer(EsConstant.IK_SMART).setTokenizer(EsConstant.IK_SMART).execute().actionGet().getTokens();
                    for (AnalyzeResponse.AnalyzeToken t : tokens) {
                        boolQuery.must(QueryBuilders.matchQuery(StringUtils.isNotEmpty(path) ? path + EsConstant.POINT + name : name, t.getTerm()));
                    }
                } else if (nestedFiles.contains(name)) {
                    // LIST,创建nest字段查询
                    EsBaseModel nestModel;
                    NestedQueryBuilder nestedQueryBuilder;
                    List<EsBaseModel> models;
                    if (val instanceof EsBaseModel) {
                        nestModel = (EsBaseModel) val;
                        nestedQueryBuilder = new NestedQueryBuilder(name, getBoolQueryBuilder(nestModel, name, elasticsearchTemplate));
                        boolQuery.must(nestedQueryBuilder);
                    } else if ((val instanceof List)) {
                        models = (List<EsBaseModel>) val;
                        for (EsBaseModel model : models) {
                            nestedQueryBuilder = new NestedQueryBuilder(name, getBoolQueryBuilder(model, name, elasticsearchTemplate));
                            boolQuery.must(nestedQueryBuilder);
                        }
                    }
                }
            }
        } catch (RuntimeException e) {
            LOGGER.error("////  创建搜索条件出错",e);
            throw new IndexServerException(ErrorCodeConsts.CREATE_INDEX_EXCEPTION,e);
        } catch (IllegalAccessException e) {
            LOGGER.error("////  创建搜索条件时,参数格式转换出错",e);
            throw new IndexServerException(ErrorCodeConsts.CREATE_INDEX_EXCEPTION,e);
        } catch (InvocationTargetException e) {
            LOGGER.error("////  创建搜索条件时,执行set方法出错",e);
            throw new IndexServerException(ErrorCodeConsts.CREATE_INDEX_EXCEPTION,e);
        }

        return boolQuery;
    }

    protected static Logger LOGGER = LoggerFactory.getLogger(QueryBuilderUtil.class);
}
