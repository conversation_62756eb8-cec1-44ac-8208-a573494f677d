package com.apec.framework.es.dto;

import java.util.List;

import org.apache.lucene.queryparser.classic.QueryParser;

import com.apec.framework.common.dto.BaseDTO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * Created by KINGFS on 2017/8/17.
 * 关键字查询(对分词字段进行模糊查询)
 * <AUTHOR>
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
public class EsQueryDTO extends BaseDTO
{
    /** 全文检索字符串*/
    private String queryString;

//    /** 商家ID*/
//    private String shopId;
//
//    /** 商家名*/
//    private String shopName;
//
//    /** 商家标志*/
//    private String shopTag;
//
//    /** 商家类型*/
//    private String shopType;
//
//    /** 状态条件*/
//    private String status;
//
//    /** 是否在客户端展示 */
//    private String showClient;
//
//    /** 模板编码*/
//    private String templateCode;
//
//    /** 城市ID*/
//    private String cityIds;
//
//    /** 组织ID*/
//    private String oecdId;
//
//    /** 指定类目*/
//    private String goodsCategoryId;

    /** 排序条件集*/
    private List<EsSort> sorts;

    /**
     * 搜索的用户
     */
    private String userId;

    //=======================================================================================
    //=======================================================================================

    public String getQueryString()
    {
        return queryString;
    }

    /**
     * 过滤空格
     * @param queryString
     */
    public void setQueryString(String queryString)
    {
        this.queryString = QueryParser.escape(queryString.trim());
    }

    //=======================================================================================
    //=======================================================================================
}
