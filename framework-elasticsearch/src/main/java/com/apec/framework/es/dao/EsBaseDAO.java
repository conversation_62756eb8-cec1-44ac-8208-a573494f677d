package com.apec.framework.es.dao;

import java.io.Serializable;

import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;
import org.springframework.data.repository.NoRepositoryBean;

/**
 * <p>类 编 号：</p>
 * <p>类 名 称：</p>
 * <p>内容摘要：该类继承了ElasticsearchRepository，类似JPA操作。</p>
 * <p>创建日期：2018/9/13 9:43</p>
 * <AUTHOR>
 */
@NoRepositoryBean
public interface EsBaseDAO<T, ID extends Serializable> extends ElasticsearchRepository<T, ID>
{
}
