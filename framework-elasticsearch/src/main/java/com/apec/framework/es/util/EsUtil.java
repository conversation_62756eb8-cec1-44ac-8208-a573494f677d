package com.apec.framework.es.util;

import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.springframework.data.elasticsearch.annotations.FieldIndex;
import org.springframework.data.elasticsearch.annotations.FieldType;

import com.apec.framework.es.annotation.FieldBoost;
import com.apec.framework.es.common.EsConstant;
import com.apec.framework.es.dto.EsFields;
import com.apec.framework.es.model.EsBaseModel;
import com.google.common.collect.Lists;

/**
 * ES工具
 * Created by KINGFS on 2017/9/14.
 * <AUTHOR>
 */
public abstract class EsUtil
{

    /**
     * 获取指定分词方式的字段
     * @param clazz Model的class
     * @param fieldIndex 索引方式
     * @return 字段集
     */
    public static List<String> getFilesByFieldIndex(Class<?> clazz, FieldIndex fieldIndex)
    {
        List<String> fieldAnnos = Lists.newArrayList();

        // 获取自身字段
        fieldAnnos.addAll(getFilesByFieldIndex(clazz.getDeclaredFields(), fieldIndex));
        // 获取父类的字段-考虑多级继承
        for(Class supClazz = clazz.getSuperclass(); null != supClazz; supClazz = supClazz.getSuperclass())
        {
            fieldAnnos.addAll(getFilesByFieldIndex(supClazz.getDeclaredFields(), fieldIndex));
        }

        return fieldAnnos;
    }

    /**
     * 获取指定fieldIndex的属性名
     * @param fields
     * @param fieldIndex
     * @return
     */
    private static List<String> getFilesByFieldIndex(Field[] fields, FieldIndex fieldIndex)
    {
        org.springframework.data.elasticsearch.annotations.Field field;
        List<String> fieldAnnos = Lists.newArrayList();
        for(Field f : fields)
        {
            field = f.getAnnotation(org.springframework.data.elasticsearch.annotations.Field.class);
            if(null != field && field.index() == fieldIndex)
            {
                fieldAnnos.add(f.getName());
            }
        }

        return fieldAnnos;
    }

    //=======================================================================================================
    //=======================================================================================================

    /**
     * 获取指定的字段类型的字段
     * @param clazz Model的class
     * @param fieldType 索引类型
     * @return 字段集
     */
    public static List<String> getFilesByFieldType(Class<?> clazz, FieldType fieldType)
    {
        List<String> fieldAnnos = Lists.newArrayList();

        // 获取自身字段
        fieldAnnos.addAll(getFilesByFieldType(clazz.getDeclaredFields(), fieldType));
        //# 获取父类的字段-考虑多级继承
        for(Class supClazz = clazz.getSuperclass(); null != supClazz; supClazz = supClazz.getSuperclass())
        {
            fieldAnnos.addAll(getFilesByFieldType(supClazz.getDeclaredFields(), fieldType));
        }

        return fieldAnnos;
    }

    /**
     * 获取指定fieldType的属性名
     * @param fields
     * @param fieldType
     * @return
     */
    private static List<String> getFilesByFieldType(Field[] fields, FieldType fieldType)
    {
        org.springframework.data.elasticsearch.annotations.Field field;
        List<String> fieldAnnos = Lists.newArrayList();
        for(Field f : fields)
        {
            field = f.getAnnotation(org.springframework.data.elasticsearch.annotations.Field.class);
            if(null != field && field.type() == fieldType)
            {
                fieldAnnos.add(f.getName());
            }
        }

        return fieldAnnos;
    }

    //=======================================================================================================
    //=======================================================================================================

    /**
     * 获取自定义权重配置
     * @param clazz Model的class
     * @return 权重集
     */
    public static List<EsFields> getFileBoost(Class<? extends EsBaseModel> clazz)
    {
        List<EsFields> fileBoost = getFileBoost(clazz, null);

        // TODO : 尚存在问题,加入nest字段权重后全文检索无法检索nest字段的信息,暂未开放
        //        return fileBoost;
        return Lists.newArrayList();
    }

    /**
     * 获取自定义权重配置
     * @param clazz Model的class
     * @param path nest类型Model的路径
     * @return 权重集
     */
    private static List<EsFields> getFileBoost(Class<? extends EsBaseModel> clazz, final String path)
    {
        List<EsFields> esFields = Lists.newArrayList();

        // 获取自身权重字段
        esFields.addAll(getBoost(clazz.getDeclaredFields(), path));
        // 获取父类的权重字段-考虑多级继承
        for(Class supClazz = clazz.getSuperclass(); null != supClazz; supClazz = supClazz.getSuperclass())
        {
            esFields.addAll(getBoost(supClazz.getDeclaredFields(), path));
        }

        return esFields;
    }

    /**
     * 获取Fields中属性配置的权重
     * @param fields
     * @param path
     * @return
     */
    private static List<EsFields> getBoost(Field[] fields, String path)
    {
        FieldBoost fieldBoost;
        org.springframework.data.elasticsearch.annotations.Field field;
        List<EsFields> esFields = Lists.newArrayList();
        EsFields dto;
        for(Field f : fields)
        {
            fieldBoost = f.getAnnotation(FieldBoost.class);
            if(null == fieldBoost)
            {
                field = f.getAnnotation(org.springframework.data.elasticsearch.annotations.Field.class);
                if(null != field && field.type() == FieldType.Nested)
                {
                    esFields.addAll(getNestBoost(f, path));
                }
                continue;
            }

            dto = new EsFields();
            dto.setField(StringUtils.isNotEmpty(path) ? path + EsConstant.POINT + f.getName() : f.getName());
            dto.setBoost(fieldBoost.boost());

            esFields.add(dto);
        }

        return esFields;
    }

    /**
     * 获取Nest字段的权重
     * @param f
     * @param path
     * @return
     */
    private static List<EsFields> getNestBoost(Field f, String path)
    {
        Class<? extends EsBaseModel> aClass;
        List<EsFields> fileBoost;
        List<EsFields> esFields = Lists.newArrayList();

        Type genericType = f.getGenericType();
        if(genericType instanceof ParameterizedType)
        {
            ParameterizedType parameterizedType = (ParameterizedType)genericType;
            Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
            for(Type actualTypeArgument : actualTypeArguments)
            {
                aClass = (Class<? extends EsBaseModel>)actualTypeArgument;
                fileBoost = getFileBoost(aClass, StringUtils.isNotEmpty(path) ? path + EsConstant.POINT + f.getName() : f.getName());
                if(CommonTools.isNotEmpty(fileBoost))
                {
                    esFields.addAll(fileBoost);
                }
            }
        }
        else
        {
            aClass = (Class<? extends EsBaseModel>)genericType;
            fileBoost = getFileBoost(aClass, StringUtils.isNotEmpty(path) ? path + EsConstant.POINT + f.getName() : f.getName());
            if(CommonTools.isNotEmpty(fileBoost))
            {
                esFields.addAll(fileBoost);
            }
        }

        return esFields;
    }
}