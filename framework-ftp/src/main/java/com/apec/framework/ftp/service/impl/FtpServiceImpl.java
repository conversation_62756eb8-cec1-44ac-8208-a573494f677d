package com.apec.framework.ftp.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.apec.framework.common.constant.ConfigConsts;
import com.apec.framework.common.constant.ErrorCodeConsts;
import com.apec.framework.common.exception.BusinessException;
import com.apec.framework.ftp.service.FtpService;

/**
 * 基本描述：FtpServiceImpl FTP业务逻辑处理
 *
 * <AUTHOR>
 */
@Service
public class FtpServiceImpl implements FtpService
{

    private FTPClient ftp;

    private static final Logger LOGGER = LoggerFactory.getLogger(FtpServiceImpl.class);

    private static final int DEFAULT_TIMEOUT = 20000;

    private static final int CONNECT_TIMEOUT = 50000;

    private static final int DATA_TIMEOUT = 100000;

    private static final String SLASH = "/";

    @Value("${ftp.host}")
    private String host;

    @Value("${ftp.port}")
    private int port;

    @Value("${ftp.username}")
    private String username;

    @Value("${ftp.password}")
    private String password;

    @Value("${ftp.workingDir}")
    private String workingDir;


    private FTPClient createNewConnect() throws BusinessException
    {
//        if(this.ftp != null && this.ftp.isConnected()){
//            return this.ftp;
//        }
        this.ftp = new FTPClient();

        ftp.setDefaultTimeout(DEFAULT_TIMEOUT);
        ftp.setConnectTimeout(CONNECT_TIMEOUT);
        ftp.setDataTimeout(DATA_TIMEOUT);
        ftp.setControlEncoding(ConfigConsts.SYSTEM_ENCODING);
        // 连接ftp
        try
        {
            ftp.connect(host, port);
        }
        catch (IOException e)
        {
            throw new BusinessException(ErrorCodeConsts.ERROR_FTP_CONNECT_FAILD);
        }

        try
        {
            if(!ftp.login(username.trim(), password.trim()))
            {
                disconnect(ftp);
                throw new BusinessException(ErrorCodeConsts.ERROR_FTP_LOGIN_FAILD);
            }
        }
        catch (IOException e)
        {
            throw new BusinessException(ErrorCodeConsts.ERROR_FTP_LOGIN_FAILD);
        }

        if(!FTPReply.isPositiveCompletion(ftp.getReplyCode()))
        {
            disconnect(ftp);
            throw new BusinessException(ErrorCodeConsts.ERROR_FTP_CONNECT_FAILD);
        }

        try
        {
            ftp.setFileType(FTP.BINARY_FILE_TYPE);
        }
        catch (IOException e)
        {
            throw new BusinessException(ErrorCodeConsts.ERROR_FTP_FILETYPE_FAILD);
        }
        //设置工作目录
        try
        {
            if(!ftp.changeWorkingDirectory(workingDir))
            {
                disconnect(ftp);
                throw new BusinessException(ErrorCodeConsts.ERROR_FTP_WORKINGDIR_FAILD);
            }
        }
        catch (IOException e)
        {
            throw new BusinessException(ErrorCodeConsts.ERROR_FTP_WORKINGDIR_FAILD);
        }
        //        ftp.enterLocalPassiveMode(); //使用被动模式通过防火墙
        return ftp;
    }

    private void disconnect(FTPClient ftp)
    {
        if(ftp.isConnected())
        {
            try
            {
                ftp.logout();
                ftp.disconnect();
            }
            catch (IOException e)
            {
                LOGGER.error("It occurred error in executing FtpServiceImpl.disconnect IOException case: " + e);
            }
        }
    }

    @Override
    public synchronized void uploadFile(String filePath, String ftpFileName, InputStream is) throws BusinessException
    {
        FTPClient ftpClient = createNewConnect();
        try
        {
            LOGGER.info("创建新的ftp连接,ReplyCode:{},ReplyString:{},Status:{}",
                    ftpClient.getReplyCode() ,
                    ftpClient.getReplyString(),
                    ftpClient.getStatus());

            String[] folderList = filePath.split(SLASH);
            String currWkDir = workingDir;
            for(String folder : folderList)
            {
                if(StringUtils.isNotEmpty(folder))
                {
                    currWkDir = currWkDir + SLASH + folder;
                    boolean flag = ftpClient.changeWorkingDirectory(currWkDir);
                    LOGGER.info("切换目录:{}, rs:{}",currWkDir,flag);
                    if(!flag)
                    {
                        flag = ftpClient.makeDirectory(folder);
                        LOGGER.info("在目录:{} 下新建目录:{}, rs:{}",currWkDir, folder,flag);
                        flag = ftpClient.changeWorkingDirectory(currWkDir);
                        LOGGER.info("切换目录:{}, rs:{}",currWkDir,flag);
                    }
                }
            }

            if(ftpClient.storeFile(ftpFileName, is))
            {
                return;
            }
        }
        catch (IOException e)
        {
            e.printStackTrace();
            LOGGER.error("It occurred error in executing FtpServiceImpl.uploadFile IOException case:{}", e);
        }finally {
            LOGGER.info("开始关闭ftp ...");
            disconnect(this.ftp);
            LOGGER.info("已关闭ftp.");
        }
        throw new BusinessException(ErrorCodeConsts.ERROR_FTP_UPLOAD_FAILD);
    }

    @Override
    public synchronized byte[] download(String ftpFileName) throws BusinessException
    {
        FTPClient ftpClient = createNewConnect();
        try
        {
            //列出指定路径所有文件（包括文件夹）
            FTPFile[] fileInfoArray = ftpClient.listFiles(ftpFileName);
            if(fileInfoArray != null && fileInfoArray.length > 0)
            {
                ByteArrayOutputStream out = new ByteArrayOutputStream();
                //进入被动模式
                ftpClient.enterLocalPassiveMode();
                if(ftpClient.retrieveFile(ftpFileName, out))
                {
                    return out.toByteArray();
                }
            }
        }
        catch (IOException e)
        {
            LOGGER.error("It occurred error in executing FtpServiceImpl.download IOException case: {}", e);
        }
        finally {
            LOGGER.info("//// 下載，开始关闭ftp ...");
            disconnect(this.ftp);
            LOGGER.info("//// 下載，已关闭ftp.");
        }
        throw new BusinessException(ErrorCodeConsts.ERROR_FTP_DOWNLOAD_FAILD);
    }

    @Override
    public void close() {
        if(this.ftp != null && this.ftp.isConnected()){
            try {
                this.ftp.logout();
                this.ftp.disconnect();
            } catch (IOException e) {
                LOGGER.error("ftp channel close fail, cause {}",e);
            }
        }
        this.ftp = null;
    }
}
