package com.apec.mgtdatadict;

import com.apec.datadict.dto.*;
import com.apec.framework.common.model.ResultData;
import com.apec.magpie.cb.dto.ApecRpcParamDTO;
import com.apec.magpie.cb.dto.SelectDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;


@FeignClient(value = "BASE-DATADICT")
public interface MgtBaseDataDictClient {

    @PostMapping("extbasedatadict/getSelect")
    ResultData<List<SelectDTO>> getSelect(@RequestBody ApecRpcParamDTO<SelectQueryDTO> dto);

    @PostMapping("extbasedatadict/getDictItemSelect")
    ResultData<List<SelectDTO>> getDictItemSelect(@RequestBody ApecRpcParamDTO<DataDictSubDTO> dto);

    @PostMapping("extbasedatadict/getDictItemSelectList")
    ResultData<Map<String, List<SelectDTO>>> getDictItemSelectList(@RequestBody ApecRpcParamDTO<DictItemSelectListQueryDTO> dto);
}

