# must be unique in a given SonarQube instance
sonar.host.url=http://*************:19000 
#sonar.login=datadict
#sonar.password=datadict
sonar.login=****************************************
#sonar.password=****************************************
#
sonar.language=java
# Encoding of the source code. Default is default system encoding
sonar.sourceEncoding=UTF-8

sonar.projectKey=APEC_CJ101_DataDict

# --- optional properties ---

# defaults to project key
sonar.projectName=APEC_CJ101_DataDict
# defaults to 'not provided'
sonar.projectVersion=1.0.0-RELEASE

# Path is relative to the sonar-project.properties file. Defaults to .
sonar.sources=/Users/<USER>/gitapec/magpie/APEC_CJ101_DataDict/datadict-server/src
sonar.java.binaries==/Users/<USER>/gitapec/magpie/APEC_CJ101_DataDict/datadict-server/target/classes


