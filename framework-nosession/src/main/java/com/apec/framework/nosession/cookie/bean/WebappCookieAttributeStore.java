package com.apec.framework.nosession.cookie.bean;

import com.apec.framework.nosession.cookie.AttributeCookieStore;
import com.apec.framework.nosession.cookie.Encode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;

/**
 * ClientUAInfo cookie封装類
 * Created by l<PERSON><PERSON> on 2018/5/2.
 */
@Component
public class WebappCookieAttributeStore implements AttributeCookieStore //extends CloudCookieAttributeStore
{
    private Set<String> keyNames = new HashSet<String>();

    @Value("${cookieName:cl_ua}")
    private String cookieName;

    private String path = "/";

    @Value("${app.domain:apec.develop}")
    private String domain;

    private int maxInactiveInterval = -1;

    @Autowired
    private Encode sucCookieEncode;

	{
		keyNames.add("clientUAInfo");
	}

    public Set<String> getKeyNames() {
        return keyNames;
    }

    public void setKeyNames(Set<String> keyNames) {
        this.keyNames = keyNames;
    }

    public void setCookieName(String cookieName) {
        this.cookieName = cookieName;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public void setMaxInactiveInterval(int maxInactiveInterval) {
        this.maxInactiveInterval = maxInactiveInterval;
    }

    public Set<String> getAttributeNames() {
        return keyNames;
    }

    public boolean isMatch(String key) {
        return keyNames.contains(key);
    }

    public int getOrder() {
        return 10;
    }

    @Override
    public int getMaxInactiveInterval() {
        return maxInactiveInterval;
    }

    @Override
    public String getCookieName() {
        return cookieName;
    }

    @Override
    public String getPath() {
        return path;
    }

    /**
     * @param encode
     *            the encode to set
     */
    public void setEncode(Encode encode) {
        this.sucCookieEncode = encode;
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.hundsun.jresplus.web.nosession.cookie.AttributeCookieStore#getEncode
     * ()
     */
    @Override
    public Encode getEncode() {
        return sucCookieEncode;
    }

    @Override
    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }
}
