package com.apec.framework.nosession.cookie;

import java.util.Set;
import org.springframework.core.Ordered;

/**
 * cookie装饰接口
 */
public abstract interface AttributeCookieStore
  extends Ordered
{
  public abstract boolean isMatch(String paramString);
  
  public abstract String getCookieName();
  
  public abstract Encode getEncode();
  
  public abstract int getMaxInactiveInterval();
  
  public abstract String getPath();
  
  public abstract Set<String> getAttributeNames();
  
  public abstract String getDomain();
}
