package com.apec.framework.nosession.cookie;

import com.apec.framework.nosession.SessionEncoderException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * cookie管理类接口
 */
public abstract interface CookiesManager
{
    public abstract String readCookieValue(HttpServletRequest paramHttpServletRequest, String paramString);

    public abstract void writeCookie(HttpServletRequest paramHttpServletRequest,
        HttpServletResponse paramHttpServletResponse, <PERSON><PERSON> paramCookie)
        throws SessionEncoderException;
}
