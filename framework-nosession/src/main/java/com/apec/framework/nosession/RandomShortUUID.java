package com.apec.framework.nosession;

import java.security.SecureRandom;

public final class RandomShortUUID
    implements UUIDGenerator
{
    private static volatile SecureRandom numberGenerator = new SecureRandom();

    private static final int length = 25;

    private static StringBuilder getBuilder()
    {
        SecureRandom ng = numberGenerator;
        byte[] data = new byte[16];
        ng.nextBytes(data);

        StringBuilder sb = new StringBuilder(25);
        long bits = data[0] & 0xFF;
        bits = bits << 8 | data[1] & 0xFF;
        bits = bits << 8 | data[2] & 0xFF;
        bits = bits << 8 | data[3] & 0xFF;
        bits = bits << 8 | data[4] & 0xFF;
        bits = bits << 8 | data[5] & 0xFF;
        bits = bits << 8 | data[6] & 0xFF;
        bits = bits << 6 | data[7] & 0x3F;
        append(bits, sb);
        bits = data[8] & 0xFF;
        bits = bits << 8 | data[9] & 0xFF;
        bits = bits << 8 | data[10] & 0xFF;
        bits = bits << 8 | data[11] & 0xFF;
        bits = bits << 8 | data[12] & 0xFF;
        bits = bits << 8 | data[13] & 0xFF;
        bits = bits << 8 | data[14] & 0xFF;
        bits = bits << 4 | data[15] & 0xF;
        append(bits, sb);
        return sb;
    }

    public static String getFixSize()
    {
        StringBuilder sb = getBuilder();
        int needAdd = 25 - sb.length();
        for(int i = 0; i < needAdd; i++)
        {
            sb.append('0');
        }
        return sb.toString();
    }

    public static String get()
    {
        return getBuilder().toString();
    }

    public String gain()
    {
        return get();
    }

    static final char[] Digits = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f', 'g',
        'h', 'i', 'j', 'k', 'm', 'n', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x'};

    private static final int Radix = Digits.length;

    private static void append(long i, StringBuilder sb)
    {
        char[] buf = new char[13];
        int charPos = 12;
        i = -i;
        while (i <= -Radix)
        {
            buf[(charPos--)] = Digits[((int)-(i % Radix))];
            i /= Radix;
        }
        buf[charPos] = Digits[((int)-i)];
        sb.append(buf, charPos, 13 - charPos);
    }
}
