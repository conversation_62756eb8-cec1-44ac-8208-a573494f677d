package com.apec.framework.nosession.common.util.digest;

/**
 * 信息摘要接口
 * 
 * <AUTHOR>
 * @version $Id: MessageDigest.java,v 0.1 2010-6-10 下午05:03:53 zhengdd Exp $
 */
public interface MessageDigest {

    /**
     * 对明文信息进行信息摘要处理,使用UTF-8编码
     * 
     * @param text 明文信息
     * @return String 信息摘要
     */
    public String digest(String text);

    /**
     * 对明文信息进行信息摘要处理,使用指定字符集编码
     *  
     * @param text 明文信息
     * @param encoding 字符集编码
     * @return 信息摘要
     */
    public String digest(String text, String encoding);

}
