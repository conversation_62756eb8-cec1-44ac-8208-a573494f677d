package com.apec.framework.nosession.cookie;

import com.hundsun.jresplus.common.util.StringUtil;

public class <PERSON><PERSON>
    extends javax.servlet.http.Cookie
    implements Cloneable
{
    private static final long serialVersionUID = 7271090104478428146L;

    private boolean httpOnly = false;

    public Cookie(String name, String value)
    {
        super(name, value);
    }

    public Cookie(String name, String value, boolean httpOnly)
    {
        super(name, value);
        this.httpOnly = httpOnly;
    }

    public Cookie(String name, String value, boolean httpOnly, int maxAge)
    {
        super(name, value);
        setMaxAge(maxAge);
        this.httpOnly = httpOnly;
    }

    public Cookie(String name, String value, boolean httpOnly, int maxAge, String path)
    {
        super(name, value);
        setMaxAge(maxAge);
        setPath(path);
        this.httpOnly = httpOnly;
    }

    public Cookie(String name, String value, boolean httpOnly, int maxAge, String path, String domain)
    {
        super(name, value);
        setMaxAge(maxAge);
        setPath(path);
        if(!StringUtil.isEmpty(domain))
        {
            setDomain(domain);
        }
        this.httpOnly = httpOnly;
    }

    public boolean isHttpOnly()
    {
        return this.httpOnly;
    }

    public void setHttpOnly(boolean httpOnly)
    {
        this.httpOnly = httpOnly;
    }

    public Object clone()
    {
        return super.clone();
    }
}
