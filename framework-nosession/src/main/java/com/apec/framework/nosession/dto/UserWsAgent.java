package com.apec.framework.nosession.dto;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * 处理恒生的user对象
 * Created by l<PERSON><PERSON> on 2018/4/26.
 */
public class UserWsAgent extends BaseDTO
{

    private static Log log = LogFactory.getLog(UserWsAgent.class);

    public static final String UC_SESSION_ID = "r_userAgent_sessionId";
    public static final String UC_ID = "r_userAgent_id";
    public static final String UC_ACCOUNT = "r_userAgent_account";
    public static final String UC_NAME = "r_userAgent_name";
    public static final String UC_MENU = "r_sys_menu_form";
    public static final String UC_FOOTER = "r_sys_footer";
    public static final String UC_HEADER = "r_sys_header";
    public static final String UC_CMS_HEADER = "r_sys_cms_header";
    public static final String UC_USER = "r_userAgent";
    public static final String UC_USR_AGT = "userAgent";
    public static final String UC_SUB_SYSTEM = "r_sub_system_form";
    public static final String UC_SUB_SYSTEM_CODE = "r_sub_code";
    public static final String UC_BIZ_CODE = "_biz_code_";
    public static final String UC_BIZ_GROUPS = "_biz_groups_";
    public static final String UC_SUB_SYSTEM_HOST = "r_sub_system_form_host";
    /**
     * 系统配置的url导航条地址信息
     */
    public static final String UC_URL_NAVI = "uc_url_navi";
    /**
     * 系统配置的权限code导航条地址信息
     */
    public static final String UC_AUTH_CODE_NAVI = "uc_auth_code_navi";
    /**
     * 当前方法请求的子系统code
     */
    public static final String CURR_AUTH_CODE = "uc_curr_auth_code";
    /**
     * url引用的导航条地址信息
     */
    public static final String UC_REQ_REF_NAVI = "uc_req_ref_navi";
    /**
     * (cookie)会员登录有效的token
     */
    public static final String COOKIE_VALID_TOKEN = "_v_t_";

    /**
     * 无权限的隐藏错误信息
     */
    public static final String ACCESS_DENIED_MSG = "access_denied_msg";

    /**
     * 用户交易权限的key
     */
    public static final String USER_TRADE_FUNC_KEY = "user_trade_func";

    /**
     * 客户端UserAgent的cookie信息（cookie中记录userId，sessionId和token）
     */
    public static final String COOKIE_CLIENT_AGENT = "cl_ua";

    /**
     *
     */
    private static final long serialVersionUID = 2488216455575393857L;

    /**
     * 用户对应组织key
     */
    public static final Object USER_MARKETS_KEY = "user_markets_key";

    //权限code列表
    private Map<String,List<String>> permissions = new HashMap<String, List<String>>();

    /**
     * 用户token信息（多终端登录），key是多终端区分的标志位
     */
    private Map<String, UserTokenInfo> userTokenInfoMap;

    //最后存活时间
    private long lastActiveTime;

    private Map<String ,Object > userMap;

    private String currentSubSystemCode;

    /**
     * 会话id
     */
    private String sessionId;

    /**
     * 交易所id
     */
    private Integer exchangeId;

    /**
     * 是否限制多终端登录
     */
    private boolean isLimitMultiClientLogin;

    /**
     * 是否需要刷新权限缓存的标志位
     */
    private boolean needRefreshPermissionFlag;
    /**
     * 是否需要刷新是否绑卡的标志位
     */
    private boolean needRefreshWhetherTiedCardFlag;

    /**
     * 提示信息
     */
    private String tipMsg;

    /**
     * 用户所属组织
     */
    private List<String> markertList;

    /**
     * 序列化
     * @return
     */
    public String doSerializing(){
        return null;
    }

    /**
     * 反序列化
     * @param serializedString	序列化字符串
     * @return
     */
    public String doUnSerializing(String serializedString){
        return null;
    }

    public String getUserId(){
        if(userMap!=null && !userMap.isEmpty()){
            return userMap.get("userId").toString();
        }
        return null;
    }

    /**
     * 设置用户权限信息
     * @param permission
     */
    public void setPermission(List<String> permissionList,String systemCode){
        if(log.isDebugEnabled()){
            log.debug("system[" + systemCode + "] sets permission :[" + permissionList.toArray() + "]");
        }
        this.permissions.put(systemCode, permissionList);
    }

    /**
     * 判断是否有权限
     * @param permissionCode
     * @return
     */
    public boolean havePermission(String permissionCode,String systemCode){
        if(log.isDebugEnabled()){
            log.debug("action need permission :"+permissionCode);
        }
        this.lastActiveTime = System.currentTimeMillis();
        List<String> systemPermission = this.permissions.get(systemCode);
        if(systemPermission==null || systemPermission.size()==0){
            return false;
        }
        for(String code : systemPermission){
            if(code.equalsIgnoreCase(permissionCode)){
                return true;
            }
        }
        return false;
    }

    /**
     * 是否被强制退出（当其他客户端登录时）
     * @param clientToken 客户端传过来的token
     * @return
     * <AUTHOR>
     */
    public boolean isForcedLogout(String clientToken) {
        if(!isMultiClientLoginEnabled()) {
            return false;
        }
        if(StringUtils.isBlank(clientToken)) {
            return false;
        }
        if(userTokenInfoMap != null && !userTokenInfoMap.isEmpty()) {
            for(Iterator<Map.Entry<String, UserTokenInfo>> iter = userTokenInfoMap.entrySet().iterator(); iter.hasNext(); ) {
                Map.Entry<String, UserTokenInfo> entry = iter.next();
                UserTokenInfo userTokenInfo = entry.getValue();
                if(userTokenInfo != null) {
                    if(userTokenInfo.getUserToken().equals(clientToken)) {
                        return false;
                    }
                }
            }

            //如果上述两个登录类型的token都校验不过，则返回强制退出标识
            return true;
        }
        return false;
    }

    /**
     * 获取最后存活时间
     * @return
     */
    public long getLastActiveTime(){
        return this.lastActiveTime;
    }

    public Map<String, List<String>> getPermissions() {
        updateActiveTime();
        return permissions;
    }

    private void updateActiveTime() {
        this.lastActiveTime = System.currentTimeMillis();
    }

    public void setPermissions(Map<String, List<String>> permissions) {
        this.permissions = permissions;
    }

    public Map<String, Object> getUserMap() {
        return userMap;
    }

    public void setUserMap(Map<String, Object> userMap) {
        this.userMap = userMap;
    }


    public String getCurrentSubSystemCode() {
        return currentSubSystemCode;
    }

    public void setCurrentSubSystemCode(String currentSubSystemCode) {
        this.currentSubSystemCode = currentSubSystemCode;
    }

    public Integer getExchangeId() {
        return exchangeId;
    }

    public void setExchangeId(Integer exchangeId) {
        this.exchangeId = exchangeId;
    }


    /**
     * 是否多终端登录启用
     * @return
     * <AUTHOR>
     */
    public boolean isMultiClientLoginEnabled() {
        return isLimitMultiClientLogin;
    }

    public void setLimitMultiClientLogin(boolean isLimitMultiClientLogin) {
        this.isLimitMultiClientLogin = isLimitMultiClientLogin;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }


    public boolean isNeedRefreshWhetherTiedCardFlag() {
        return needRefreshWhetherTiedCardFlag;
    }

    public void setNeedRefreshWhetherTiedCardFlag(boolean needRefreshWhetherTiedCardFlag) {
        this.needRefreshWhetherTiedCardFlag = needRefreshWhetherTiedCardFlag;
    }

    //end



    public boolean isNeedRefreshPermissionFlag() {
        return needRefreshPermissionFlag;
    }

    public void setNeedRefreshPermissionFlag(boolean needRefreshPermissionFlag) {
        this.needRefreshPermissionFlag = needRefreshPermissionFlag;
    }

    public boolean isModifyPwd() {
        if(MapUtils.isNotEmpty(this.userMap) && this.userMap.containsKey("isModifyPwd")){
            if( "1".equals(this.userMap.get("isModifyPwd").toString())){
                this.userMap.remove("isModifyPwd");
                return Boolean.TRUE;
            }

        }
        return Boolean.FALSE;
    }

    public String getTipMsg() {
        return tipMsg;
    }

    public void setTipMsg(String tipMsg) {
        this.tipMsg = tipMsg;
    }

    public List<String> getMarkertList() {
        return markertList;
    }

    public void setMarkertList(List<String> markertList) {
        this.markertList = markertList;
    }

}
