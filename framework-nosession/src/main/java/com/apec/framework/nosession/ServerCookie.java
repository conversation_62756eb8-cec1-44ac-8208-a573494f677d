
package com.apec.framework.nosession;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

public class ServerCookie

{
    private static final String OLD_COOKIE_PATTERN = "EEE, dd-MMM-yyyy HH:mm:ss z";

    private static final ThreadLocal<DateFormat> OLD_COOKIE_FORMAT = new ThreadLocal()

    {

        protected DateFormat initialValue()

        {

            DateFormat df = new SimpleDateFormat("EEE, dd-MMM-yyyy HH:mm:ss z", Locale.US);

            df.setTimeZone(TimeZone.getTimeZone("GMT"));

            return df;

        }

    };

    private static final String ancientDate = ((DateFormat)OLD_COOKIE_FORMAT.get()).format(new Date(10000L));

    public static final boolean STRICT_SERVLET_COMPLIANCE = Boolean
        .valueOf(System.getProperty("org.apache.catalina.STRICT_SERVLET_COMPLIANCE", "false")).booleanValue();

    public static final boolean ALWAYS_ADD_EXPIRES = Boolean
        .valueOf(System.getProperty("org.apache.tomcat.util.http.ServerCookie.ALWAYS_ADD_EXPIRES", "true"))
        .booleanValue();

    private static final String tspecials = ",; ";

    private static final String tspecials2 = "()<>@,;:\\\"/[]?={} \t";

    private static final String tspecials2NoSlash = "()<>@,;:\\\"[]?={} \t";

    public static boolean isToken(String value)

    {

        return isToken(value, null);

    }

    public static boolean isToken(String value, String literals)
    {

        String tspecials = literals == null ? ",; " : literals;

        if(value == null)
        {

            return true;

        }

        int len = value.length();

        for(int i = 0; i < len; i++)
        {

            char c = value.charAt(i);

            if(tspecials.indexOf(c) != -1)
            {

                return false;

            }

        }

        return true;

    }

    public static boolean containsCTL(String value, int version)
    {

        if(value == null)
        {

            return false;

        }

        int len = value.length();

        for(int i = 0; i < len; i++)
        {

            char c = value.charAt(i);

            if(((c < ' ') || (c >= '')) &&
               (c != '\t'))

            {

                return true;

            }

        }

        return false;

    }

    public static boolean isToken2(String value)
    {

        return isToken2(value, null);

    }

    public static boolean isToken2(String value, String literals)
    {

        String tspecials2 = literals == null ? "()<>@,;:\\\"/[]?={} \t" : literals;

        if(value == null)
        {

            return true;

        }

        int len = value.length();

        for(int i = 0; i < len; i++)
        {

            char c = value.charAt(i);

            if(tspecials2.indexOf(c) != -1)
            {

                return false;

            }

        }

        return true;

    }

    public static String getCookieHeaderName(int version)

    {

        if(version == 1)

        {

            return "Set-Cookie";

        }

        return "Set-Cookie";

    }

    public static void appendCookieValue(StringBuilder buf, int version, String name, String value, String path,
        String domain, String comment, int maxAge, boolean isSecure, boolean isHttpOnly)

    {

        buf.append(name);

        buf.append("=");

        version = maybeQuote2(version, buf, value, true);

        if(version == 1)

        {

            buf.append("; Version=1");

            if(comment != null)
            {

                buf.append("; Comment=");

                maybeQuote2(version, buf, comment);

            }

        }

        if(domain != null)
        {

            buf.append("; Domain=");

            maybeQuote2(version, buf, domain);

        }

        if(maxAge >= 0)
        {

            if(version > 0)
            {

                buf.append("; Max-Age=");

                buf.append(maxAge);

            }

            if((version == 0) || (ALWAYS_ADD_EXPIRES))

            {

                buf.append("; Expires=");

                if(maxAge == 0)
                {

                    buf.append(ancientDate);

                }
                else
                {

                    buf.append(((DateFormat)OLD_COOKIE_FORMAT.get())
                                   .format(new Date(System.currentTimeMillis() + maxAge * 1000L)));

                }

            }

        }

        else if(version > 0)
        {

            buf.append("; Discard");

        }

        if(path != null)
        {

            buf.append("; Path=");

            if(version == 0)
            {

                maybeQuote2(version, buf, path);

            }
            else
            {

                maybeQuote2(version, buf, path, "()<>@,;:\\\"[]?={} \t", false);

            }

        }

        if(isSecure)
        {

            buf.append("; Secure");

        }

        if(isHttpOnly)
        {

            buf.append("; HttpOnly");

        }

    }

    public static boolean alreadyQuoted(String value)
    {

        if((value == null) || (value.length() == 0))
        {

            return false;

        }

        return (value.charAt(0) == '"') && (value.charAt(value.length() - 1) == '"');

    }

    public static int maybeQuote2(int version, StringBuilder buf, String value)

    {

        return maybeQuote2(version, buf, value, false);

    }

    public static int maybeQuote2(int version, StringBuilder buf, String value, boolean allowVersionSwitch)

    {

        return maybeQuote2(version, buf, value, null, allowVersionSwitch);

    }

    public static int maybeQuote2(int version, StringBuilder buf, String value, String literals,
        boolean allowVersionSwitch)

    {

        if((value != null) && (value.length() != 0))

        {

            if(containsCTL(value, version))
            {

                throw new IllegalArgumentException(
                    "Control character in cookie value, consider BASE64 encoding your value");

            }

            if(alreadyQuoted(value))
            {

                buf.append('"');

                buf.append(escapeDoubleQuotes(value, 1, value.length() - 1));

                buf.append('"');

            }
            else if((allowVersionSwitch) && (!STRICT_SERVLET_COMPLIANCE) && (version == 0) && (!isToken2(value,
                                                                                                         literals)))

            {

                buf.append('"');

                buf.append(escapeDoubleQuotes(value, 0, value.length()));

                buf.append('"');

                version = 1;

            }
            else if((version == 0) && (!isToken(value, literals)))
            {

                buf.append('"');

                buf.append(escapeDoubleQuotes(value, 0, value.length()));

                buf.append('"');

            }
            else if((version == 1) && (!isToken2(value, literals)))
            {

                buf.append('"');

                buf.append(escapeDoubleQuotes(value, 0, value.length()));

                buf.append('"');

            }
            else
            {

                buf.append(value);

            }
        }

        return version;

    }

    private static String escapeDoubleQuotes(String s, int beginIndex, int endIndex)

    {

        if((s == null) || (s.length() == 0) || (s.indexOf('"') == -1))
        {

            return s;

        }

        StringBuilder b = new StringBuilder();

        for(int i = beginIndex; i < endIndex; i++)
        {

            char c = s.charAt(i);

            if(c == '\\')
            {

                b.append(c);

                i++;
                if(i >= endIndex)
                {

                    throw new IllegalArgumentException("Invalid escape character in cookie value.");

                }

                b.append(s.charAt(i));

            }
            else if(c == '"')
            {

                b.append('\\').append('"');

            }
            else
            {

                b.append(c);

            }

        }

        return b.toString();

    }

}
