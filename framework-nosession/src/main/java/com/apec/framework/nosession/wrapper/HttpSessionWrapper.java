package com.apec.framework.nosession.wrapper;

import com.apec.framework.nosession.NoSessionContext;
import com.apec.framework.nosession.StoreContext;
import com.hundsun.jresplus.common.util.StringUtil;

import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import javax.servlet.http.HttpSessionContext;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * session 装饰类
 */
public class HttpSessionWrapper
    implements HttpSession
{
    private static final Logger log = LoggerFactory.getLogger(HttpSessionWrapper.class);

    private NoSessionContext context;

    private ServletContext servletContext;

    private HttpServletRequest request;

    private Map<String, StoreContext> storeAttribute;

    private Set<String> enumerations;

    private boolean isNew = false;

    private boolean isValid = true;

    public HttpSessionWrapper()
    {
        this.enumerations = new HashSet();
    }

    public void setContext(NoSessionContext context)
    {
        this.context = context;
    }

    public void setServletContext(ServletContext servletContext)
    {
        this.servletContext = servletContext;
    }

    public void setStoreAttribute(Map<String, StoreContext> attributes)
    {
        this.storeAttribute = attributes;
    }

    public void setRequest(HttpServletRequest request)
    {
        this.request = request;
    }

    public void setId(String id)
    {
        setAttribute("jsessionId", id);
    }

    public long getCreationTime()
    {
        return getAttribute("creationTime") == null ? 0L : ((Long)getAttribute("creationTime")).longValue();
    }

    public String getId()
    {
        return (String)getAttribute("jsessionId");
    }

    private void checkInvalid()
    {
        if(false == isValid())
        {
            throw new IllegalStateException("session has been invalidated");
        }
    }

    public boolean isValid()
    {
        return this.isValid;
    }

    public long getLastAccessedTime()
    {
        return getAttribute("lastAccessedTime") == null ? 0L : ((Long)getAttribute("lastAccessedTime")).longValue();
    }

    public ServletContext getServletContext()
    {
        checkInvalid();
        return this.servletContext;
    }

    public void setMaxInactiveInterval(int interval)
    {
    }

    public int getMaxInactiveInterval()
    {
        return this.context.getMaxInactiveInterval();
    }

    @Deprecated
    public HttpSessionContext getSessionContext()
    {
        throw new UnsupportedOperationException("No longer supported method: getSessionContext");
    }

    public Object getAttribute(String key)
    {
        if(!this.context.isSessionKey(key))
        {
            checkInvalid();
        }

        return this.storeAttribute.containsKey(key) ? ((StoreContext)this.storeAttribute.get(key)).getValue() : null;
    }

    public Object getValue(String name)
    {
        checkInvalid();
        return getAttribute(name);
    }

    public Enumeration getAttributeNames()
    {
        return Collections.enumeration(this.enumerations);
    }

    public String[] getValueNames()
    {
        return (String[])this.enumerations.toArray();
    }

    /**
     * 重写setAttribute
     * @param key
     * @param value
     */
    public void setAttribute(String key, Object value)
    {
        checkInvalid();
        if(this.context.isSessionKey(key))
        {
            updateAttribute(key, value);
        }
        if(this.context.isMatchStore(key))
        {
            this.enumerations.add(key);
            updateAttribute(key, value);
        }
    }

    private void updateAttribute(String key, Object value)
    {
        if(this.storeAttribute.containsKey(key))
        {
            ((StoreContext)this.storeAttribute.get(key)).setValue(value);
        }
        else
        {
            this.storeAttribute.put(key, new StoreContext(value, true));
        }
    }

    public void putValue(String name, Object value)
    {
        setAttribute(name, value);
    }

    public void removeAttribute(String key)
    {
        checkInvalid();
        setAttribute(key, null);
    }

    public void removeValue(String name)
    {
        removeAttribute(name);
    }

    public void invalidate()
    {
        this.isValid = false;
        this.context.invalidateStore(getId());
        Iterator<String> it = this.storeAttribute.keySet().iterator();
        while (it.hasNext())
        {
            String key = (String)it.next();
            if(!this.context.isSessionKey(key))
            {
                ((StoreContext)this.storeAttribute.get(key)).setValue(null);
                this.enumerations.remove(key);
            }
        }
    }

    public boolean isNew()
    {
        return this.isNew;
    }

    public boolean timeOut()
    {
        long nowTime = System.currentTimeMillis();
        if((getMaxInactiveInterval() != -1) && (nowTime > getLastAccessedTime() + getMaxInactiveInterval() * 1000))
        {
            return true;
        }
        return false;
    }

    public void setIsNew(boolean b)
    {
        this.isNew = b;
    }

    public HttpSessionWrapper create(boolean createNew)
    {
        fitStoreAttribute();
        this.isNew = false;

        String jesssionid = getId();
        if((createNew) || (StringUtil.isBlank(jesssionid)))
        {
            this.isNew = true;
        }
        else if((StringUtil.isNotBlank(jesssionid)) &&
                (timeOut()))
        {
            if(log.isDebugEnabled())
            {
                log.debug("the session is time out! id =" + getId());
            }
            this.isNew = true;
        }

        if(this.isNew)
        {
            setId(this.context.genSessionID());
            setAttribute("creationTime", Long.valueOf(System.currentTimeMillis()));
            //CSDF攻击待定
         //   setAttribute(CSRFTokenManager.CSRF_TOKEN_FOR_SESSION_ATTR_NAME, CSRFTokenManager.generateCsrfToken());
        }
        setAttribute("lastAccessedTime", Long.valueOf(System.currentTimeMillis()));

        return this;
    }

    private void fitStoreAttribute()
    {
        Map<String, Object> storeData = this.context.getStoreData(this.request);
        for(Entry<String, Object> entry : storeData.entrySet())
        {
            this.storeAttribute.put(entry.getKey(), new StoreContext(entry.getValue()));
        }
    }
}
