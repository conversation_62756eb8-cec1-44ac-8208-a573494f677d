package com.apec.framework.nosession.wrapper;

import com.hundsun.jresplus.common.util.io.BufferedByteArrayOutputStream;

import javax.servlet.ServletOutputStream;
import javax.servlet.WriteListener;
import java.io.IOException;

public class ServletOutputStreamWrapper
    extends ServletOutputStream
{
    private BufferedByteArrayOutputStream bos;

    public ServletOutputStreamWrapper(BufferedByteArrayOutputStream bos)
    {
        this.bos = bos;
    }

    public void write(int b) throws IOException
    {
        this.bos.write(b);
    }

    public void write(byte[] b, int off, int len) throws IOException
    {
        this.bos.write(b, off, len);
    }

    public void write(byte[] b) throws IOException
    {
        this.bos.write(b);
    }

    public void close()
        throws IOException
    {
    }

    public void flush()
        throws IOException
    {
    }

    @Override
    public boolean isReady()
    {
        return false;
    }

    @Override
    public void setWriteListener(WriteListener writeListener)
    {

    }
}
