package com.apec.framework.nosession.wrapper;

import com.apec.framework.nosession.NoSessionContext;
import com.apec.framework.nosession.StoreContext;

import java.util.Map;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import javax.servlet.http.HttpSession;

/**
 * request装饰类
 */
public class RequestWrapper
    extends HttpServletRequestWrapper
{
    private NoSessionContext context;

    private ServletContext servletContext;

    private Map<String, StoreContext> storeAttribute;

    private HttpSessionWrapper _httpSession;

    public RequestWrapper(HttpServletRequest request)
    {
        super(request);
    }

    public void setContext(NoSessionContext context)
    {
        this.context = context;
    }

    public void setServletContext(ServletContext servletContext)
    {
        this.servletContext = servletContext;
    }

    public void setStoreAttribute(Map<String, StoreContext> storeAttributes)
    {
        this.storeAttribute = storeAttributes;
    }

    public HttpSession getSession()
    {
        return getSession(false);
    }

    /**
     * 重写getSession方法，初始化值
     * @param create
     * @return
     */
    public HttpSession getSession(boolean create)
    {
        if((!create) && (this._httpSession != null) && (this._httpSession.isValid()))
        {
            return this._httpSession;
        }

        if(this._httpSession != null)
        {
            this._httpSession.invalidate();
        }
        HttpSessionWrapper session = new HttpSessionWrapper();
        session.setStoreAttribute(this.storeAttribute);
        session.setRequest(this);
        session.setContext(this.context);
        session.setServletContext(this.servletContext);
        this._httpSession = session.create(create);
        //待定csrf攻击
       /*if (this._httpSession.isNew()) {
         setAttribute("csrf_token", CSRFTokenManager.getTokenForSession(this._httpSession));
         setAttribute("refer_csrf_token", CSRFTokenManager.getTokenForSession(this._httpSession));
         }*/
        return this._httpSession;
    }
}
