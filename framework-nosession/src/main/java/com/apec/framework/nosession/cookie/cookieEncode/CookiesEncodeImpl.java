package com.apec.framework.nosession.cookie.cookieEncode;

import com.apec.framework.nosession.cookie.Encode;
import com.apec.framework.nosession.cookie.HessianZipSerializer;
import com.hundsun.jresplus.common.util.StringUtil;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.SerializationException;
import org.springframework.stereotype.Component;

@Component("cookiesEncode")
public class CookiesEncodeImpl
    implements Encode
{
    private String salt;

    public String encode(Object object)
        throws SerializationException
    {
        if(object == null)
        {
            return null;
        }
        String str = new String(Base64.encodeBase64(HessianZipSerializer.encode(object)));

        if(StringUtil.isNotBlank(this.salt))
        {
            String encode_salt = new String(Base64.encodeBase64(HessianZipSerializer.encode(this.salt)));
            return encode_salt + str;
        }
        return str;
    }

    public Object decode(String str) throws SerializationException
    {
        if(StringUtil.isEmpty(str) == true)
        {
            return null;
        }
        if(StringUtil.isNotBlank(this.salt))
        {
            String encode_salt = new String(Base64.encodeBase64(HessianZipSerializer.encode(this.salt)));

            String nStr = str.substring(encode_salt.length());
            return HessianZipSerializer.decode(Base64.decodeBase64(nStr));
        }
        return HessianZipSerializer.decode(Base64.decodeBase64(str));
    }

    public void setSalt(String salt)
    {
        this.salt = salt;
    }
}
