package com.apec.framework.nosession.common.util;

import com.hundsun.jresplus.common.util.StringUtil;
import org.apache.commons.logging.LogFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by l<PERSON><PERSON> on 2018/5/3.
 */
public class SelfDepContext
{
    private static Map<String, String> depClassMap = new HashMap<>();

    public static void put(String key, String value)
    {
        if (StringUtil.isBlank(key))
        {
            return;
        }
        if (value == null)
        {
            return;
        }
        depClassMap.put(key, value);
    }

    public static String get(String key)
    {
        if (StringUtil.isBlank(key))
        {
            return null;
        }
        return (String)depClassMap.get(key);
    }

    public static void remove(String key)
    {
        depClassMap.remove(key);
    }
}
