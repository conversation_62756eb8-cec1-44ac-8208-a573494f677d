package com.apec.framework.nosession.cookie.cookieEncode;

import com.apec.framework.nosession.common.crypto.Crypto;
import com.apec.framework.nosession.common.util.SelfDepContext;
import com.apec.framework.nosession.common.util.SelfUtil;
import com.apec.framework.nosession.cookie.Encode;
import com.apec.framework.nosession.dto.SelfDependence;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang.SerializationException;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Map.Entry;

/**
 * jres兼容melody方式的cookie加解密
 * 
 * 思路：发现jres cookie加密的值类型为Map<'cookie配置key', '序列化对象'>，map的key值为cookie配置的key值，object为需要序列化的对象。<br>
 * 1、加密：根据encode方法的入参，判断map的size大于1为jres内部cookie，size=1，再判断ma的object值是否是melody接口SelfDependence的实例化。<br>
 *      a)如果不是， cookie存储形式：jres@@的头信息 + 对象Map<'cookie配置key', '序列化对象'>用jres加密后的字符串，并将上述字符串再次用melody加密后存于cookie中<br>
 *      b)如果是，则调用SelfDependence.lieDown接口并将返回值用加密后写于cookie，selfDependence中需包含cookie的配置key值，用于解密时newInstance用<br>
 * 2、解密：将加密字符串用melody解密。根据解密后的字符串判断，是否是jres@@开头<br>
 *      a)如果是，则调用jres解密方法，解密出该Map<'cookie配置key', '序列化对象'>，并返回<br>
 *      b)如果不是，则该加密字符串为selfDependence.lieDown所得，并将字符串通过melody的反序列化接口，获得对象的cookie配置key和反序列化后的对象，拼装成Map<'cookie配置key', '序列化对象'>形式并返回
 * <AUTHOR>
 */
@Component("sucCookieEncode")
public class SucCookieEncodeImpl implements Encode
{

	private static Log log = LogFactory.getLog(SucCookieEncodeImpl.class);
	
	private static final String JRES_PREFIX = "jres";

	private static final String SPLIT_CHAR = "@@";

	@Autowired
	private Crypto crypto;
	
	/**
	 * jres的加解密接口
	 */
	private Encode jresEncode = new CookiesEncodeImpl();
	
	// 在值前面增加多少随机数字，如果 <= 0 ,则表示不增加
	@Value("${cookie.random.char:0}")
	private Integer randomChar;
	
	// 字符加解密，缺省为utf8
	private String encoding = "UTF-8";
	
	@Override
	public Object decode(String value) throws SerializationException {
		// 将客户端的cookie值翻译过来
		String decodedStr = getMelodyDecodeStr(value);
		if(decodedStr == null) {
			log.error("SucCookieEncodeImpl.decode : decode str [" + value + "] fail");
			return null;
		}
		//头信息，如果是jres版的cookie加解密，头部是jres，否则是melody加解密的
		boolean isJresEncode = decodedStr.startsWith(JRES_PREFIX);
		try {
			if(isJresEncode) { //jres版加解密
				log.debug("SucCookieEncodeImpl.decode : jres decode called");
				String[] args = decodedStr.split(SPLIT_CHAR);
				if(args == null || args.length != 2) {
					log.error("SucCookieEncodeImpl.decode : jres decode array is not equals 2");
					return null;
				}
				return jresEncode.decode(args[1]);
			} else {
				String[] melodyArgs = SelfUtil.recover(decodedStr);
				if(melodyArgs == null || melodyArgs.length == 0) {
					log.error("SucCookieEncodeImpl.decode : melodyArgs array is null");
					return null;
				}
				String cookieConfKey = melodyArgs[0];
				//数组第一个参数得到实例化对象
				String className = SelfDepContext.get(cookieConfKey);
				log.debug("SucCookieEncodeImpl.decode: melody className :" + className + ", from cookieCnfKey:" + cookieConfKey);
				if(className == null) {
					log.error("SucCookieEncodeImpl.decode:className is null");
					return null;
				}
				SelfDependence selfDepObj = (SelfDependence) Class.forName(className).newInstance();
				Object valueObj = selfDepObj.riseUp(decodedStr);
				Map<String, Object> resMap = new HashMap<String, Object>(); //jresplus要求是map形式，key值为配置的cookie config key
				resMap.put(cookieConfKey, valueObj);
				return resMap;
			}
		} catch (Exception e) {
			log.error("SucCookieEncodeImpl.decode:", e);
			throw new RuntimeException(e);
		}
	}

	@SuppressWarnings("unchecked")
	@Override
	public String encode(Object mapObject) throws SerializationException {
		if(mapObject == null) {
			log.error("SucCookieEncodeImpl.encode : encode value is null");
			return null;
		}
		if(!(mapObject instanceof Map)) {
			log.error("SucCookieEncodeImpl.encode : object value is null, this is not jres type");
			return null;
		}
		Map<String, Object> map = (Map<String, Object>) mapObject;
		if(map == null || map.isEmpty()) {
			return null;
		}
		if(map.size() > 1) { // map的size大于1，肯定不是兼容melody的cookie加密存放，使用jres的cookie加密
			return getMelodyEncodedStr(JRES_PREFIX + SPLIT_CHAR + jresEncode.encode(mapObject));
		}
		String encodeKey = null;
		Object encodeObj = null;
		//map的size为1，可能是实现melody的selfDependence，需要作进一步判断
		Iterator<Entry<String, Object>> iter = map.entrySet().iterator();
		while (iter.hasNext()) {
			Entry<String, Object> entry = iter.next();
			encodeKey = entry.getKey();
			encodeObj = entry.getValue();
		}
		if(encodeObj == null) {
			log.error("SucCookieEncodeImpl.encode : encode object is null");
			return null;
		}
		if(!(encodeObj instanceof SelfDependence)) { //类并非实现melody的selfDependence
			// 使用jresplus默认的
			log.debug("SucCookieEncodeImpl.encode : encode object is not instance of SelfDependence, use jresplus cookie encode");
			return getMelodyEncodedStr(JRES_PREFIX + SPLIT_CHAR + jresEncode.encode(mapObject));
		}
		log.debug("SucCookieEncodeImpl.encode : encode type is instance of SelfDependence, use melody cookie encode");
		//注册到SelfDependence的子类key和类名关联关系中
		SelfDepContext.put(encodeKey, encodeObj.getClass().getName());
		SelfDependence selfDepObj = (SelfDependence) encodeObj;
		String lieDownStr = selfDepObj.lieDown(); //jres里面cookie的对像的key值
		//如果实现melody的selfDependence
		return getMelodyEncodedStr(lieDownStr);
	}
	
	/**
	 * melody的解密
	 * @param back
	 * @return
	 * <AUTHOR>
	 */
	private String getMelodyDecodeStr(String back) {
		if (back == null || back == "") {
			return null;
		}
		if (this.crypto != null) {
			back = this.crypto.dectypt(back, Crypto.Encoding.Base32, encoding);
		}
		if (back == null) {
			return null;
		}
		if (this.randomChar > 0) {
			if (back.length() < this.randomChar) {
				return null;
			}
			back = back.substring(this.randomChar);
		}
		return back;
	}

	/**
	 * melody的加密
	 * @param str
	 * @return
	 * <AUTHOR>
	 */
	private String getMelodyEncodedStr(String str) {
		if (str == "") {
			return "";
		}
		if (this.randomChar > 0) {
			str = RandomStringUtils.randomAlphanumeric(this.randomChar) + str;
		}
		if (this.crypto != null) {
			str = this.crypto.encrypt(str, Crypto.Encoding.Base32, encoding);
		}
		return str;
	}

	public Crypto getCrypto() {
		return crypto;
	}

	public void setCrypto(Crypto crypto) {
		this.crypto = crypto;
	}

	public Integer getRandomChar() {
		return randomChar;
	}

	public void setRandomChar(Integer randomChar) {
		this.randomChar = randomChar;
	}

	public String getEncoding() {
		return encoding;
	}

	public void setEncoding(String encoding) {
		this.encoding = encoding;
	}
}
