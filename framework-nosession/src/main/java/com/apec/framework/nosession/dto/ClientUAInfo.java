package com.apec.framework.nosession.dto;

import com.apec.framework.nosession.common.util.CommonUtils;
import com.apec.framework.nosession.common.util.SelfUtil;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 客户端UserAgent的cookie信息（cookie中记录userId，sessionId和token）
 * 
 * <AUTHOR>
 */
public class ClientUAInfo extends BaseDTO implements SelfDependence
{

	/**
	 * 
	 */
	private static final long serialVersionUID = 6303716917454087278L;

	private static final Logger log = LoggerFactory.getLogger(ClientUAInfo.class);
	
	/**
	 * cookie userAgent信息 key值
	 */
	public static final String CLIENT_UA_INFO = "clientUAInfo";
	
	private String userId;
	
	private String sessionId;
	
	private String userToken;
	
	private String randomUUID;

	@Override
	public String lieDown() {
		String[] arr = new String[5];
		//当前UserAgent类的sessionKey，用于riseUp，从SelfDepContext取注册的类名
		arr[0] = CLIENT_UA_INFO;
		arr[1] = userId;
		arr[2] = sessionId;
		arr[3] = userToken;
		arr[4] = CommonUtils.getRandomUUID();
		return SelfUtil.format(arr);
	}

	@Override
	public SelfDependence riseUp(String value) {
		if(StringUtils.isBlank(value)) {
			log.error("riseUp: value param cannot be null");
			return null;
		}
		String[] values = SelfUtil.recover(value);
		if(ArrayUtils.isEmpty(values)) {
			log.error("rise up array is null for value[{}]", value);
			return null;
		}
		int len = value.length();
		log.debug("ClientUAInfo.rising up from className:" + values[0]);
		int i = 1;
		if(i < len) {
			this.userId = values[i];
		}
		i++;
		
		if(i < len) {
			this.sessionId = values[i];
		}
		i++;
		
		if(i < len) {
			this.userToken = values[i];
		}
		i++;
		
		if(i < len) {
			this.randomUUID = values[i];
		}
		
		return this;
	}

	public String getUserId() {
		return userId;
	}

	public String getSessionId() {
		return sessionId;
	}

	public String getUserToken() {
		return userToken;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}

	public void setUserToken(String userToken) {
		this.userToken = userToken;
	}

	public String getRandomUUID() {
		return randomUUID;
	}

	public void setRandomUUID(String randomUUID) {
		this.randomUUID = randomUUID;
	}
}
