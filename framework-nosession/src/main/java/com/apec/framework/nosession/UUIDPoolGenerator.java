package com.apec.framework.nosession;

import com.hundsun.jresplus.common.util.BlockingQueueLocate;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

@Component
public class UUIDPoolGenerator
    implements UUIDGenerator, InitializingBean, DisposableBean
{
    private static final Logger logger = LoggerFactory.getLogger(UUIDPoolGenerator.class);

    private static final String EventStringPlaceholder = "esp";

    private Lock lock = new ReentrantLock();

    private Condition worker = this.lock.newCondition();

    private BlockingQueue<String> queue = BlockingQueueLocate.createBlockingQueue();

    private Thread producer;

    private boolean fixedLength = false;

    private int capacity = 1000;

    private int threshold = 200;

    public void afterPropertiesSet() throws Exception
    {
        if(this.threshold > this.capacity)
        {
            throw new IllegalArgumentException("threshold can't great than capacity.");
        }

        this.producer = new Thread("UUIDGen")
        {
            public void run()
            {
                int times = capacity / threshold;
                for (int i = 0; i < times; i++) {
                    produce();
                }
                logger.debug("fill the queue with capacity:" + capacity);
                while (!Thread.interrupted()) {
                    lock.lock();
                    try {
                        worker.await();
                        produce();
                    } catch (InterruptedException e) {
                        break;
                    } finally {
                        lock.unlock();
                    }
                }
            }
        };
        this.producer.setDaemon(true);
        this.producer.start();
    }

    public void destroy() throws Exception
    {
        this.producer.interrupt();
    }

    private void produce()
    {
        for(int i = 0; i < this.threshold; i++)
        {
            this.queue.add(this.fixedLength ? RandomShortUUID.getFixSize() : RandomShortUUID.get());
        }

        this.queue.add("esp");
    }

    private void startProduce()
    {
        this.lock.lock();
        try
        {
            this.worker.signal();
        }
        finally
        {
            this.lock.unlock();
        }
    }

    public String gain()
    {
        String one;
        try
        {
            one = (String)this.queue.take();
        }
        catch (InterruptedException e)
        {
            throw new RuntimeException(e);
        }
        if(one != "esp")
        {
            return one;
        }
        startProduce();
        return gain();
    }
}

 