package com.apec.framework.nosession.common.util;


import com.apec.framework.nosession.common.util.digest.MessageDigest;
import com.apec.framework.nosession.common.util.digest.impl.MD5MessageDigestImpl;

import java.util.UUID;

/**
 * Created by l<PERSON><PERSON> on 2018/5/3.
 */
public class CommonUtils
{
    public static String getRandomUUID() {
        return UUID.randomUUID().toString();
    }

    /**
     * md5加密字符串
     * @param str
     * @return
     * <AUTHOR>
     */
    public static String getMd5Str(String str) {
        MessageDigest digest = new MD5MessageDigestImpl();
        return digest.digest(str);
    }

    /**
     * 登录用户服务器sessionId
     * @param userId
     * @return
     * <AUTHOR>
     */
    public static String getUserSessionId(Long userId) {
        return "user" + userId;
    }
}
