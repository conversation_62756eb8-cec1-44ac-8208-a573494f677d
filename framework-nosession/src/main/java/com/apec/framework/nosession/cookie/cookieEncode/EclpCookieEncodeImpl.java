package com.apec.framework.nosession.cookie.cookieEncode;

import com.apec.framework.nosession.common.crypto.Crypto;
import com.apec.framework.nosession.common.util.SelfDepContext;
import com.apec.framework.nosession.common.util.SelfUtil;
import com.apec.framework.nosession.cookie.Encode;
import com.apec.framework.nosession.dto.SelfDependence;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.SerializationException;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * Created by liliwei on 2018/5/5.
 */
@Component("eclpCookiesEncode")
public class EclpCookieEncodeImpl implements Encode
{
    private static Log log = LogFactory.getLog(EclpCookieEncodeImpl.class);
    private static final String JRES_PREFIX = "jres";
    private static final String SPLIT_CHAR = "@@";
    @Autowired
    private Crypto crypto;
    private Encode jresEncode = new CookiesEncodeImpl();
    @Value("${cookie.random.char:0}")
    private Integer randomChar;
    private String encoding = "UTF-8";

    public Object decode(String value)
        throws SerializationException
    {
        String decodedStr = getMelodyDecodeStr(value);
        if (decodedStr == null)
        {
            log.error("EclpCookieEncodeImpl.decode : decode str [" + value + "] fail");
            return null;
        }
        boolean isJresEncode = decodedStr.startsWith("jres");
        try
        {
            if (isJresEncode)
            {
                log.debug("EclpCookieEncodeImpl.decode : jres decode called");
                String[] args = decodedStr.split("@@");
                if ((args == null) || (args.length != 2))
                {
                    log.error("EclpCookieEncodeImpl.decode : jres decode array is not equals 2");
                    return null;
                }
                return this.jresEncode.decode(args[1]);
            }
            String[] melodyArgs = SelfUtil.recover(decodedStr);
            if ((melodyArgs == null) || (melodyArgs.length == 0))
            {
                log.error("EclpCookieEncodeImpl.decode : melodyArgs array is null");
                return null;
            }
            String cookieConfKey = melodyArgs[0];

            String className = SelfDepContext.get(cookieConfKey);
            log.debug("EclpCookieEncodeImpl.decode: melody className :" + className + ", from cookieCnfKey:" + cookieConfKey);
            if (className == null)
            {
                log.error("EclpCookieEncodeImpl.decode:className is null");
                return null;
            }
            SelfDependence selfDepObj = (SelfDependence)Class.forName(className).newInstance();
            Object valueObj = selfDepObj.riseUp(decodedStr);
            Map<String, Object> resMap = new HashMap<>();
            resMap.put(cookieConfKey, valueObj);
            return resMap;
        }
        catch (Exception e)
        {
            log.error("EclpCookieEncodeImpl.decode:", e);
            throw new RuntimeException(e);
        }
    }

    public String encode(Object mapObject)
        throws SerializationException
    {
        if (mapObject == null)
        {
            log.error("EclpCookieEncodeImpl.encode : encode value is null");
            return null;
        }
        if (!(mapObject instanceof Map))
        {
            log.error("EclpCookieEncodeImpl.encode : object value is null, this is not jres type");
            return null;
        }
        Map<String, Object> map = (Map)mapObject;
        if ((map == null) || (map.isEmpty())) {
            return null;
        }
        if (map.size() > 1) {
            return getMelodyEncodedStr("jres@@" + this.jresEncode.encode(mapObject));
        }
        String encodeKey = null;
        Object encodeObj = null;

        Iterator<Map.Entry<String, Object>> iter = map.entrySet().iterator();
        while (iter.hasNext())
        {
            Map.Entry<String, Object> entry = (Map.Entry)iter.next();
            encodeKey = (String)entry.getKey();
            encodeObj = entry.getValue();
        }
        if (encodeObj == null)
        {
            log.error("EclpCookieEncodeImpl.encode : encode object is null");
            return null;
        }
        if (!(encodeObj instanceof SelfDependence))
        {
            log.debug("EclpCookieEncodeImpl.encode : encode object is not instance of SelfDependence, use jresplus cookie encode");
            return getMelodyEncodedStr("jres@@" + this.jresEncode.encode(mapObject));
        }
        log.debug("EclpCookieEncodeImpl.encode : encode type is instance of SelfDependence, use melody cookie encode");

        SelfDepContext.put(encodeKey, encodeObj.getClass().getName());
        SelfDependence selfDepObj = (SelfDependence)encodeObj;
        String lieDownStr = selfDepObj.lieDown();

        return getMelodyEncodedStr(lieDownStr);
    }

    private String getMelodyDecodeStr(String back)
    {
        if ((back == null) || (back == "")) {
            return null;
        }
        if (this.crypto != null) {
            back = this.crypto.dectypt(back, Crypto.Encoding.Base32, this.encoding);
        }
        if (back == null) {
            return null;
        }
        if (this.randomChar.intValue() > 0)
        {
            if (back.length() < this.randomChar.intValue()) {
                return null;
            }
            back = back.substring(this.randomChar.intValue());
        }
        return back;
    }

    private String getMelodyEncodedStr(String str)
    {
        if (str == "") {
            return "";
        }
        if (this.randomChar.intValue() > 0) {
            str = RandomStringUtils.randomAlphanumeric(this.randomChar.intValue()) + str;
        }
        if (this.crypto != null) {
            str = this.crypto.encrypt(str, Crypto.Encoding.Base32, this.encoding);
        }
        return str;
    }

    public Crypto getCrypto()
    {
        return this.crypto;
    }

    public void setCrypto(Crypto crypto)
    {
        this.crypto = crypto;
    }

    public Integer getRandomChar()
    {
        return this.randomChar;
    }

    public void setRandomChar(Integer randomChar)
    {
        this.randomChar = randomChar;
    }

    public String getEncoding()
    {
        return this.encoding;
    }

    public void setEncoding(String encoding)
    {
        this.encoding = encoding;
    }
}
