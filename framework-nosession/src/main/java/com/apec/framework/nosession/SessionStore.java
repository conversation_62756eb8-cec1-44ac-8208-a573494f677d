package com.apec.framework.nosession;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.Cache;
import org.springframework.cache.Cache.ValueWrapper;
import org.springframework.cache.CacheManager;
import org.springframework.util.CollectionUtils;

public class SessionStore
    implements AttributeStore
{
    private static final Logger log = LoggerFactory.getLogger(SessionStore.class);

    private String SESSION_CACHE_NAME = "SessionCache";

    private Cache cache;

    private CacheManager cacheManager;

    public static Set<String> attributeNames = new HashSet();

    public CacheManager getCacheManager()
    {
        return this.cacheManager;
    }

    public void setCacheManager(CacheManager cacheManager)
    {
        this.cacheManager = cacheManager;
    }

    public int getOrder()
    {
        return 1;
    }

    public void invalidate(String sessionId)
    {
    }

    public Set<String> getAttributeNames()
    {
        return attributeNames;
    }

    public void setAttributeNames(Set<String> value)
    {
        attributeNames = value;
    }

    public boolean isMatch(String key)
    {
        if("jsessionId".equals(key))
        {
            return true;
        }
        return attributeNames.contains(key);
    }

    public Map<String, Object> loadValue(String sessionId)
    {
        Cache cache = getCache();
        if(null == cache)
        {
            return new HashMap();
        }
        ValueWrapper value = cache.get(sessionId);

        if(null == value)
        {
            return new HashMap();
        }
        return (Map)value.get();
    }

    public void setValue(String sessionId, Map<String, StoreContext> values)
    {
        Cache cache = getCache();
        if(null == cache)
        {
            return;
        }
        if((null == values) || (CollectionUtils.isEmpty(values.keySet())))
        {
            return;
        }
        Map<String, Object> cacheMap = new HashMap();
        for(String key : values.keySet())
        {
            StoreContext value = (StoreContext)values.get(key);
            cacheMap.put(key, value.getValue());
        }
        cache.put(sessionId, cacheMap);
    }

    private Cache getCache()
    {
        if(null == this.cache)
        {
            this.cache = getCacheManager().getCache(this.SESSION_CACHE_NAME);
            if(null == this.cache)
            {
                log.error("For the cache manager failed, check the cache configuration!");
            }
        }
        return this.cache;
    }
}