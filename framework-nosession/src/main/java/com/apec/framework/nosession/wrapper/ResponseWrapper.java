
package com.apec.framework.nosession.wrapper;

import com.apec.framework.nosession.NoSessionContext;
import com.apec.framework.nosession.ServerCookie;
import com.apec.framework.nosession.StoreContext;
import com.hundsun.jresplus.common.util.io.BufferedByteArrayOutputStream;

import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletResponseWrapper;

/**
 * Response装饰类
 */
public class ResponseWrapper
    extends HttpServletResponseWrapper

{
    private Map<String, StoreContext> storeAttribute = null;

    private HttpServletResponse response;

    private HttpServletRequest request;

    private boolean _commited = false;

    private Set<javax.servlet.http.Cookie> _cookies = new HashSet();

    private BufferedByteArrayOutputStream _outputStream;

    private ServletOutputStreamWrapper _servletOutputStream;

    private PrintWriter _pw;

    private int recyclingBufferBlockSize = 2;

    private NoSessionContext context;

    public ResponseWrapper(HttpServletResponse response)
    {

        super(response);

        this.response = response;

    }

    public void init() throws IOException

    {

        this._outputStream = NoSessionContext.getOurputStream();

        this._pw = new PrintWriter(new OutputStreamWriter(this._outputStream, this.context.getOutCharset()));

        this._servletOutputStream = new ServletOutputStreamWrapper(this._outputStream);

    }

    public void setStoreAttributes(Map<String, StoreContext> attributes)
    {

        this.storeAttribute = attributes;

    }

    public void setRequest(HttpServletRequest request)
    {

        this.request = request;

    }

    public void setContext(NoSessionContext context)
    {

        this.context = context;

    }

    public void addCookie(javax.servlet.http.Cookie cookie)

    {

        if((isCommitted()) || (this._commited == true))
        {

            return;

        }

        Iterator<javax.servlet.http.Cookie> it = this._cookies.iterator();

        while (it.hasNext() == true)
        {

            javax.servlet.http.Cookie cookieTemp = (javax.servlet.http.Cookie)it.next();

            if(cookieTemp.getName().equals(cookie.getName()))
            {

                this._cookies.remove(cookieTemp);

                it = this._cookies.iterator();

            }

        }

        this._cookies.add(cookie);

    }

    private void cookiesCommit()
    {

        if((this._commited) || (isCommitted()))
        {

            return;

        }

        if(this.request.getSession() != null)

        {

            this.context.updateCookieStore(this.request, this, this.storeAttribute);

        }

        for(javax.servlet.http.Cookie cookie : this._cookies)
        {

            if((cookie instanceof com.apec.framework.nosession.cookie.Cookie))
            {

                addHeader(getCookieHeaderName(cookie),
                          getCookieHeaderValue((com.apec.framework.nosession.cookie.Cookie)cookie));

            }

            else

            {

                super.addCookie(cookie);

            }

        }

        this._commited = true;

    }

    public void flushBuffer() throws IOException

    {

        try
        {

            cookiesCommit();

            ServletOutputStream sos = super.getOutputStream();

            this._pw.flush();

            this._outputStream.writeTo(sos);

            sos.flush();

        }
        finally
        {

            resetBufferedOutputStream();

        }

    }

    private void resetBufferedOutputStream()
    {

        this._outputStream.reset(this.recyclingBufferBlockSize);

    }

    public void reset()

    {

        resetBufferedOutputStream();

    }

    public void resetBuffer()

    {

        resetBufferedOutputStream();

    }

    public int getBufferSize()

    {

        return Integer.MAX_VALUE;

    }

    private String getCookieHeaderName(javax.servlet.http.Cookie cookie)
    {

        return ServerCookie.getCookieHeaderName(cookie.getVersion());

    }

    private String getCookieHeaderValue(com.apec.framework.nosession.cookie.Cookie cookie)
        throws IllegalArgumentException

    {

        return appendCookieHeaderValue(new StringBuilder(), cookie).toString();

    }

    private StringBuilder appendCookieHeaderValue(StringBuilder buf,
        com.apec.framework.nosession.cookie.Cookie cookie)
        throws IllegalArgumentException

    {

        ServerCookie.appendCookieValue(buf, cookie.getVersion(), cookie.getName(), cookie.getValue(), cookie.getPath(),
                                       cookie.getDomain(), cookie.getComment(), cookie.getMaxAge(), cookie.getSecure(),
                                       cookie.isHttpOnly());

        return buf;

    }

    public void sendRedirect(String location) throws IOException

    {

        cookiesCommit();

        super.sendRedirect(location);

    }

    public ServletOutputStream getOutputStream() throws IOException

    {

        return this._servletOutputStream;

    }

    public PrintWriter getWriter() throws IOException

    {

        return this._pw;

    }

    public void setBufferSize(int size)
    {
    }

}
