package com.apec.framework.nosession;

import java.util.Map;
import java.util.Set;

import org.springframework.core.Ordered;

public abstract interface AttributeStore
    extends Ordered
{
    public abstract void invalidate(String paramString);

    public abstract Set<String> getAttributeNames();

    public abstract boolean isMatch(String paramString);

    public abstract Map<String, Object> loadValue(String paramString);

    public abstract void setValue(String paramString, Map<String, StoreContext> paramMap);
}
