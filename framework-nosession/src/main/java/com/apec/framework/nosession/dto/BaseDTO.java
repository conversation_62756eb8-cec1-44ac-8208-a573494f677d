/*
 * Hundsun Inc.
 * Copyright (c) 2010-2015 All Rights Reserved.
 *
 * Author     :yangzl
 * Version    :1.0
 * Create Date:2012-7-13
 */
package com.apec.framework.nosession.dto;


import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

/**
 * 共享类的数据传输其类，主要是封装便于jvm之间数据传输对象包的基类
 * 以便于统一与标准化管理。
 * <br>
 * DTO 包含需要共享给其它系统共用信息封装对象，BaseDTO不等同于BaseObject类
 * <AUTHOR>
 * @version $Id: BaseDTO.java,v 0.1 2012-7-13 下午1:58:23 yangzl Exp $
 */
public class BaseDTO implements Serializable {

	private static final long serialVersionUID = 3561932422411139402L;

	public BaseDTO() {

	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}

	@Override
	public boolean equals(Object obj) {
		return EqualsBuilder.reflectionEquals(this, obj);
	}

	@Override
	public int hashCode() {
		return HashCodeBuilder.reflectionHashCode(this);
	}
}
