package com.apec.framework.nosession.cookie.bean;

import com.apec.framework.nosession.cookie.AttributeCookieStore;
import com.apec.framework.nosession.cookie.Encode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;

/**
 * cookieName:_cc_ 封装類
 * Created by l<PERSON><PERSON> on 2018/5/5.
 */
@Component
public class EclpSessionAgentAttributeStore implements AttributeCookieStore
{
    private Set<String> keyNames = new HashSet<>();
    @Value("${eclp.cookieName:_sc_}")
    private String cookieName;
    private String path = "/";
    private int maxInactiveInterval = -1;
    @Autowired
    private Encode eclpCookiesEncode;

    {
        keyNames.add("sessionAgent");
    }


    public Set<String> getKeyNames()
    {
        return this.keyNames;
    }

    public void setKeyNames(Set<String> keyNames)
    {
        this.keyNames = keyNames;
    }

    public void setCookieName(String cookieName)
    {
        this.cookieName = cookieName;
    }

    public void setPath(String path)
    {
        this.path = path;
    }

    public void setMaxInactiveInterval(int maxInactiveInterval)
    {
        this.maxInactiveInterval = maxInactiveInterval;
    }

    public Set<String> getAttributeNames()
    {
        return this.keyNames;
    }

    @Override
    public String getDomain()
    {
        return null;
    }

    public boolean isMatch(String key)
    {
        return this.keyNames.contains(key);
    }

    public int getOrder()
    {
        return 10;
    }

    public int getMaxInactiveInterval()
    {
        return this.maxInactiveInterval;
    }

    public String getCookieName()
    {
        return this.cookieName;
    }

    public String getPath()
    {
        return this.path;
    }

    public void setEncode(Encode encode)
    {
        this.eclpCookiesEncode = encode;
    }

    public Encode getEncode()
    {
        return this.eclpCookiesEncode;
    }
}
