package com.apec.framework.nosession.common.util.digest.impl;

import org.springframework.util.DigestUtils;

/**
 * MD5信息摘要实现类
 * 
 * <AUTHOR>
 * @version $Id: MD5MessageDigestImpl.java,v 0.1 2010-6-10 下午05:09:24 zhengdd
 *          Exp $
 */
public final class MD5MessageDigestImpl extends AbstractMessageDigestImpl
{

	/**
	 * MD5信息摘要算法实现, 采用Apache Commons的codec包的MD5实现
	 * 
	 * @param bytes
	 * @return byte[]
	 *
	 */
	@Override
	protected byte[] digestInternal(byte[] bytes) {
		if (bytes == null) {
			throw new IllegalArgumentException("paramter bytes can't be null");
		}
		return DigestUtils.md5Digest(bytes);
	}

}
