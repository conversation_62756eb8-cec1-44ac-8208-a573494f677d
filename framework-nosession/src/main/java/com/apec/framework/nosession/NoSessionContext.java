package com.apec.framework.nosession;

import com.apec.framework.nosession.cookie.*;
import com.apec.framework.nosession.cookie.bean.SessionToken;
import com.hundsun.jresplus.beans.ObjectFactory;
import com.hundsun.jresplus.common.util.ArrayUtil;
import com.hundsun.jresplus.common.util.StringUtil;
import com.hundsun.jresplus.common.util.io.BufferedByteArrayOutputStream;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.SerializationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

/**
 * 自定义session上下文
 */
@Component
public class NoSessionContext
    implements InitializingBean
{
    private static final Logger log = LoggerFactory.getLogger(NoSessionContext.class);

    public static final ThreadLocal<BufferedByteArrayOutputStream> outputStreams = new ThreadLocal();

    @Autowired
    private ObjectFactory objectFactory;

    @Autowired
    private CookiesManager cookiesManager;

    private static int outBufferSize = 5120;

    @Value("${response.out.charset:UTF-8}")
    private String outCharset;

    @Autowired
    private UUIDGenerator uuidGenerator;

    @Value("${nosession.https}")
    private boolean https;

    SessionToken sessionToken;

    List<AttributeCookieStore> attributeCookieStores = new ArrayList();

    List<AttributeStore> attributeStores = new ArrayList();

    public void setHttps(boolean https)
    {
        this.https = https;
    }

    public static BufferedByteArrayOutputStream getOurputStream()
    {
        if(outputStreams.get() == null)
        {
            outputStreams.set(new BufferedByteArrayOutputStream(outBufferSize));
        }
        return (BufferedByteArrayOutputStream)outputStreams.get();
    }

    public void setObjectFactory(ObjectFactory objectFactory)
    {
        this.objectFactory = objectFactory;
    }

    public void setUuidGenerator(UUIDGenerator uuidGenerator)
    {
        this.uuidGenerator = uuidGenerator;
    }

    public void setOutCharset(String outCharset)
    {
        this.outCharset = outCharset;
    }

    public void setCookiesManager(CookiesManager cookiesManager)
    {
        this.cookiesManager = cookiesManager;
    }

    public boolean isSessionKey(String key)
    {
        return SessionToken.keyNames.contains(key);
    }

    public void afterPropertiesSet() throws Exception
    {
        List<AttributeCookieStore> list1 = this.objectFactory.getBeansOfType4List(AttributeCookieStore.class);

        Set<String> attrs = new HashSet();
        if(!ArrayUtil.isEmpty(list1))
            for(AttributeCookieStore store : list1)
            {
                this.attributeCookieStores.add(store);
                if(store.getAttributeNames() != null)
                {

                    if((store instanceof SessionToken))
                    {
                        this.sessionToken = ((SessionToken)store);
                    }
                    attrs.addAll(store.getAttributeNames());
                }
            }
        Assert.notNull(this.sessionToken, "session store min data[SessionToken] losed");

        List<AttributeStore> list2 = this.objectFactory.getBeansOfType4List(AttributeStore.class);

        if(!ArrayUtil.isEmpty(list2))
        {
            for(AttributeStore store : list2)
            {
                this.attributeStores.add(store);
                if(store.getAttributeNames() != null)
                {

                    attrs.addAll(store.getAttributeNames());
                }
            }
        }
        StringBuffer attrStr = new StringBuffer("");
        for(String key : attrs)
        {
            attrStr.append(key).append(",");
        }
        log.info("Nosession store registed attribute[{}]", attrStr);
    }

    public void updateCookieStore(HttpServletRequest request, HttpServletResponse response,
        Map<String, StoreContext> attributes)
    {
        attributes.put("sessionKeys", new StoreContext(attributes.keySet(), true));

        for(AttributeCookieStore attributeStore : this.attributeCookieStores)
        {
            boolean isNotModified = isNotModified(attributes, attributeStore);

            if((!isNotModified) || (attributeStore.getMaxInactiveInterval() != -1))
            {

                Map<String, Object> storeData = getMatchedStoreData(attributes, attributeStore);

                writeCookie(request, response, attributeStore, storeData);
            }
        }
    }

    private boolean isNotModified(Map<String, StoreContext> attributes, AttributeCookieStore attributeStore)
    {
        for(Entry<String, StoreContext> entry : attributes.entrySet())
        {
            if(attributeStore.isMatch((String)entry.getKey()))
            {

                if(((StoreContext)entry.getValue()).isModified())
                    return false;
            }
        }
        return true;
    }

    private Map<String, Object> getMatchedStoreData(Map<String, StoreContext> attributes,
        AttributeCookieStore attributeStore)
    {
        Map<String, Object> storeData = new HashMap();
        for(Entry<String, StoreContext> entry : attributes.entrySet())
        {
            if(attributeStore.isMatch((String)entry.getKey()))
            {

                if(((StoreContext)entry.getValue()).getValue() != null)
                {
                    storeData.put(entry.getKey(), ((StoreContext)entry.getValue()).getValue());
                }
            }
        }
        return storeData;
    }

    private void writeCookie(HttpServletRequest request, HttpServletResponse response,
        AttributeCookieStore attributeStore, Map<String, Object> tmp)
    {
        try
        {
            int cookieTime = 0;
            String cookieName = attributeStore.getCookieName();
            String cookiePath = attributeStore.getPath();

            String cookieDomain = attributeStore.getDomain();
            boolean httpOnly = true;
            String cookieValue = null;
            if(tmp.size() > 0)
            {
                cookieValue = attributeStore.getEncode().encode(tmp);
                cookieTime = attributeStore.getMaxInactiveInterval();
            }
            Cookie cookie = new Cookie(cookieName, cookieValue, httpOnly, cookieTime, cookiePath, cookieDomain);

            if(this.https)
            {
                cookie.setSecure(true);
            }
            this.cookiesManager.writeCookie(request, response, cookie);
        }
        catch (SessionEncoderException e)
        {
            log.error("write cookie store error !", e);
        }
        catch (SerializationException e)
        {
            log.error("write cookie store error !", e);
        }
    }

    public void updateAttributeStore(Map<String, StoreContext> attributes)
    {
        StoreContext sessionStore = (StoreContext)attributes.get("jsessionId");
        String sessionId = (String)sessionStore.getValue();
        for(AttributeStore store : this.attributeStores)
        {
            store.setValue(sessionId, getMatchedStoreData(attributes, store));
        }
    }

    private Map<String, StoreContext> getMatchedStoreData(Map<String, StoreContext> attributes,
        AttributeStore attributeStore)
    {
        Map<String, StoreContext> storeData = new HashMap();
        for(Entry<String, StoreContext> entry : attributes.entrySet())
        {
            if(attributeStore.isMatch((String)entry.getKey()))
            {

                storeData.put(entry.getKey(), entry.getValue());
            }
        }
        return storeData;
    }

    public boolean isMatchStore(String key)
    {
        for(AttributeCookieStore store : this.attributeCookieStores)
        {
            if(store.isMatch(key))
            {
                return true;
            }
        }
        for(AttributeStore store : this.attributeStores)
        {
            if(store.isMatch(key))
            {
                return true;
            }
        }
        return false;
    }

    public String getOutCharset()
    {
        return this.outCharset;
    }

    public int getMaxInactiveInterval()
    {
        return this.sessionToken.getMaxInactiveInterval();
    }

    public String genSessionID()
    {
        return this.uuidGenerator.gain();
    }

    public void invalidateStore(String sessionId)
    {
        for(AttributeStore store : this.attributeStores)
        {
            store.invalidate(sessionId);
        }
    }

    public Map<String, Object> getStoreData(HttpServletRequest request)
    {
        Map<String, Object> storeData = new HashMap();
        String sessionId = "";
        for(AttributeCookieStore store : this.attributeCookieStores)
        {
            Encode storeEncode = store.getEncode();
            String storeCookieName = store.getCookieName();
            String cookieValue = this.cookiesManager.readCookieValue(request, storeCookieName);

            if(!StringUtil.isEmpty(cookieValue))
            {
                try
                {

                    Map<String, Object> map = (Map)storeEncode.decode(cookieValue);

                    if(map != null)
                    {

                        if(map.containsKey("jsessionId"))
                        {
                            sessionId = (String)map.get("jsessionId");
                        }
                        storeData.putAll(map);
                    }
                }
                catch (Exception e)
                {
                    log.error("Get data from cookie error[{}]", e.getMessage());
                }
            }
        }
        if(StringUtil.isBlank(sessionId))
        {
            return storeData;
        }
        for(AttributeStore store : this.attributeStores)
        {
            Map<String, Object> map = store.loadValue(sessionId);
            if(map != null)
            {
                storeData.putAll(map);
            }
        }

        return storeData;
    }

    public Map<String, Object> getSessionTokenData(HttpServletRequest request)
    {
        String cookieValue = this.cookiesManager.readCookieValue(request, this.sessionToken.getCookieName());

        if(StringUtil.isBlank(cookieValue))
        {
            return null;
        }
        try
        {
            return (Map)this.sessionToken.getEncode().decode(cookieValue);
        }
        catch (Exception e)
        {
            log.error("SessionToken cookieValue decode error[{}].", e.getMessage());
        }

        return null;
    }

    public String getSessionIdFormStore(HttpServletRequest request)
    {
        Map<String, Object> map = getSessionTokenData(request);
        if(map == null)
        {
            return null;
        }
        return (String)map.get("jsessionId");
    }
}

