package com.apec.framework.nosession;

import com.apec.framework.common.constant.FrameConsts;
import com.apec.framework.nosession.common.util.SelfDepContext;
import com.apec.framework.nosession.dto.ClientUAInfo;
import com.apec.framework.nosession.wrapper.RequestWrapper;
import com.apec.framework.nosession.wrapper.ResponseWrapper;

import java.io.IOException;
import java.net.SocketException;
import java.util.HashMap;
import java.util.Map;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hundsun.eclp.biz.domain.user.UserAgent;
import com.hundsun.eclp.client.common.GenericUserAgent;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

/**
 * 该过滤器用来装饰session，request，response
 * Created by liliwei on 2018/5/2.
 */
//@Component("noSessionFilter")
public class NoSessionFilter
    extends OncePerRequestFilter
    implements Filter
{
    private static final Logger log = LoggerFactory.getLogger(NoSessionFilter.class);

    @Value("${nosession.onoff}")
    private boolean onoff = true;

    @Autowired
    NoSessionContext context;

    public void setOnoff(boolean onoff)
    {
        this.onoff = onoff;
    }

    public void setContext(NoSessionContext context)
    {
        this.context = context;
    }

    @Override
    public void afterPropertiesSet() throws ServletException
    {
        //super.afterPropertiesSet();
        SelfDepContext.put(ClientUAInfo.CLIENT_UA_INFO, ClientUAInfo.class.getName());
        //注册eclp的userAgent到SelfDependence的key和类名关联关系中
        SelfDepContext.put(GenericUserAgent.ECLP_USER_AGENT, GenericUserAgent.class.getName());
        SelfDepContext.put(UserAgent.SESSION_AGENT, UserAgent.class.getName());
    }

    protected void doFilterInternal(HttpServletRequest servletRequest, HttpServletResponse servletResponse,
        FilterChain filterChain)
        throws ServletException, IOException
    {
        //不是恒生的不走
        String type = servletRequest.getHeader(FrameConsts.SESSION_VALIDATE_TYPE);
        String servletPath = servletRequest.getServletPath();
        //占时通过后缀校验.magpei
        /*if(StringUtils.isNotEmpty(type)&&!FrameConsts.DZ_SESSION_VALIDATE_APP.equals(type)){*/
        if(servletPath.contains(FrameConsts.MAGPIE_SESSION_VALIDATE)){
            if(this.onoff)
            {
                Map<String, StoreContext> storeAttribute = new HashMap();

                RequestWrapper request = new RequestWrapper(servletRequest);
                request.setStoreAttribute(storeAttribute);
                request.setContext(this.context);
                request.setServletContext(getServletContext());

                ResponseWrapper response = new ResponseWrapper(servletResponse);
                response.setRequest(request);
                response.setContext(this.context);
                response.setStoreAttributes(storeAttribute);
                response.init();

                boolean isFlush = true;
                try
                {
                    filterChain.doFilter(request, response);
                }
                catch (Throwable e)
                {
                    isFlush = false;
                    throw new ServletException(e);
                }
                finally
                {
                    if(request.getSession() != null)
                    {
                        this.context.updateAttributeStore(storeAttribute);
                    }
                    try
                    {
                        if(isFlush)
                        {
                            response.flushBuffer();
                        }
                        else
                        {
                            response.reset();
                        }
                    }
                    catch (Exception e)
                    {
                        String err = e.getMessage();
                        if((e.getCause() != null) && ((e.getCause() instanceof SocketException)))
                        {
                            err = e.getCause().getMessage();
                        }
                        log.error("response flush or reset failed[{}]", err);
                    }
                    storeAttribute.clear();
                }
            }
            else
            {
                filterChain.doFilter(servletRequest, servletResponse);
            }
        }else {
            filterChain.doFilter(servletRequest, servletResponse);
        }
    }
}
