package com.apec.framework.nosession;

import java.io.Serializable;

public class StoreContext
    implements Serializable
{
    private static final long serialVersionUID = 1L;

    private Object value;

    private boolean modified;

    public StoreContext()
    {
        this.value = null;
        this.modified = false;
    }

    public StoreContext(Object value)
    {
        this.value = value;
        this.modified = false;
    }

    public StoreContext(Object value, boolean modified)
    {
        this.value = value;
        this.modified = modified;
    }

    public Object getValue()
    {
        return this.value;
    }

    public void setValue(Object value)
    {
        this.modified = true;
        this.value = value;
    }

    public boolean isModified()
    {
        return this.modified;
    }

    public void setModified(boolean modified)
    {
        this.modified = modified;
    }
}