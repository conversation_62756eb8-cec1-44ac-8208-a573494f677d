package com.apec.framework.nosession.cookie;

import com.caucho.hessian.io.Hessian2Input;
import com.caucho.hessian.io.Hessian2Output;
import org.apache.commons.lang.SerializationException;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.zip.Deflater;
import java.util.zip.DeflaterOutputStream;
import java.util.zip.Inflater;
import java.util.zip.InflaterInputStream;


public class HessianZipSerializer
{
    public static byte[] encode(Object object)
        throws SerializationException
    {
        if(object == null)
        {
            return null;
        }
        ByteArrayOutputStream baos = new ByteArrayOutputStream();

        Deflater def = new Deflater(9, false);
        DeflaterOutputStream dos = new DeflaterOutputStream(baos, def);

        Hessian2Output ho = null;
        try
        {
            ho = new Hessian2Output(dos);
            ho.writeObject(object);
        }
        catch (Exception e)
        {
            throw new SerializationException("Failed to encode date", e);
        }
        finally
        {
            if(ho != null)
            {
                try
                {
                    ho.close();
                }
                catch (IOException e)
                {
                }
            }
            try
            {
                dos.close();
            }
            catch (IOException e)
            {
            }

            def.end();
        }

        return baos.toByteArray();
    }

    public static Object decode(byte[] encodedValue) throws SerializationException
    {
        ByteArrayInputStream bais = new ByteArrayInputStream(encodedValue);
        Inflater inf = new Inflater(false);
        InflaterInputStream iis = new InflaterInputStream(bais, inf);
        Hessian2Input hi = null;
        try
        {
            hi = new Hessian2Input(iis);
            return hi.readObject();
        }
        catch (Exception e)
        {
            throw new SerializationException("Failed to parse data", e);
        }
        finally
        {
            if(hi != null)
            {
                try
                {
                    hi.close();
                }
                catch (IOException e)
                {
                }
            }
            try
            {
                iis.close();
            }
            catch (IOException e)
            {
            }
            inf.end();
        }
    }
}

 