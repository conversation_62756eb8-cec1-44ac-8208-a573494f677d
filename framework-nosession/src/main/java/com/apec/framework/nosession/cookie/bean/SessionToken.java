package com.apec.framework.nosession.cookie.bean;

import com.apec.framework.nosession.cookie.AttributeCookieStore;
import com.apec.framework.nosession.cookie.Encode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;

/**
 * sessionId装饰类
 */
@Component
public class SessionToken
    implements AttributeCookieStore
{
    public static final String CREATION_TIME = "creationTime";

    public static final String LAST_ACCESSED_TIME = "lastAccessedTime";

    public static final String SESSION_ID = "jsessionId";

    public static final String KEYS = "sessionKeys";

    public static Set<String> keyNames = new HashSet();

    static
    {
        keyNames.add("creationTime");
        keyNames.add("jsessionId");
        keyNames.add("lastAccessedTime");
        keyNames.add("sessionKeys");
    }

    @Value("${session.meta.cookie.path}")
    private String cookiepath;

    @Value("${session.meta.cookie.domain}")
    private String metaDomain;

    @Value("${session.meta.cookie.name}")
    private String metaCookieName;

    @Value("${session.max.inacterval}")
    private int maxInactiveInterval = -1;

    @Autowired
    private Encode cookiesEncode;

    public String getCookiepath()
    {
        return this.cookiepath;
    }

    public void setCookiepath(String cookiepath)
    {
        this.cookiepath = cookiepath;
    }

    public void setEncode(Encode encode)
    {
        this.cookiesEncode = encode;
    }

    public void setMetaDomain(String metaDomain)
    {
        this.metaDomain = metaDomain;
    }

    public String getMetaDomain()
    {
        return this.metaDomain;
    }

    public void setMetaCookieName(String metaCookieName)
    {
        this.metaCookieName = metaCookieName;
    }

    public int getOrder()
    {
        return 0;
    }

    public boolean isMatch(String key)
    {
        return keyNames.contains(key);
    }

    public String getCookieName()
    {
        return this.metaCookieName;
    }

    public Encode getEncode()
    {
        return this.cookiesEncode;
    }

    public void setMaxInactiveInterval(int maxInactiveInterval)
    {
        this.maxInactiveInterval = maxInactiveInterval;
    }

    public int getMaxInactiveInterval()
    {
        return this.maxInactiveInterval;
    }

    public String getPath()
    {
        return this.cookiepath;
    }

    public Set<String> getAttributeNames()
    {
        return keyNames;
    }

    public String getDomain()
    {
        return this.metaDomain;
    }
}
