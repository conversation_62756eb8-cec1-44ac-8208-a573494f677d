package com.hundsun.eclp.client.common;

/**
 * Created by l<PERSON><PERSON> on 2018/4/28.
 */
public class HsHandleUtils
{

    public static String parseDomainByUrl(String url)
    {
        if (org.apache.commons.lang.StringUtils.isBlank(url))
        {
            return null;
        }
        if (!url.endsWith("/")) {
            url = url + "/";
        }
        boolean isSSL = url.startsWith("https");
        if ((url.startsWith("http://localhost")) || (url.startsWith("https://localhost")))
        {
            return null;
        }
        int startIndex = url.indexOf(".");
        int slashIndex = -1;
        int colonIndex = -1;
        if (isSSL)
        {
            slashIndex = url.indexOf("/", "https://".length() + 1);
            colonIndex = url.indexOf(":", "https://".length() + 1);
        }
        else
        {
            slashIndex = url.indexOf("/", "http://".length() + 1);
            colonIndex = url.indexOf(":", "http://".length() + 1);
        }
        int endIndex = colonIndex > 0 ? colonIndex : slashIndex;
        String domain = url.substring(startIndex, endIndex);
        return domain;
    }
}
