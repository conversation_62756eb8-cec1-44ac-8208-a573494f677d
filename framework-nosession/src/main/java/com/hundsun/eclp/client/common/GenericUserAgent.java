package com.hundsun.eclp.client.common;

import com.apec.framework.nosession.common.util.SelfUtil;
import com.apec.framework.nosession.dto.SelfDependence;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.io.Serializable;
import java.math.BigInteger;
import java.sql.Date;
import java.util.HashMap;
import java.util.Map;

public class GenericUserAgent implements SelfDependence, Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -3315587920659890231L;

	private static Log _log = LogFactory.getLog(GenericUserAgent.class);
	
	/**
	 * eclp userAgent信息 key值
	 */
	public static final String ECLP_USER_AGENT = "eclpUserAgent";
	
	public static final String USER_AGENT = "userAgent";
	
	/**
	 * 基础userAgent的lieDown数组个数
	 */
	protected static final Integer BASE_LIE_DOWN_LEN = 16;
	
	protected long id = -1L;
	
	protected String name = "";
	
	protected Map<String,BigInteger> permissionsMap = new HashMap<String, BigInteger>();
	
	protected BigInteger permissions = new BigInteger("0");
	
	protected Long eclpLastLoginTime = -1L;
	

	protected String currentSystemCode;
	
	protected String userAccount;
	
	protected Short userType;
	
	protected int loginFailedTimes = 0;
	
	protected String lastLoginIp;
	
	protected String nowLoginIp;
	
	protected boolean isSuperUser = false;
	
	/**
	 * 用户最后活动时间
	 */
	protected Long lastActiveTime = -1L;
	
	/**
	 * （云平台）交易所id
	 */
	protected Integer exchangeId;
	
	
	/**
	 * 组织code
	 */
	protected String marketCode;
	
	
	public String getMarketCode() {
		return marketCode;
	}

	public void setMarketCode(String marketCode) {
		this.marketCode = marketCode;
	}

	public GenericUserAgent(){
		super();
	}
	
	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public BigInteger getPermissions() {
		return permissions;
	}

	public void setPermissions(BigInteger permissions) {
		this.permissions = permissions;
	}

	public String getCurrentSystemCode() {
		return currentSystemCode;
	}

	public void setCurrentSystemCode(String currentSystemCode) {
		this.currentSystemCode = currentSystemCode;
	}

	public Long getEclpLastLoginTime() {
		return eclpLastLoginTime;
	}

	public void setEclpLastLoginTime(Long eclpLastLoginTime) {
		this.eclpLastLoginTime = eclpLastLoginTime;
	}
	
	public void setSubsystemPermission(BigInteger permission, String subsystemCode){
		this.permissionsMap.put(subsystemCode, permission);
	}
	
	public BigInteger getSubsystemPermission(String subsystemCode){
		return this.permissionsMap.get(subsystemCode);
	}
	
	/**
	 * 判断是否有权限
	 * 
	 * @param code
	 * @return
	 */
	/*public final boolean havePermission(int code) {
		if(_log.isDebugEnabled()){
			_log.debug("check permission in subSystem["+this.currentSystemCode+"]");
		}
		if(MemData.getPermissionSet(this.currentSystemCode) == null){
			_log.error("Can't find the permission collection for subsystem["+this.currentSystemCode+"] in the client memory ...");
			_log.error("keys in MemData");
			if(_log.isErrorEnabled()){
				for(String key : MemData.getPermissionSetMap().keySet()){
					_log.error(key + " : " + MemData.getPermissionSet(key));
				}
			}
			return false;
		}
		if(_log.isDebugEnabled()){
			for(Entry<String, BigInteger> item : this.permissionsMap.entrySet()){
				_log.debug(item.getKey()+"-----"+item.getValue());
			}
			_log.debug("action need permission :" + code + " index :" + MemData.getPermissionSet(this.currentSystemCode).get(code));
			_log.debug("all permission list:" + MemData.getPermissionSet(this.currentSystemCode));
			_log.debug("all system permission :"+this.permissions);
		}
		if(MemData.getPermissionSet(this.currentSystemCode).get(code) != null){
			if(_log.isDebugEnabled()){
				_log.debug("user have permission : "+ this.permissions.testBit(
                    MemData.getPermissionSet(this.currentSystemCode).get(code)));
			}
			return this.permissions.testBit(MemData.getPermissionSet(this.currentSystemCode).get(code));
		}else{
			return false;
		}
	}*/
	
	/**
	 * 判断是否有权限（含子系统code参数）
	 * @param code
	 * @param subsystemCode
	 * @return
	 * <AUTHOR>
	 */
	/*public final boolean havePermission(int code, String subsystemCode) {
		if(_log.isDebugEnabled()){
			_log.debug("check permission in subSystem["+subsystemCode+"]");
		}
		if(MemData.getPermissionSet(subsystemCode) == null){
			_log.error("Can't find the permission collection for subsystem["+subsystemCode+"] in the client memory ...");
			_log.error("keys in MemData");
			if(_log.isErrorEnabled()){
				for(String key : MemData.getPermissionSetMap().keySet()){
					_log.error(key + " : " + MemData.getPermissionSet(key));
				}
			}
			return false;
		}
		BigInteger permissions = permissionsMap.get(subsystemCode);
		if(permissions == null) {
			_log.error("cannot find user permissions of subsystem:" + subsystemCode);
			return false;
		}
		if(_log.isDebugEnabled()){
			for(Entry<String, BigInteger> item : this.permissionsMap.entrySet()){
				_log.debug(item.getKey()+"-----"+item.getValue());
			}
			_log.debug("action need permission :" + code + " index :" + MemData.getPermissionSet(subsystemCode).get(code));
			_log.debug("all permission list:" + MemData.getPermissionSet(subsystemCode));
			_log.debug("all system permission :"+permissions);
		}
		if(MemData.getPermissionSet(subsystemCode).get(code) != null){
			if(_log.isDebugEnabled()){
				_log.debug("user have permission : "+ permissions.testBit(
                    MemData.getPermissionSet(subsystemCode).get(code)));
			}
			return permissions.testBit(MemData.getPermissionSet(subsystemCode).get(code));
		}else{
			return false;
		}
	}*/
	
	/**
	 * 无权限的debug信息
	 * @param code
	 * @return
	 * <AUTHOR>
	 */
	/*public final String getAccessDeniedMsg(int code) {
		StringBuilder sb = new StringBuilder();
		Map<Integer, Integer> sysPermissionSet = MemData.getPermissionSet(this.currentSystemCode);
		if(sysPermissionSet == null){
			sb.append("Can't find the permission collection for subsystem["+this.currentSystemCode+"] in the client memory ...");
			return sb.toString();
		}
		sb.append("current subsystemCode [");
		sb.append(this.currentSystemCode);
		sb.append("] for permission code:");
		sb.append(code);
		sb.append(", all system permission list:");
		sb.append(sysPermissionSet);
		sb.append(", user permission list:");
		sb.append(this.permissions);
		return sb.toString();
	}*/
	
	/**
	 * 无权限的debug信息（含子系统code参数）
	 * @param code
	 * @return
	 * <AUTHOR>
	 */
	/*public final String getAccessDeniedMsg(int code, String subsystemCode) {
		StringBuilder sb = new StringBuilder();
		Map<Integer, Integer> sysPermissionSet = MemData.getPermissionSet(subsystemCode);
		if(sysPermissionSet == null){
			sb.append("Can't find the permission collection for subsystem["+subsystemCode+"] in the client memory ...");
			return sb.toString();
		}
		sb.append("current subsystemCode [");
		sb.append(subsystemCode);
		sb.append("] for permission code:");
		sb.append(code);
		sb.append(", all system permission list:");
		sb.append(sysPermissionSet);
		sb.append(", user permission list:");
		sb.append(permissionsMap.get(subsystemCode));
		return sb.toString();
	}*/
	
	/**
	 * 添加权限
	 * @param code
	 * @return
	 */
	/*public boolean setPermission(int code){
		if(MemData.getPermissionSet(this.currentSystemCode).get(code) != null ){
			this.permissions.setBit(MemData.getPermissionSet(this.currentSystemCode).get(code));
			return true;
		}else{
			return false;
		}
	}*/
	
	
	public  final String getPermissionString(int radix){
		StringBuffer sb = new StringBuffer();
		for(String key : this.permissionsMap.keySet()){
			BigInteger big = this.permissionsMap.get(key);
			if(big!=null){
				sb.append(key).append(":").append(big.toString(radix)).append("/");
			}else{
				if(_log.isWarnEnabled()){
					_log.warn("something wrong with permissionMap in GenericUserAgent, can't get the permission of subsystem("+key+")");
				}
			}
		}
		String result = sb.toString();
		if(StringUtils.isNotEmpty(result)){
			return sb.toString().substring(0,sb.toString().lastIndexOf("/"));
		}else{
			return "";
		}
	}
	
	
	public final Map<String, BigInteger> getPermissionMapFromString(String permissionString) {
		if(StringUtils.isEmpty(permissionString)){
			return new HashMap<String, BigInteger>();
		}
		String[] permissions = permissionString.split("/");
		Map<String, BigInteger> map = new HashMap<String, BigInteger>();
		for(String item : permissions){
			String[] temp = item.split(":");
			if(temp.length>1){
				map.put(temp[0], new BigInteger(temp[1],36));
			}else{
				if(_log.isWarnEnabled()){
					_log.warn("when riseup from cookie, permissionMap found an unexpect value["+temp.toString()+"]");
				}
			}
		}
		return map;
	}
	
	@Override
	public String toString(){
		return "currentSystemCode=<"+this.currentSystemCode+">  id=<"+this.id+">  userAccount=<"+this.userAccount+">   name=<"+this.name+">  permissionsStr=<"+this.permissions.toString(36)+"> permissionMap=<"+ getPermissionString(36)+">";
	}
	
	@Override
	public String lieDown() {
		return SelfUtil.format(getBaseLieDownArray());
	}
	
	protected String[] getBaseLieDownArray() {
		String[] arr = new String[BASE_LIE_DOWN_LEN];
		//当前UserAgent类的sessionKey，用于riseUp，从SelfDepContext取注册的类名
		arr[0] = getAgentSessionKey();
		arr[1] = id+"";
		arr[2] = name;
		arr[3] = getPermissionString(36);
		arr[4] = permissions != null ? permissions.toString(36) : "";
		arr[5] = eclpLastLoginTime+"";
		arr[6] = nullToStr(currentSystemCode);
		arr[7] = userAccount;
		arr[8] = this.loginFailedTimes+"";
		arr[9] = "-1";
		arr[10] = nullToStr(this.lastLoginIp);
		arr[11] = this.userType+"";
		arr[12] = String.valueOf(this.isSuperUser);
		arr[13] = this.lastActiveTime+"";
		arr[14] = this.exchangeId+"";
		arr[15] = this.marketCode+"";
		return arr;
	}

	@Override
	public SelfDependence riseUp(String value) {
		String[] values = SelfUtil.recover(value);
		setBaseRiseUp(values);
		return this;
	}
	
	protected void setBaseRiseUp(String[] values) {
		//added by tanhl start 增加liedown里数组第一个值为需要实例化的类名
		if(values == null || values.length == 0) {
			_log.error("GenericUserAgent.riseUp: value array is null");
			return;
		}
		_log.debug("GenericUserAgent.rising up from className:" + values[0]);
		//added by tanhl end
		int len = values.length;
		if(StringUtils.isNotEmpty(values[1]) && !"null".equalsIgnoreCase(values[1])){
			this.id = Long.parseLong(values[1]);
		}
		this.name = values[2];
		permissionsMap = getPermissionMapFromString(values[3]);
		permissions = StringUtils.isNotBlank(values[4]) ? new BigInteger(values[4],36) : new BigInteger("0");
		if(StringUtils.isNotEmpty(values[5])){
			this.eclpLastLoginTime = Long.parseLong(values[5]);
		}
		this.currentSystemCode = values[6];
		this.userAccount= values[7];
		if(StringUtils.isNotEmpty(values[8])){
			this.loginFailedTimes = Integer.parseInt(values[8]);
		}
//		跳过value[8]lastLoginTime
		this.lastLoginIp = values[10];
		if(StringUtils.isNotEmpty(values[11]) && !"null".equalsIgnoreCase(values[11])){
			this.userType = Short.parseShort(values[11]);
		}
		if(StringUtils.isNotEmpty(values[12]) &&
				(values[12].equalsIgnoreCase("true") || values[12].equalsIgnoreCase("false"))){
			this.isSuperUser = Boolean.valueOf(values[12]);
		}
		if(StringUtils.isNotEmpty(values[13])){
			this.lastActiveTime = Long.parseLong(values[13]);
		}
		//增加云平台exchangeId
		if(len > 14 && StringUtils.isNotEmpty(values[14])){
			this.exchangeId = Integer.valueOf(values[14]);
		}
		if(StringUtils.isNotEmpty(values[15])){
			this.marketCode = values[15];
		}
	}

	public String getUserAccount() {
		return userAccount;
	}

	public void setUserAccount(String userAccount) {
		this.userAccount = userAccount;
	}

	public Map<String, BigInteger> getPermissionsMap() {
		return permissionsMap;
	}

	public void setPermissionsMap(Map<String, BigInteger> permissionsMap) {
		this.permissionsMap = permissionsMap;
	}

	public Short getUserType() {
		return userType;
	}

	public void setUserType(Short userType) {
		this.userType = userType;
	}

	public int getLoginFailedTimes() {
		return loginFailedTimes;
	}

	public void setLoginFailedTimes(int loginFailedTimes) {
		this.loginFailedTimes = loginFailedTimes;
	}

	public String getLastLoginIp() {
		return lastLoginIp;
	}

	public void setLastLoginIp(String lastLoginIp) {
		this.lastLoginIp = lastLoginIp;
	}

	public String getNowLoginIp() {
		return nowLoginIp;
	}

	public void setNowLoginIp(String nowLoginIp) {
		this.nowLoginIp = nowLoginIp;
	}

	public boolean isSuperUser() {
		return isSuperUser;
	}

	public void setSuperUser(boolean isSuperUser) {
		this.isSuperUser = isSuperUser;
	}

	public Long getLastActiveTime() {
		return lastActiveTime;
	}

	public void setLastActiveTime(Long lastActiveTime) {
		this.lastActiveTime = lastActiveTime;
	}
	
	public Date getLastLoginDate() {
		if(this.eclpLastLoginTime == -1L) {
			return null;
		}
		return new Date(this.eclpLastLoginTime);
	}
	
	public Integer getExchangeId() {
		return exchangeId;
	}

	public void setExchangeId(Integer exchangeId) {
		this.exchangeId = exchangeId;
	}

	private String nullToStr(String str) {
		return str == null || str.trim() == "" ? "" : str;
	}
	
	/**
	 * 当前UserAgent类的sessionKey，用于riseUp，从SelfDepContext取注册的类名
	 * @return
	 * <AUTHOR>
	 */
	public String getAgentSessionKey() {
		return ECLP_USER_AGENT;
	}
}
