package com.hundsun.eclp.client.common;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

public class MemData {
	
	private final static Log log = LogFactory.getLog(MemData.class);

	//权限最后更新时间
	private static long permissionUpdateTime = -1;
	
	/**
	 * 子系统权限信息缓存key值
	 */
	public static final String SYS_PERMISSION_MAP_CACHE_KEY = "subsystemPermissionMap";
	
	/**
	 * 缓存类型（由registerService afterPropertiesSet()方法赋值）
	 */
	private static String cacheType;
	
	/**
	 * 权限缓存类型：local
	 */
	public static final String LOCAL_CACHE_TYPE = "local";
	
	/**
	 * 权限缓存类型：memcached
	 */
	public static final String MEMCACHED_CACHE_TYPE = "memcached";
	
	/**
	 * 权限缓存类型：couchbase
	 */
	public static final String COUCHBASE_CACHE_TYPE = "couchbase";
	
	/**
	 * 权限缓存类型：redis
	 */
	public static final String REDIS_CACHE_TYPE = "redis";
	
	public static long getPermissionUpdateTime() {
		return permissionUpdateTime;
	}

	public static void setPermissionUpdateTime(long permissionUpdateTime) {
		MemData.permissionUpdateTime = permissionUpdateTime;
	}
	
	/*public static Map<Integer, Integer> getPermissionSet(String systemCode) {
		return getSubsystemPermissionMap().get(systemCode);
	}*/
	
	/*public static void setPermissionSet(String systemCode, Map<Integer, Integer> permissionSet) {
		if(isDistributedCache()) {
			log.error("MemoPermission.setPermissionSet: current cache type is "+cacheType+", client is not allowed to set permission");
			return;
		}
		Map<String, Map<Integer, Integer>> permissionSetMap = getSubsystemPermissionMap();
		permissionSetMap.put(systemCode, permissionSet);
		getSysCacheService().saveObj(SYS_PERMISSION_MAP_CACHE_KEY, permissionSetMap);
	}*/

	/*public static Map<String, Map<Integer, Integer>> getPermissionSetMap() {
		return getSubsystemPermissionMap();
	}*/

	/*public static void setPermissionSetMap(Map<String, Map<Integer, Integer>> permissionSetMap) {
		if(isDistributedCache()) {
			log.error("MemoPermission.setPermissionSetMap: current cache type is "+cacheType+", client is not allowed to set permission");
			return;
		}
		getSysCacheService().saveObj(SYS_PERMISSION_MAP_CACHE_KEY, permissionSetMap);
	}*/

	public static String getCacheType() {
		return cacheType;
	}

	public static void setCacheType(String cacheType) {
		MemData.cacheType = cacheType;
	}
	
	/**
	 * 获得缓存服务sysCacheService
	 * @return
	 * <AUTHOR>
	 */
	/*public static SysCacheService getSysCacheService() {
		SysCacheService sysCacheService = (SysCacheService) ObjectFactory.getBean("sysCacheService");
		if(sysCacheService == null) {
			log.error("MemData: sysCacheService is not configured");
			throw new RuntimeException("MemData: sysCacheService is not configured");
		}
		return sysCacheService;
	}*/
	
	@SuppressWarnings("unchecked")
	/*public static Map<String, Map<Integer, Integer>> getSubsystemPermissionMap() {
		Map<String, Map<Integer, Integer>> subsystemPermissionMap 
			= (Map<String, Map<Integer, Integer>>) getSysCacheService().getSystemObj(SYS_PERMISSION_MAP_CACHE_KEY);
		if(subsystemPermissionMap == null) {
			log.error("MemData: subsystemPermissionMap is null in cache");
			if(isDistributedCache()) {
				throw new RuntimeException("MemData: subsystemPermissionMap is null in cache");
			} else {
				subsystemPermissionMap = new HashMap<String, Map<Integer,Integer>>();
				getSysCacheService().saveObj(SYS_PERMISSION_MAP_CACHE_KEY, subsystemPermissionMap);
			}
		}
		return subsystemPermissionMap;
	}*/
	
	/**
	 * 是否是分布式缓存
	 * @return
	 * <AUTHOR>
	 */
	public static boolean isDistributedCache() {
		//memcached
		return MEMCACHED_CACHE_TYPE.equals(cacheType )
				|| COUCHBASE_CACHE_TYPE.equals(cacheType)
				|| REDIS_CACHE_TYPE.equals(cacheType );
	}
	
}
