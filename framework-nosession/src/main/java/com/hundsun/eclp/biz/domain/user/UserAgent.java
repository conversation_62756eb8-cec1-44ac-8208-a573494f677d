package com.hundsun.eclp.biz.domain.user;

import com.apec.framework.nosession.common.util.DateUtil;
import com.apec.framework.nosession.common.util.SelfUtil;
import com.apec.framework.nosession.dto.SelfDependence;
import com.hundsun.eclp.client.common.GenericUserAgent;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.Date;

public class UserAgent extends GenericUserAgent
{

	/**
	 * 
	 */
	private static final long serialVersionUID = 3705029374417987102L;
	
	private static Log _log = LogFactory.getLog(UserAgent.class);
	
	public static final String ECLP_SUBSYSTEM_CODE = "eclp";
	
	public static final String SESSION_AGENT = "sessionAgent";
	
	private String sessionToken;

	public UserAgent() {
		super();
	}
	
	public UserAgent(GenericUserAgent genericUserAgent) {
		if(genericUserAgent != null) {
			super.id = genericUserAgent.getId();
			super.name = genericUserAgent.getName();
			super.permissionsMap = genericUserAgent.getPermissionsMap();
			super.permissions = genericUserAgent.getPermissions();
			super.eclpLastLoginTime = genericUserAgent.getEclpLastLoginTime();
			super.currentSystemCode = genericUserAgent.getCurrentSystemCode();
			super.userAccount = genericUserAgent.getUserAccount();
			super.userType = genericUserAgent.getUserType();
			super.loginFailedTimes = genericUserAgent.getLoginFailedTimes();
			super.lastLoginIp = genericUserAgent.getLastLoginIp();
			super.nowLoginIp=genericUserAgent.getNowLoginIp();
			super.isSuperUser = genericUserAgent.isSuperUser();
			super.lastActiveTime = genericUserAgent.getLastActiveTime();
			super.exchangeId = genericUserAgent.getExchangeId();
			super.marketCode=genericUserAgent.getMarketCode();
		}
	}
	
	public UserAgent(Users user) {
		super();
		super.id = user.getId();
		super.name = user.getName();
		super.userAccount = user.getAccount();
	}
	

	/**
	 * 添加权限
	 * @param index
	 * @return
	 */
	
	public boolean setPermission(int index){
		super.permissions = super.permissions.setBit(index);
		super.setSubsystemPermission(permissions, currentSystemCode);
		return true;
	}
	

	
	public GenericUserAgent toGenericUserAgent() {
		GenericUserAgent agent = new GenericUserAgent();
		agent.setId(super.id);
		agent.setName(super.name);
		agent.setPermissionsMap(super.permissionsMap);
		agent.setPermissions(super.permissions);
		agent.setEclpLastLoginTime(super.eclpLastLoginTime);
		agent.setCurrentSystemCode(super.currentSystemCode);
		agent.setUserAccount(super.userAccount);
		agent.setUserType(super.userType);
		agent.setLoginFailedTimes(super.loginFailedTimes);
		agent.setLastLoginIp(super.lastLoginIp);
		agent.setSuperUser(super.isSuperUser);
		agent.setLastActiveTime(super.lastActiveTime);
		agent.setExchangeId(super.exchangeId);
		agent.setMarketCode(super.marketCode);
		return agent;
	}

	public String getSessionToken() {
		return sessionToken;
	}

	public void setSessionToken(String sessionToken) {
		this.sessionToken = sessionToken;
	}
	
	@Override
	public String getAgentSessionKey() {
		return UserAgent.SESSION_AGENT;
	}
	public String getFormattedNowLoginTime() {
		if(super.eclpLastLoginTime !=null && super.eclpLastLoginTime !=-1L){
			String date = DateUtil.convertDateToString("yyyy-MM-dd HH:mm:ss", this.getLastLoginDate());
			if(StringUtils.isNotBlank(date) && !date.startsWith("1970")) {
				return date;
			} 
		}
		return null;
	}
	public String getFormattedLastLoginTime() {
		if(super.eclpLastLoginTime !=null && super.eclpLastLoginTime !=-1L){
			String date = DateUtil.convertDateToString("yyyy-MM-dd HH:mm:ss", this.getLastLoginDate1());
			if(StringUtils.isNotBlank(date) && !date.startsWith("1970")) {
				return date;
			} 
		}
		return null;
	}
	public Date getLastLoginDate1() {
		if(super.eclpLastLoginTime == -1L) {
			return null;
		}
		return new Date(this.getLastActiveTime());
	}
	
	@Override
	protected String[] getBaseLieDownArray() {
		String[] arr = super.getBaseLieDownArray();
		String[] newArr = new String[arr.length+1];
		int i = 0;
		for(String str : arr) {
			newArr[i++] = str;
		}
		newArr[i] = this.sessionToken;
		return newArr;
	}
	
	@Override
	public SelfDependence riseUp(String value) {
		String[] values = SelfUtil.recover(value);
		setBaseRiseUp(values);
		int len = values.length;
		if(len > BASE_LIE_DOWN_LEN) {
			this.sessionToken = values[BASE_LIE_DOWN_LEN];
		}
		return this;
	}
	
}
