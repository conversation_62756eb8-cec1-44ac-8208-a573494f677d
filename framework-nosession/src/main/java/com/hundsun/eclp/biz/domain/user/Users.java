package com.hundsun.eclp.biz.domain.user;



import com.apec.framework.nosession.dto.BaseDTO;

import java.util.Date;
import java.util.List;

public class Users extends BaseDTO
{
    /**
	 * 
	 */
	private static final long serialVersionUID = -6593292324070146928L;

	/**
     * 用户id
     */
    private Long id;

    /**
     * 用户登录账号
     */
    private String account;

    /**
     * 密码
     */
    private String password;

    /**
     * 姓名
     */
    private String name;

    /**
     * 状态： 0禁用 1启用  2删除
     */
    private Short status;
    /**
     * 片区职位
     */
    private String areaType;

    /**
     * 用户类型： 1： 开发级 2：应用级 3：其他
     */
    private Short userType;

    /**
     * 最后登录ip
     */
    private String lastLoginIp;

    /**
     * 最后登录时间
     */
    private Date lastLoginTime;

    /**
     * 登录次数
     */
    private Long loginNum;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModify;

    /**
     * 会员详情
     */
    private UserInfo userInfo ;

    /**
     * 角色id
     */
    private List<Long> roleId;
    /**
     * 新密码，修改时用
     */
    private String newPassword ;
    /**
     * 默认子系统
     */
    private Long   defaultSubSys ;

    /**
     * 来源：登陆：login，其他:other
     * 
     */
    private String source;
    
    /**
     * 登录失败次数
     */
    private Long loginFailedTimes;
    
    /**
     * 交易所ID
     */
    private String exchangeId; 
    
    /**
     * 复核类型
     */
    private String recheckType;
    
    /**
     * 复核申请状态
     */
    private String applyStatus;
    
    /**
     * 申请时间
     */
    private Date applyTime;
    
    /**
     * 审核时间
     */
    private Date auditTime;
    
    /**
     * 审核流水表id
     */
    private Long auditId;
    
    /**
     * 上次修改密码时间
     */
    private Date lastModifyPwdTime;
    
    /**
     * 上次登录失败时间
     */
    private Date lastLoginFailTime;
    
    /**
     * 修改密码的标志位
     */
    private String updatePwdFlag;
    
    /**
     * 密码输错的标志位
     */
    private String pwdIncorrentFlag;

    public Users(){
    }

    public Users(String account, String password){
    	this.account = account;
    	this.password = password;
    }

    public Users(long id, String account, String password,String lastLoginIp){
    	this.id =id;
    	this.account = account;
    	this.password = password;
    	this.lastLoginIp = lastLoginIp;
    }
    
    
	public Long getDefaultSubSys() {
		return defaultSubSys;
	}

	public void setDefaultSubSys(Long defaultSubSys) {
		this.defaultSubSys = defaultSubSys;
	}

	public String getNewPassword() {
		return newPassword;
	}

	public void setNewPassword(String newPassword) {
		this.newPassword = newPassword;
	}

	public List<Long> getRoleId() {
		return roleId;
	}

	public void setRoleId(List<Long> roleId) {
		this.roleId = roleId;
	}

	public UserInfo getUserInfo() {
		return userInfo;
	}

	public void setUserInfo(UserInfo userInfo) {
		this.userInfo = userInfo;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getAccount() {
		return account;
	}

	public void setAccount(String account) {
		this.account = account;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Short getStatus() {
		return status;
	}

	public void setStatus(Short status) {
		this.status = status;
	}

	public Short getUserType() {
		return userType;
	}

	public void setUserType(Short userType) {
		this.userType = userType;
	}

	public String getLastLoginIp() {
		return lastLoginIp;
	}

	public void setLastLoginIp(String lastLoginIp) {
		this.lastLoginIp = lastLoginIp;
	}

	public Date getLastLoginTime() {
		return lastLoginTime;
	}

	public void setLastLoginTime(Date lastLoginTime) {
		this.lastLoginTime = lastLoginTime;
	}

	public Long getLoginNum() {
		return loginNum;
	}

	public void setLoginNum(Long loginNum) {
		this.loginNum = loginNum;
	}

	public Date getGmtCreate() {
		return gmtCreate;
	}

	public void setGmtCreate(Date gmtCreate) {
		this.gmtCreate = gmtCreate;
	}

	public Date getGmtModify() {
		return gmtModify;
	}

	public void setGmtModify(Date gmtModify) {
		this.gmtModify = gmtModify;
	}

	/**
	 * @return the source
	 */
	public String getSource() {
		return source;
	}

	/**
	 * @param source the source to set
	 */
	public void setSource(String source) {
		this.source = source;
	}

	public String getExchangeId() {
		return exchangeId;
	}

	public void setExchangeId(String exchangeId) {
		this.exchangeId = exchangeId;
	}

	public Long getLoginFailedTimes() {
		return loginFailedTimes != null ? loginFailedTimes : 0L;
	}

	public void setLoginFailedTimes(Long loginFailedTimes) {
		this.loginFailedTimes = loginFailedTimes;
	}

	public String getRecheckType() {
		return recheckType;
	}

	public void setRecheckType(String recheckType) {
		this.recheckType = recheckType;
	}

	public String getApplyStatus() {
		return applyStatus;
	}

	public void setApplyStatus(String applyStatus) {
		this.applyStatus = applyStatus;
	}

	public Date getApplyTime() {
		return applyTime;
	}

	public void setApplyTime(Date applyTime) {
		this.applyTime = applyTime;
	}

	public Date getAuditTime() {
		return auditTime;
	}

	public void setAuditTime(Date auditTime) {
		this.auditTime = auditTime;
	}

	public Long getAuditId() {
		return auditId;
	}

	public void setAuditId(Long auditId) {
		this.auditId = auditId;
	}

	public Date getLastModifyPwdTime() {
		return lastModifyPwdTime;
	}

	public void setLastModifyPwdTime(Date lastModifyPwdTime) {
		this.lastModifyPwdTime = lastModifyPwdTime;
	}

	public String getUpdatePwdFlag() {
		return updatePwdFlag;
	}

	public void setUpdatePwdFlag(String updatePwdFlag) {
		this.updatePwdFlag = updatePwdFlag;
	}

	public String getPwdIncorrentFlag() {
		return pwdIncorrentFlag;
	}

	public void setPwdIncorrentFlag(String pwdIncorrentFlag) {
		this.pwdIncorrentFlag = pwdIncorrentFlag;
	}

	public Date getLastLoginFailTime() {
		return lastLoginFailTime;
	}

	public void setLastLoginFailTime(Date lastLoginFailTime) {
		this.lastLoginFailTime = lastLoginFailTime;
	}

	public String getAreaType() {
		return areaType;
	}

	public void setAreaType(String areaType) {
		this.areaType = areaType;
	}
	
	

   }