package com.hundsun.eclp.biz.domain.user;

import com.apec.framework.nosession.dto.BaseDTO;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

public class UserInfo extends BaseDTO
{
    /**
	 * 
	 */
	private static final long serialVersionUID = -1484581108502771112L;

	/**
     *用户信息id
     */
    private Long id;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String realName;

    /**
     *人员状态: 0正常  1删除  2删除
     */
    private Short status;


    /**
     *职位
     */
    private String position;

    /**
     * 生日
     */
    private Date birthday;

    /**
     * qq
     */
    private String qq;

    /**
     * msn
     */
    private String msn;

    /**
     * wangwang
     */
    private String wangwang;

    /**
     * 办公电话
     */
    private String officePhone;

    /**
     * 住宅电话
     */
    private String homePhone;

    /**
     * 移动电话
     */
    private String mobilePhone;

    /**
     * 邮件地址
     */
    private String email;

    /**
     * 地址
     */
    private String address;

    /**
     *入职时间
     */
    private Date availableDate;

    /**
     * 工作年限
     */
    private Short workYear;

    /**
     * 证据类型 0 居民身份证，1驾驶证,2 护照，3军官证，4 其他
     */
    private Short idType;

    /**
     * 证件号
     */
    private String idCard;

    /**
     * 学历
     */
    private String education;

    /**
     * 学位
     */
    private String degree;

    /**
     * 是否在职
     */
    private Short isActive;

    /**
     * 工号
     */
    private String jobNo;

    /**
     * 性别；0-男，1-女
     */
    private Short gender;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModify;
    
    /**
     * 排序
     */
    private Short sort;
    /**
     * 删除标志
     */
    private String isDeleted;
    /**
     * id
     */
    private Long uinfoId;
    /**
     * 会员账号
     */
    private String account;
    
    /**
     * 注册地址省份code
     */
    private String regProvinceCode;

    /**
     * 注册省份别名
     */
    private String regProvince;

    /**
     * 注册地址市code
     */
    private String regCityCode;

    /**
     * 注册市别名
     */
    private String regCity;

    /**
     * 区县
     */
    private String regDistrict;

    /**
     * 区县别名
     */
    private String regDistrictCode;

    /**
     * 街道等详细地址(不包含省市地区)
     */
    private String regAddr;
    
    /**
     * 扩展字段1
     */
    private String ext1;
    
    /**
     * 扩展字段2
     */
    private String ext2;
    
    /**
     * 扩展字段3
     */
    private String ext3;
    
    /**
     * 会员类型
     */
    private Short userType;
    
    /**
    * 交易所ID
    */
   private String exchangeId; 
    
    public Long getUinfoId() {
		return uinfoId;
	}

	public void setUinfoId(Long uinfoId) {
		this.uinfoId = uinfoId;
	}

	public String getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(String isDeleted) {
		this.isDeleted = isDeleted;
	}

	private List<Long> deptList ;
    
    public List<Long> getDeptList() {
		return deptList;
	}

	public void setDeptList(List<Long> deptList) {
		this.deptList = deptList;
	}

	private String deptName;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public Short getStatus() {
		return status;
	}

	public void setStatus(Short status) {
		this.status = status;
	}

	public String getPosition() {
		return position;
	}

	public void setPosition(String position) {
		this.position = position;
	}

	public Date getBirthday() {
		return birthday;
	}

	public void setBirthday(Date birthday) {
		this.birthday = birthday;
	}

	public String getQq() {
		return qq;
	}

	public void setQq(String qq) {
		this.qq = qq;
	}

	public String getMsn() {
		return msn;
	}

	public void setMsn(String msn) {
		this.msn = msn;
	}

	public String getWangwang() {
		return wangwang;
	}

	public void setWangwang(String wangwang) {
		this.wangwang = wangwang;
	}

	public String getOfficePhone() {
		return officePhone;
	}

	public void setOfficePhone(String officePhone) {
		this.officePhone = officePhone;
	}

	public String getHomePhone() {
		return homePhone;
	}

	public void setHomePhone(String homePhone) {
		this.homePhone = homePhone;
	}

	public String getMobilePhone() {
		return mobilePhone;
	}

	public void setMobilePhone(String mobilePhone) {
		this.mobilePhone = mobilePhone;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public Date getAvailableDate() {
		return availableDate;
	}

	public void setAvailableDate(Date availableDate) {
		this.availableDate = availableDate;
	}

	public Short getWorkYear() {
		return workYear;
	}

	public void setWorkYear(Short workYear) {
		this.workYear = workYear;
	}

	public Short getIdType() {
		return idType;
	}

	public void setIdType(Short idType) {
		this.idType = idType;
	}

	public String getIdCard() {
		return idCard;
	}

	public void setIdCard(String idCard) {
		this.idCard = idCard;
	}

	public String getEducation() {
		return education;
	}

	public void setEducation(String education) {
		this.education = education;
	}

	public String getDegree() {
		return degree;
	}

	public void setDegree(String degree) {
		this.degree = degree;
	}

	public Short getIsActive() {
		return isActive;
	}

	public void setIsActive(Short isActive) {
		this.isActive = isActive;
	}

	public String getJobNo() {
        return jobNo;
    }

    public void setJobNo(String jobNo) {
        this.jobNo = jobNo;
    }

    public Short getGender() {
        return gender;
    }

    public void setGender(Short gender) {
        this.gender = gender;
    }

    public Date getGmtCreate() {
		return gmtCreate;
	}

	public void setGmtCreate(Date gmtCreate) {
		this.gmtCreate = gmtCreate;
	}

	public Date getGmtModify() {
		return gmtModify;
	}

	public void setGmtModify(Date gmtModify) {
		this.gmtModify = gmtModify;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public Short getSort() {
		return sort;
	}

	public void setSort(Short sort) {
		this.sort = sort;
	}

	public String getAccount() {
		return account;
	}

	public void setAccount(String account) {
		this.account = account;
	}
	
	public String getRegProvinceCode() {
		return regProvinceCode;
	}

	public void setRegProvinceCode(String regProvinceCode) {
		this.regProvinceCode = regProvinceCode;
	}

	public String getRegProvince() {
		return regProvince;
	}

	public void setRegProvince(String regProvince) {
		this.regProvince = regProvince;
	}

	public String getRegCityCode() {
		return regCityCode;
	}

	public void setRegCityCode(String regCityCode) {
		this.regCityCode = regCityCode;
	}

	public String getRegCity() {
		return regCity;
	}

	public void setRegCity(String regCity) {
		this.regCity = regCity;
	}

	public String getRegDistrict() {
		return regDistrict;
	}

	public void setRegDistrict(String regDistrict) {
		this.regDistrict = regDistrict;
	}

	public String getRegDistrictCode() {
		return regDistrictCode;
	}

	public void setRegDistrictCode(String regDistrictCode) {
		this.regDistrictCode = regDistrictCode;
	}

	public String getRegAddr() {
		return regAddr;
	}

	public void setRegAddr(String regAddr) {
		this.regAddr = regAddr;
	}

	public String getExt1() {
		return ext1;
	}

	public void setExt1(String ext1) {
		this.ext1 = ext1;
	}

	public String getExt2() {
		return ext2;
	}

	public void setExt2(String ext2) {
		this.ext2 = ext2;
	}

	public String getExt3() {
		return ext3;
	}

	public void setExt3(String ext3) {
		this.ext3 = ext3;
	}

	public Short getUserType() {
		return userType;
	}

	public void setUserType(Short userType) {
		this.userType = userType;
	}
	
	public String getExchangeId() {
		return exchangeId;
	}

	public void setExchangeId(String exchangeId) {
		this.exchangeId = exchangeId;
	}

	/*public UserDTO toDTO(){
		UserDTO dto = new UserDTO();
		try {
			BeanUtilEx.copyProperties(dto, this);
		} catch (IllegalAccessException e) {
			e.printStackTrace();
		} catch (InvocationTargetException e) {
			e.printStackTrace();
		}
		return dto;
	}*/

	/**
	 * 省市区的全名
	 * @return
	 * <AUTHOR>
	 */
	public String getRegFullAddr() {
		StringBuilder sb = new StringBuilder();
		if(StringUtils.isNotBlank(regProvince)) {
			sb.append(regProvince);
			sb.append(" ");
		}
		if(StringUtils.isNotBlank(regCity)) {
			sb.append(regCity);
			sb.append(" ");
		}
		if(StringUtils.isNotBlank(regDistrict)) {
			sb.append(regDistrict);
			sb.append(" ");
		}
		if(StringUtils.isNotBlank(regAddr)) {
			sb.append(regAddr);
		}
		return sb.toString();
	}

}