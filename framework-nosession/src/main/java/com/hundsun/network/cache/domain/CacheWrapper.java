package com.hundsun.network.cache.domain;

import java.io.Serializable;

/**
 * Created by liliwei on 2018/5/5.
 */
public class CacheWrapper implements Serializable
{
    private static final long serialVersionUID = 194409831198632376L;
    private Object value;

    public CacheWrapper() {}

    public CacheWrapper(Object value)
    {
        this.value = value;
    }

    public Object getValue()
    {
        return this.value;
    }

    public void setValue(Object value)
    {
        this.value = value;
    }
}
