package com.hundsun.exchange.uc.client.dto;

import com.apec.framework.nosession.dto.BaseDTO;

/**
 * 用户token
 * 
 * <AUTHOR>
 */
public class UserTokenInfo extends BaseDTO
{

	/**
	 * 
	 */
	private static final long serialVersionUID = 4224185293747950151L;

	/**
	 * 用户token
	 */
	private String userToken;
	
	/**
	 * 上次活跃时间
	 */
	private long lastActiveTime;
	
	/**
	 * 客户端登录的id标识
	 */
	private String clientId;

	public String getUserToken() {
		return userToken;
	}

	public void setUserToken(String userToken) {
		this.userToken = userToken;
	}

	public long getLastActiveTime() {
		return lastActiveTime;
	}

	public void setLastActiveTime(long lastActiveTime) {
		this.lastActiveTime = lastActiveTime;
	}

	public String getClientId() {
		return clientId;
	}

	public void setClientId(String clientId) {
		this.clientId = clientId;
	}
}
