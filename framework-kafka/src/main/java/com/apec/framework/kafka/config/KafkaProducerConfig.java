/**
 * 
 */
package com.apec.framework.kafka.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;

import com.apec.framework.kafka.bean.KafkaProvider;

/**
 * kafka配置
 * <AUTHOR>
 * @version 2019-06-13
 */
@Component
@ConditionalOnBean(value=KafkaProvider.class)
public class KafkaProducerConfig
{

    /**
     * server地址
     */
    @Value("${kafka.server}")
    private String kafkaServer;
    
    /**
     * ACK设置
     */
    @Value("${kafka.producer.acks:0}")
    private String acks = "0";
    
    /**
     * 发送重试次数
     */
    @Value("${kafka.producer.retries:0}")
    private int retries = 0;
    
    /**
     * 发送最大消息字节数
     */
    @Value("${kafka.producer.max-request-size:1048576}")
    private int maxRequestSize=1*1024*1024;
    
    /**
     * 生产者topic
     */
    @Value("${kafka.producer.topic}")
    private String producerTopic;
    
    public String getKafkaServer()
    {
        return kafkaServer;
    }

    public void setKafkaServer(String kafkaServer)
    {
        this.kafkaServer = kafkaServer;
    }

    public String getAcks()
    {
        return acks;
    }

    public void setAcks(String acks)
    {
        this.acks = acks;
    }

    public int getRetries()
    {
        return retries;
    }

    public void setRetries(int retries)
    {
        this.retries = retries;
    }

    public String getProducerTopic()
    {
        return producerTopic;
    }

    public void setProducerTopic(String producerTopic)
    {
        this.producerTopic = producerTopic;
    }

    public int getMaxRequestSize()
    {
        return maxRequestSize;
    }

    public void setMaxRequestSize(int maxRequestSize)
    {
        this.maxRequestSize = maxRequestSize;
    }
}
