package com.apec.framework.kafka.bean;

import java.time.Duration;
import java.util.Arrays;
import java.util.Properties;
//import java.util.concurrent.ArrayBlockingQueue;
//import java.util.concurrent.RejectedExecutionHandler;
//import java.util.concurrent.ThreadPoolExecutor;
//import java.util.concurrent.TimeUnit;

import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.apec.framework.kafka.config.KafkaConsumerConfig;

/**
 * Kafka消费者,用于监听并接收来自kafka集群的消息。
 * <AUTHOR>
 * @since 2019-06-13
 */
public abstract class KafkaReceiver extends Thread
{

    private static Logger LOG  = LoggerFactory.getLogger(KafkaReceiver.class);
    
    private KafkaConsumerConfig kafkaConfiguration;

    private Properties props = new Properties();
    
    private static int threadInitNumber = 0;
    
//    private ThreadPoolExecutor threadPool;

    /**
     * 通过配置文件构造一个kafka接收器
     * @param kafkaConfiguration 通过配置文件得到的kafka配置
     */
    public KafkaReceiver(KafkaConsumerConfig kafkaConfiguration)
    {
        this.kafkaConfiguration = kafkaConfiguration;
    }

    /**
     * 初始化consumer及消费监听线程
     */
    public void init()
    {
        // 从配置文件中注入有关kafka消费者的设置项
        String kafkaServer = kafkaConfiguration.getKafkaServer();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaServer);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, kafkaConfiguration.getGroupId());
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, kafkaConfiguration.isAutoCommit());
        props.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, kafkaConfiguration.getInterval());
        props.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, kafkaConfiguration.getTimeout());
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        
        
        setName("KafkaConsumer-" + nextThreadNumber());
        start();
        LOG.info("Kafka consumer has been started to listen broker:{}",kafkaServer);
    }
    
    /**
     * 同步增长线程号
     * @return
     */
    private synchronized int nextThreadNumber()
    {
        return ++threadInitNumber;
    }

    @Override
    public void run()
    {
        try (final Consumer<String, String> consumer = new KafkaConsumer<>(props))
        {
            consumer.subscribe(Arrays.asList(kafkaConfiguration.getConsumerTopic()));
            while (true)
            {
                Duration duration = Duration.ofMillis(kafkaConfiguration.getPollTimeout());
                ConsumerRecords<String, String> records = consumer.poll(duration);
                if(null == records || records.isEmpty())
                {
                    continue;
                }
                try
                {
                	this.receiveRecords(records,consumer);
                	if(!kafkaConfiguration.isAutoCommit())
                	{
                		consumer.commitAsync();
                	}
                }
                
                // 发生异常时不退出while循环
                catch(RuntimeException ex)
                {
                	LOG.error("consume kafka message error!",ex);
                }
            }
        }
    }
    
    /**
     * 处理消息,如果是采用了autocommit则消息会自动提交,否则需要手动显式进行{@link consumer.commitSync()} 
     * @param records  记录集合
     * @param consumer 消费者
     */
    protected abstract void receiveRecords(ConsumerRecords<String, String> records,final Consumer<String, String> consumer);
}