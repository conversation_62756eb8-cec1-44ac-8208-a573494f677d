package com.apec.framework.kafka.bean;

import java.util.Properties;

import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;

import com.apec.framework.kafka.config.KafkaProducerConfig;

/**
 * Kafka消息通用处理类
 * <AUTHOR>
 * @Date 2018-04-16
 */
public class KafkaProvider
{
    private KafkaProducerConfig config;
    
    private String defaultTopic;
    
    private Producer<String, String> producer;
    
    /**
     * 由配置文件构建生产者
     * @param kafkaConfiguration
     */
    public KafkaProvider(KafkaProducerConfig kafkaConfiguration)
    {
        this.config = kafkaConfiguration;
    }
    
    /**
     * 读取配置并初始化生产者
     */
    public void init()
    {
        Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, config.getKafkaServer());
        props.put(ProducerConfig.ACKS_CONFIG, config.getAcks());
        props.put(ProducerConfig.RETRIES_CONFIG, config.getRetries());
        props.put(ProducerConfig.MAX_REQUEST_SIZE_CONFIG, config.getMaxRequestSize());
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG,   "org.apache.kafka.common.serialization.StringSerializer");
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        producer = new KafkaProducer<>(props);
        this.defaultTopic=config.getProducerTopic();
    }
    
    /**
     * 发送到其它topic
     * @param data 消息体
     */
    public void sendToKafka(String topic, String data)
    {
        producer.send(new ProducerRecord<String, String>(topic, data));
    }
    
    /**
     * 发送消息到默认topic
     * @param data
     */
    public void sendToKafka(String data)
    {
        sendToKafka(defaultTopic, data);
    }
}