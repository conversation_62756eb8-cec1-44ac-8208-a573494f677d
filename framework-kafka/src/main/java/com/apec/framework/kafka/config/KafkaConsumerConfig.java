/**
 * 
 */
package com.apec.framework.kafka.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;

import com.apec.framework.kafka.bean.KafkaReceiver;

/**
 * kafka配置
 * <AUTHOR>
 * @version 2019-06-13
 */
@Component
@ConditionalOnBean(value=KafkaReceiver.class)
public class KafkaConsumerConfig
{
    /**
     * 消费groupId
     */
    @Value("${kafka.consumer.groupid}")
    private String groupId;

    /**
     * server地址
     */
    @Value("${kafka.server}")
    private String kafkaServer;
    
    /**
     * 消费topic
     */
    @Value("${kafka.consumer.topic}")
    private String consumerTopic;
    
    /**
     * 是否自动提交
     */
    @Value("${kafka.consumer.autoCommit:true}")
    private boolean autoCommit=false;
    
    /**
     * 自动提交周期
     */
    @Value("${kafka.consumer.autocommit-interval:1000}")
    private int interval = 1000;
    
    /**
     * 检测consumer超时时间
     */
    @Value("${kafka.consumer.timeout:30000}")
    private int timeout = 30000;
    
    /**
     * 消费核心线程数
     */
    @Value("${kafka.consumer.corePoolSize:5}")
    private int corePoolSize = 5;
    
    /**
     * 消费最大线程数
     */
    @Value("${kafka.consumer.maximumPoolSize:50}")
    private int maximumPoolSize = 50;
    
    /**
     * 消费者poll超时时间
     */
    @Value("${kafka.consumer.poll-timeout:5000}")
    private int pollTimeout = 5000;

    public String getGroupId()
    {
        return groupId;
    }

    public void setGroupId(String groupId)
    {
        this.groupId = groupId;
    }

    public String getKafkaServer()
    {
        return kafkaServer;
    }

    public void setKafkaServer(String kafkaServer)
    {
        this.kafkaServer = kafkaServer;
    }

    public String getConsumerTopic()
    {
        return consumerTopic;
    }

    public void setConsumerTopic(String consumerTopic)
    {
        this.consumerTopic = consumerTopic;
    }

    public boolean isAutoCommit()
    {
        return autoCommit;
    }

    public void setAutoCommit(boolean autoCommit)
    {
        this.autoCommit = autoCommit;
    }

    public int getInterval()
    {
        return interval;
    }

    public void setInterval(int interval)
    {
        this.interval = interval;
    }

    public int getTimeout()
    {
        return timeout;
    }

    public void setTimeout(int timeout)
    {
        this.timeout = timeout;
    }

    public int getCorePoolSize()
    {
        return corePoolSize;
    }

    public void setCorePoolSize(int corePoolSize)
    {
        this.corePoolSize = corePoolSize;
    }

    public int getMaximumPoolSize()
    {
        return maximumPoolSize;
    }

    public void setMaximumPoolSize(int maximumPoolSize)
    {
        this.maximumPoolSize = maximumPoolSize;
    }

    public int getPollTimeout()
    {
        return pollTimeout;
    }

    public void setPollTimeout(int pollTimeout)
    {
        this.pollTimeout = pollTimeout;
    }
}
