package com.apec.framework.config;

import com.apec.framework.cache.manager.HSCouchbaseCacheManager;
import com.apec.framework.cache.service.CouchbaseService;
import com.apec.framework.cache.support.HSCouchbaseCacheBuilder;
import com.couchbase.client.java.Bucket;
import com.couchbase.client.java.CouchbaseCluster;
import com.couchbase.client.java.env.CouchbaseEnvironment;
import com.couchbase.client.java.env.DefaultCouchbaseEnvironment;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.util.StringUtils;

import java.util.Properties;

/**
 * couchbase配置client
 * Created by liliwei on 2018/4/28.
 */
@Configuration
@PropertySource(value = "classpath:couchbase.properties")
public class CouchbaseConfig
{
    @Value("${couchbase.server}")
    private String hosts;

    /*@Value("${couchbase.port:8091}")
    private int port;*/

    @Value("${couchbase.bucket}")
    private String bucketName;

    @Value("${couchbase.passowrd}")
    private String passowrd;

    @Value("${couchbase.timeout}")
    private long timeOut;

    //@Value("${couchbase.ioPoolSize}")
    private int ioPoolSize;

    //@Value("${couchbase.computationPoolSize}")
    private int computationPoolSize;

    @Value("${couchbase.isBinary:true}")
    private boolean isBinary;

    @Value("${couchbase.cacheExpiry}")
    private int cacheExpiry;

   @Value("${hscache.couchbase.namespace:cache}")
    private String nameSpace;

    @Bean
    public HSCouchbaseCacheManager create() {
        String[] nodes = hosts.split(",");

        int cpus = Runtime.getRuntime().availableProcessors();
        int nodeSize = nodes.length;
        CouchbaseEnvironment env = DefaultCouchbaseEnvironment.builder()
            .connectTimeout(timeOut) // 默认连接超时时间8s
            .ioPoolSize(intPropertyOr(ioPoolSize, Math.min(Math.max(cpus, nodeSize), nodeSize * 3)))// 默认io线程数
            .computationPoolSize(intPropertyOr(computationPoolSize, Math.min(cpus, 8))) // 默认运算线程数
            .build();
        CouchbaseCluster couchbaseCluster = CouchbaseCluster.create(env, nodes);
        Bucket bucket = couchbaseCluster.openBucket(bucketName, passowrd);
        HSCouchbaseCacheBuilder couchbaseCacheBuilder = HSCouchbaseCacheBuilder.newInstance(bucket, nameSpace,isBinary);
        couchbaseCacheBuilder.setCacheExpiry(cacheExpiry);
        return new HSCouchbaseCacheManager(couchbaseCacheBuilder);
    }

    protected static int intPropertyOr(int ioPoolSize, int def) {
        if (ioPoolSize<=0) {
            return def;
        }
        return ioPoolSize;
    }

/*    public static void main(String[] args){
        CouchbaseConfig c=new CouchbaseConfig();
        HSCouchbaseCacheBuilder cacheBuilder = c.create();
    }*/
}
