package com.apec.framework.cache.support;

import com.apec.framework.cache.service.CouchbaseService;
import com.apec.framework.cache.service.HsCouchbaseServiceImpl;
import com.couchbase.client.java.Bucket;

/**
 * A builder for {@link HsCouchbaseServiceImpl} instance.
 *
 * <AUTHOR>
 */
public class HSCouchbaseCacheBuilder {

    private static final int DEFAULT_TTL = 0;

    private static final String DEFAULT_NAME = "def";

    private String namespace;
    private Bucket bucket;

    /**
     * 缓存有效时间
     */
    private int cacheExpiry;

    /**
     * 使用二进制存储
     */
    private Boolean binary = true;

    /**
     * 本地缓存实现类
     */

    protected HSCouchbaseCacheBuilder() {
        this.cacheExpiry = DEFAULT_TTL;
    }

    /**
     * Create a new builder instance with the given {@link Bucket}.
     *
     * @param bucket the bucket to use
     * @return a new builder
     */
    public static HSCouchbaseCacheBuilder newInstance(Bucket bucket) {
        return new HSCouchbaseCacheBuilder().withBucket(bucket);
    }

    public static HSCouchbaseCacheBuilder newInstance(Bucket bucket, String namespace) {
        return new HSCouchbaseCacheBuilder().withBucket(bucket).withNamespace(namespace);
    }

    public static HSCouchbaseCacheBuilder newInstance(Bucket bucket, String namespace, Boolean binary) {
        return new HSCouchbaseCacheBuilder().withBucket(bucket).withNamespace(namespace).withBinary(binary);
    }

    /**
     * Give a bucket to the cache to be built.
     *
     * @param bucket the bucket
     * @return this builder for chaining.
     */
    public HSCouchbaseCacheBuilder withBucket(Bucket bucket) {
        if (bucket == null) {
            throw new NullPointerException("A non-null Bucket is required for all cache builders");
        }
        this.bucket = bucket;
        return this;
    }

    /**
     * Give a default expiration (or TTL) to the cache to be built.
     *
     * @param expiration the expiration delay in milliseconds.
     * @return this builder for chaining.
     */
    public HSCouchbaseCacheBuilder withExpirationInMillis(int expiration) {
        this.cacheExpiry = expiration;
        return this;
    }

    public HSCouchbaseCacheBuilder withNamespace(String namespace) {
        this.namespace = namespace;
        return this;
    }

    public HSCouchbaseCacheBuilder withBinary(Boolean binary) {
        this.binary = binary;
        return this;
    }

    /**
     * 通过附加属性指定超时时间(秒)
     *
     * @param cacheExpiry the cacheExpiry to set
     */
    public void setCacheExpiry(int cacheExpiry) {
        this.cacheExpiry = cacheExpiry;
    }


    /**
     * Build a new {@link HsCouchbaseServiceImpl} with the specified name.
     *
     * @param cacheName the name of the cache
     * @return a {@link HsCouchbaseServiceImpl} instance
     */
    public CouchbaseService build(String cacheName) {
        if(DEFAULT_NAME.equals(cacheName))
            this.namespace=null;
            cacheName=null;
        HsCouchbaseServiceImpl couchbaseCache =
            new HsCouchbaseServiceImpl(this.namespace, cacheName, this.bucket, this.binary);
        couchbaseCache.setTtl(this.cacheExpiry);
        return couchbaseCache;
    }

}
