package com.apec.framework.cache.counter;

import com.apec.framework.cache.support.HSCouchbaseKeyBuilder;
import com.apec.framework.common.CouchbaseExpiryHandler;
import com.couchbase.client.java.Bucket;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 计数器
 * <AUTHOR>
 *
 */
public class HSCouchbaseCounter implements HSCounter {
    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    private final Bucket client;

    private final HSCouchbaseKeyBuilder keyBuilder;

    public HSCouchbaseCounter(Bucket client, HSCouchbaseKeyBuilder keyBuilder) {
        this.client = client;
        this.keyBuilder = keyBuilder;
    }

    @Override
    public long counter(String key) {
        return counter(key, 1);
    }

    @Override
    public long counter(String key, long delta) {
        return counter(key, delta, 1);
    }

    @Override
    public long counter(String key, long delta, long initial) {
        return counter(key, delta, initial, 0);
    }

    @Override
    public long counter(String key, long delta, long initial, long expiry) {
        key = keyBuilder.buildKey(key);
        Bucket bucket = this.client;
        try {
            if (expiry == 1) {
                // 用于精准流控
                key += "." + (System.currentTimeMillis() / 1000) % 2;
            }
            expiry = CouchbaseExpiryHandler.expiryTimeHandler(expiry);
            return bucket.counter(key, delta, initial, (int) expiry).content();
        } catch (Exception e) {
            LOGGER.error("couchbase HSCouchbaseCounter: ", e);
            return -1l;
        }
    }

}
