package com.apec.framework.cache.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.ParserConfig;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.apec.framework.exception.CouchbaseException;
import com.apec.framework.cache.counter.HSCouchbaseCounter;
import com.apec.framework.cache.counter.HSCounter;
import com.apec.framework.cache.datastructures.HSCollectionCache;
import com.apec.framework.cache.datastructures.HSCouchbaseCollectionCache;
import com.apec.framework.cache.support.HSCouchbaseKeyBuilder;
import com.apec.framework.common.CouchbaseCharacterHandler;
import com.apec.framework.common.CouchbaseExpiryHandler;
import com.apec.framework.common.TypeConvert;
import com.apec.framework.common.TypeFilter;
import com.apec.framework.common.constant.ErrorCodeConsts;
import com.couchbase.client.java.Bucket;
import com.couchbase.client.java.ReplicaMode;
import com.couchbase.client.java.bucket.BucketManager;
import com.couchbase.client.java.document.*;
import com.couchbase.client.java.document.json.JsonArray;
import com.couchbase.client.java.document.json.JsonObject;
import com.couchbase.client.java.document.json.JsonValue;
import com.couchbase.client.java.error.*;
import com.couchbase.client.java.view.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.Cache;
import org.springframework.cache.support.SimpleValueWrapper;
import org.springframework.stereotype.Component;
import rx.Observable;
import rx.functions.Func1;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * Created by liliwei on 2018/4/27.
 */
public class HsCouchbaseServiceImpl implements CouchbaseService
{
    private static final Logger log = LoggerFactory.getLogger(HsCouchbaseServiceImpl.class);
    /**
     * 保护null
     */
    private static final Cache.ValueWrapper EMPTY_WRAPPER = new SimpleValueWrapper(null);

    /**
     * The actual SDK {@link Bucket} instance.
     */
    private final Bucket client;

    /**
     * cache 命名空间，一般一个应用一个命名空间.
     */
    private final String namespace;

    /**
     * cache 名称，一个应用多个名称，每个名称提供不同的功能
     */
    private final String name;

    /**
     * TTL value for objects in this cache
     */
    private int ttl;

    /**
     * 查询超时时间（ms）
     */
    private final long timeout = 500L;

    /**
     * 是否默认使用二进制
     */
    private final boolean binary;

    /**
     * Delimiter for separating the name from the key in the document id of
     * objects in this cache
     */
    private final String DELIMITER = ":";

    /**
     * The design document of the view used by this cache to retrieve documents
     * in a specific namespace.
     */
    private final String CACHE_DESIGN_DOCUMENT = "hscache";

    /**
     * The suffix of the name of the view used by this cache to retrieve
     * documents in a
     * specific namespace.
     */
    private final String CACHE_VIEW_SUFFIX = "_names";

    /**
     * 超时判断的view
     */
    private final String EXPIRY_VIEW_NAME = "ttl";

    /**
     * Determines whether to always use the flush() method to clear the cache.
     */
    private Boolean alwaysFlush = false;

    /**
     * key创建类
     */
    private final HSCouchbaseKeyBuilder keyBuilder;

    private final HSCounter counter;
    private final HSCollectionCache collectionCache;

    static {
        // 支持自动类型判断
        ParserConfig.getGlobalInstance().setAutoTypeSupport(true);
    }

    /**
     * @param namespace
     * @param name
     * @param client
     */
    public HsCouchbaseServiceImpl(final String namespace, final String name, final Bucket client, final boolean binary) {
        this.namespace = namespace;
        this.name = name;
        this.client = client;
        this.binary = binary;

        this.keyBuilder = new HSCouchbaseKeyBuilder(namespace, name, DELIMITER);

        this.counter = new HSCouchbaseCounter(client, keyBuilder);
        this.collectionCache = new HSCouchbaseCollectionCache(client, keyBuilder);

        if (!getAlwaysFlush()) {
            ensureViewExists();
        }
    }

    public HSCollectionCache getCollectionCache() {
        return collectionCache;
    }

    public HSCounter getCounter() {
        return counter;
    }

    public void put(Object key, Object value, long expiry) {
        put(key, value, expiry, true);
    }

    public void put(Object key, Object value, boolean override) {
        put(key, value, ttl, override);
    }

    @Override
    public void put(Object key, Object value, long expiry, boolean override) {
        String documentId = keyBuilder.buildKey(key);
        if (expiry < 0) {
            return;
        }
        expiry = CouchbaseExpiryHandler.expiryTimeHandler(expiry);
        try {
            Document<?> document;
            if (value instanceof Document<?>) {
                document = (Document<?>) value;
                expiry = document.expiry();
            } else if (binary) {
                if (!(value instanceof Serializable)) {
                    throw new IllegalArgumentException(String.format("Value %s of type %s is not Serializable",
                                                                     value.toString(), value.getClass().getName()));
                }
                document = SerializableDocument.create(documentId, (int) expiry, (Serializable) value);
            } else {
                document = TypeConvert.convertToDocument(documentId, value, expiry);
            }
            if (override) {
                client.upsert(document);
            } else {
                client.insert(document);
            }
        } catch (DocumentAlreadyExistsException e) {
            throw new CouchbaseException(ErrorCodeConsts.CACHE_EXCEPTION,e);
        } catch (Exception e) {
            log.error("couchbase put: ", e);
        }
    }

    @Override
    public void append(Object key, String value) {
        String documentId = keyBuilder.buildKey(key);
        try {
            client.append(TypeConvert.convertToDocument(documentId, value));
        } catch (DocumentDoesNotExistException e) {
            put(documentId, value);
        } catch (Exception e) {
            log.error("append: ", e);
        }
    }

    @SuppressWarnings("unchecked")
    public <V> V get(Object key, Class<V> valueType) {
        String documentId = keyBuilder.buildKey(key);
        try {
                Document<?> document = getDocument(valueType, documentId, client);
                Object value = document;
                // 非Document 对象会被转换
                if (!Document.class.isAssignableFrom(valueType)) {
                    value = TypeConvert.convertStrToObj(document.content().toString(), valueType);
                }
                return (V) value;
        } catch (CouchbaseException e) {
            return null;
        } catch (Exception e) {
            log.error("get: ", e);
            return null;
        }

    }

    @Override
    public <T> T get(Object key, Callable<T> valueLoader)
    {
        return null;
    }

    @SuppressWarnings("unchecked")
    private <V> Document<?> getDocument(Class<V> valueType, String documentId, Bucket bucket) {
        Document<?> document = null;
        if (TypeFilter.checkType(valueType)) {
            try {
                document = getStringDocument(documentId, bucket);
            } catch (TranscodingException e) {
                document = getJsonDocument(documentId, bucket);
            }
        } else {
            Class<? extends Document<?>> target = JsonDocument.class;
            if (Document.class.isAssignableFrom(valueType)) {
                target = (Class<? extends Document<?>>) valueType;
            }
            try {
                document = getRawDocument(documentId, bucket, target);
            } catch (TranscodingException e) {
                document = getStringDocument(documentId, bucket);
            }
        }
        if (document == null) {
            throw new CouchbaseException(ErrorCodeConsts.CACHE_EXCEPTION,"couchbase document is null");
        }
        return document;
    }

    private Document<?> getRawDocument(String documentId, Bucket bucket, Class<? extends Document<?>> target) {
        Document<?> document;
        try {
            document = bucket.get(documentId, target, timeout, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            if (e.getCause() instanceof TimeoutException) {
                ReplicaMode[] modeValues = ReplicaMode.values();
                log.warn("DocumentId:{} 读缓存超时，尝试从备份读取", documentId);
                // 依次从备份1，备份2，备份3读取，直到取得数据，跳过ReplicaMode.ALL
                for (int j = 1; j < modeValues.length; j++) {
                    try {
                        List<? extends Document<?>> fromReplica = bucket.getFromReplica(documentId, ReplicaMode.values()[j], target, timeout, TimeUnit.MILLISECONDS);
                        if (fromReplica.size() == 1) {
                            document = fromReplica.get(0);
                            log.warn("从备份 {} 获得 {} 数据", j, documentId);
                            break;
                        }
                    } catch (Exception e1) {
                        throw e1;
                    }
                }
                throw e;
            } else {
                throw e;
            }
        }
        return document;
    }

    private JsonDocument getJsonDocument(String documentId, Bucket bucket) {

        return (JsonDocument) getRawDocument(documentId, bucket, JsonDocument.class);
    }

    private StringDocument getStringDocument(String documentId, Bucket bucket) {
        return (StringDocument) getRawDocument(documentId, bucket, StringDocument.class);
    }

    @Override
    public boolean exists(Object key) {
        String documentId = keyBuilder.buildKey(key);
        try {
            return client.exists(documentId);
        } catch (Exception e) {
            log.error("couchbase exists documentId [{}]: ",documentId, e);
            return false;
        }
    }

    public long ttl(Object key) {
        String documentId = keyBuilder.buildKey(key);
        List<ViewRow> views;
        try {
            ViewQuery viewQuery = ViewQuery.from(CACHE_DESIGN_DOCUMENT, EXPIRY_VIEW_NAME);
            ViewRow viewRow = null;
            Long expiry = -1l;
            try {
                viewQuery.key(CouchbaseCharacterHandler.quotesReplace(documentId));
                views = client.query(viewQuery).allRows();
                if (views.size() == 0) {
                    throw new CouchbaseException(ErrorCodeConsts.CACHE_EXCEPTION,"couchbase documentId not exist:"+documentId);
                } else {
                    viewRow = views.get(0);
                    expiry = Long.parseLong(viewRow.value().toString());
                }
                if (expiry == 0) {
                    return 0;
                } else {
                    long ttl = expiry - (System.currentTimeMillis() / 1000);
                    return ttl < 0 ? ttl(key) : ttl;
                }

            } catch (ViewDoesNotExistException ex) {
                // List<View> list = new ArrayList<>();
                // list.add(DefaultView.create("ttl", "function (doc, meta) {emit(meta.id, meta.expiration);}"));
                // client.bucketManager().upsertDesignDocument(DesignDocument.create("expiration", list));
                BucketManager bucketManager = client.bucketManager();
                DesignDocument doc = null;
                try {
                    doc = bucketManager.getDesignDocument(CACHE_DESIGN_DOCUMENT);
                } catch (Exception e) {
                    if (log.isDebugEnabled()) {
                        log.debug("Unable to retrieve design document " + CACHE_DESIGN_DOCUMENT, e);
                    }
                }

                String function = "function (doc, meta) {emit(meta.id, meta.expiration);}";//
                View v = DefaultView.create(EXPIRY_VIEW_NAME, function);

                if (doc == null) {
                    List<View> viewList = new ArrayList<View>(1);
                    viewList.add(v);
                    doc = DesignDocument.create(CACHE_DESIGN_DOCUMENT, viewList);
                } else {
                    doc.views().add(v);
                }

                bucketManager.upsertDesignDocument(doc);

                return ttl(key);
            }
        } catch (Exception e) {
            log.error("couchbase ttl: ", e);
            return -1;
        }
    }

    @Override
    public boolean updateExpires(Object key, long expiry) {
        String documentId = keyBuilder.buildKey(key);
        try {
            if (expiry < 0) {
                return client.remove(documentId) == null ? false : true;
            }
            expiry = CouchbaseExpiryHandler.expiryTimeHandler(expiry);
            return client.touch(documentId, (int) expiry);
        } catch (DocumentDoesNotExistException e) {
            throw new CouchbaseException(ErrorCodeConsts.CACHE_EXCEPTION,"couchbase documentId not exist:"+documentId);
        } catch (Exception e) {
            log.error("couchbase updateExipres [{}]: ",documentId, e);
            return false;
        }
    }

    public boolean rename(Object oldKey, Object newKey) {
        return rename(oldKey, newKey, true);
    }

    public boolean rename(Object oldKey, Object newKey, boolean override) {
        String oldKeyStr = keyBuilder.buildKey(oldKey);
        String newKeyStr = keyBuilder.buildKey(newKey);
        if (oldKeyStr.equals(newKeyStr)) {
            return true;
        }
        try {
            // TODO 检查代码，这个功能存在问题，但用处不大
            Document<?> document;
            long expiry = 0;
            try {
                document = client.get(oldKeyStr, StringDocument.class);
                if (document == null) {
                    throw new CouchbaseException(ErrorCodeConsts.CACHE_EXCEPTION,"couchbase documentId not exist");
                }
                expiry = document.expiry() == 0 ? 0 : ttl(oldKey);
                expiry = CouchbaseExpiryHandler.expiryTimeHandler(expiry);
                //这里通过ttl可能查询出未经清理expire值
                if (override) {
                    client.upsert(StringDocument.create(newKeyStr, (int) expiry, document.content().toString()));
                } else {
                    client.insert(StringDocument.create(newKeyStr, (int) expiry, document.content().toString()));
                }
            } catch (TranscodingException e) {
                try {
                    document = client.get(oldKeyStr, JsonDocument.class);
                    //这里通过ttl可能查询出未经清理expire值
                    if (override) {
                        client.upsert(JsonDocument.create(newKeyStr, (int) expiry, (JsonObject) document.content()));
                    } else {
                        client.insert(JsonDocument.create(newKeyStr, (int) expiry, (JsonObject) document.content()));
                    }
                } catch (TranscodingException e1) {
                    document = client.get(oldKeyStr, JsonArrayDocument.class);
                    //这里通过ttl可能查询出未经清理expire值
                    if (override) {
                        client.upsert(
                            JsonArrayDocument.create(newKeyStr, (int) expiry, (JsonArray) document.content()));
                    } else {
                        client.insert(
                            JsonArrayDocument.create(newKeyStr, (int) expiry, (JsonArray) document.content()));
                    }
                }
            }
            client.remove(oldKeyStr);
            return true;
        } catch (DocumentAlreadyExistsException e) {
            throw new CouchbaseException(ErrorCodeConsts.CACHE_EXCEPTION,"couchbase rename false:"+oldKeyStr);
        } catch (Exception e) {
            log.error("couchbase rename false: ", e);
            return false;
        }
    }

    //	@Override
    //	public boolean lock(Object key, long lockTime) {
    //		String keyStr = TypeConvert.convertObjToStr(key);
    //		ValueFilter.checkKey(keyStr);
    //		Bucket bucket = getBucket();
    //		try {
    //			lockMap.put(key,bucket.getAndLock(keyStr, (int)lockTime).cas());
    //			return true;
    //		} catch (Exception e) {
    //			ExceptionFilter.checkException(e);
    //			LOGGER.error("lock: ", e);
    //			return false;
    //		}
    //	}

    //	@Override
    //	public boolean lock(Object key, long lockTime, long timeout) {
    //		String keyStr = TypeConvert.convertObjToStr(key);
    //		ValueFilter.checkKey(keyStr);
    //		Bucket bucket = getBucket();
    //		try {
    //			lockMap.put(key,bucket.getAndLock(keyStr, (int)lockTime, timeout, TimeUnit.SECONDS).cas());
    //			return true;
    //		} catch (Exception e) {
    //			ExceptionFilter.checkException(e);
    //			LOGGER.error("lock: ", e);
    //			return false;
    //		}
    //	}

    //	@Override
    //	public boolean unLock(Object key) {
    //		String keyStr = TypeConvert.convertObjToStr(key);
    //		ValueFilter.checkKey(keyStr);
    //		Bucket bucket = getBucket();
    //		try {
    //			if(bucket.unlock(keyStr, lockMap.get(key))){
    //				lockMap.remove(key);
    //				return true;
    //			}
    //			return false;
    //		} catch (Exception e) {
    //			ExceptionFilter.checkException(e);
    //			LOGGER.error("unLock: ", e);
    //			return false;
    //		}
    //	}

    public void close() {
        try {
            client.close();
        } catch (Exception e) {
            log.error("close: ", e);
        }
    }

    // Spring cahce implements
    public String getName() {
        return name;
    }

    public Bucket getNativeCache() {
        return client;
    }

    public Cache.ValueWrapper get(Object key) {
        Class<? extends Document<?>> target = RawJsonDocument.class;
        if (binary) {
            target = SerializableDocument.class;
        }
        // Spring Cache，默认启用本地缓存，其中本地缓存实现类，需要注入，否则相当于未启用本地缓存
        Document<?> doc = get(key, target);
        if (doc == null) {
            return null; // 缓存失效，访问实际的方法
        } else if (doc.content() == null) {
            return EMPTY_WRAPPER;// 缓存内容为空，不访问实际的方法
        }

        if (doc instanceof RawJsonDocument) {
            String content = ((RawJsonDocument) doc).content();
            JSONObject jo = JSON.parseObject(content);
            if ("supported".equals(jo.get("@type"))) {
                // 内置类型
                return new SimpleValueWrapper(jo.get("@value"));
            }
            // 由于Spring 3.x接口问题，不知道缓存值的类型，需要通过fastjson转换保存类型信息
            Object value = content;
            if (jo.containsKey("@value")) {
                value = jo.get("@value");
            }
            Object obj = JSON.parse(value.toString());
            return new SimpleValueWrapper(obj);
        } else {
            return new SimpleValueWrapper(doc.content());
        }
    }

    //    @SuppressWarnings("unchecked")
    //    @Override
    //    public <T> T get(Object key, Callable<T> valueLoader) {
    //        try {
    //            return valueLoader.call();
    //        } catch (Exception e) {
    //            ExceptionFilter.checkException(e);
    //            LOGGER.error("get: ", e);
    //        }
    //        return (T) get(key);
    //    }

    @Override
    public Bucket getBucket()
    {
         return client;
    }

    @Override
    public void put(Object key, Object value) {
        if (value == null) {
            try {
                evict(key);
            } catch (Exception e) {
                // ignore
            }
            return;
        }

        String documentId = keyBuilder.buildKey(key);
        Document<?> doc;
        if (binary) {
            if (!(value instanceof Serializable)) {
                throw new IllegalArgumentException(String.format("Value %s of type %s is not Serializable",
                                                                 value.toString(), value.getClass().getName()));
            }
            doc = SerializableDocument.create(documentId, ttl, (Serializable) value);
        } else {
            String jsonString;
            if (TypeFilter.checkType(value) && !(value instanceof Number)) {
                // 内置类型 JSON
                JsonObject jo = JsonValue.jo();
                jo.put("@type", "supported");
                jo.put("@value", value);
                jsonString = jo.toString();
            } else {
                // 由于Spring 3.x接口问题，不知道待缓存值的类型，需要通过 fastjson 转换保存类型信息
                jsonString = JSON.toJSONStringWithDateFormat(value, "yyyy-MM-dd HH:mm:ss.SSS",
                                                             SerializerFeature.WriteClassName, SerializerFeature.IgnoreErrorGetter);
                if (jsonString != null && jsonString.charAt(0) != '{') {
                    if (value instanceof Number) {
                        jsonString = "{\"@value\":\"" + jsonString + "\"}";
                    } else if (jsonString.startsWith("[{")) {
                        jsonString = "{\"@value\":" + jsonString + "}";
                    } else {
                        throw new IllegalArgumentException("Unsupported type for HSCache: " + value.getClass());
                    }
                }
            }
            doc = RawJsonDocument.create(documentId, ttl, jsonString);
        }
        put(key, doc, true);
    }

    @Override
    public ValueWrapper putIfAbsent(Object key, Object value)
    {
        return null;
    }

    //    @Override
    //    public ValueWrapper putIfAbsent(Object key, Object value) {
    //        ValueWrapper valueWrapper = get(key);
    //        if (valueWrapper == null) {
    //            put(key, value);
    //            return null;
    //        }
    //        return valueWrapper;
    //    }

    @Override
    public void evict(Object key) {
        String documentId = keyBuilder.buildKey(key);
        try {
            client.remove(documentId);
        } catch (DocumentDoesNotExistException e) {
            throw new CouchbaseException(ErrorCodeConsts.CACHE_EXCEPTION,"couchbase doucment not in exist:"+documentId);
        } catch (Exception e) {
            log.error("couchbase evict: ", e);
        }
    }

    /**
     * 兼容Spring cache 及 本地缓存
     */
    public void clear() {
        if (getAlwaysFlush()) {
            try {
                client.bucketManager().flush();
            } catch (Exception e) {
                log.error("Couchbase flush error: ", e);
            }
        } else {
            evictAllDocuments();
        }
    }

    private void evictAllDocuments() {
        ViewQuery query = ViewQuery.from(CACHE_DESIGN_DOCUMENT, namespace + CACHE_VIEW_SUFFIX);
        query.stale(Stale.FALSE);
        if (name == null || name.trim().length() == 0) {
            query.key("");
        } else {
            query.key(name);
        }

        client.async().query(query).flatMap(ROW_IDS_OR_ERROR)
            .flatMap(new Func1<String, Observable<? extends Document<?>>>() {
                @Override
                public Observable<? extends Document<?>> call(String id) {
                    // 移除本地缓存
                    return client.async().remove(id, SerializableDocument.class);
                }
                // FIXME view 删除后会卡住，需要找原因
            }).onErrorReturn(new Func1<Throwable, Document<?>>() {

            @Override
            public Document<?> call(Throwable t) {
                log.error("couchbase query error:", t);
                return null;
            }

        }).toBlocking().lastOrDefault(null); //ignore empty cache
    }

    private void ensureViewExists() {
        BucketManager bucketManager = client.bucketManager();
        DesignDocument doc = null;

        try {
            doc = bucketManager.getDesignDocument(CACHE_DESIGN_DOCUMENT);
        } catch (Exception e) {
            if (log.isDebugEnabled()) {
                log.debug("Unable to retrieve design document " + CACHE_DESIGN_DOCUMENT, e);
            }
        }

        if (doc != null) {
            for (View view : doc.views()) {
                if ((namespace + CACHE_VIEW_SUFFIX).equals(view.name())) {
                    return;
                }
            }
        }

        String function = "function (doc, meta) {" //
                          + "var tokens = meta.id.split('" + DELIMITER + "'); "//
                          + "if(tokens.length > 2 && tokens[0] == '" + namespace + "') "//
                          + "emit(tokens[1]);"//
                          + "}";//
        View v = DefaultView.create(namespace + CACHE_VIEW_SUFFIX, function);

        if (doc == null) {
            List<View> viewList = new ArrayList<View>(1);
            viewList.add(v);
            doc = DesignDocument.create(CACHE_DESIGN_DOCUMENT, viewList);
        } else {
            doc.views().add(v);
        }

        bucketManager.upsertDesignDocument(doc);
    }

    /** Converts View Rows to the associated document's ID */
    private static final Func1<AsyncViewRow, String> ROW_TO_ID = new Func1<AsyncViewRow, String>() {
        @Override
        public String call(AsyncViewRow asyncViewRow) {
            return asyncViewRow.id();
        }
    };

    /**
     * Converts a JsonObject view error into an Observable&lt;String&gt; that
     * emits
     * a{@link com.couchbase.client.java.error.QueryExecutionException} wrapping
     * the error.
     */
    private static final Func1<JsonObject, Observable<String>> JSON_TO_ONERROR = new Func1<JsonObject, Observable<String>>() {
        @Override
        public Observable<String> call(JsonObject jsonError) {
            return Observable.error(new QueryExecutionException("Error during view query execution: ", jsonError));
        }
    };

    /**
     * Out of an {@link AsyncViewResult}, extract the stream of document IDs or
     * emit an error if unsuccessful.
     */
    private static Func1<AsyncViewResult, Observable<String>> ROW_IDS_OR_ERROR = new Func1<AsyncViewResult, Observable<String>>() {
        @Override
        public Observable<String> call(AsyncViewResult asyncViewResult) {
            if (asyncViewResult.success()) {
                return asyncViewResult.rows().map(ROW_TO_ID);
            } else {
                return asyncViewResult.error().flatMap(JSON_TO_ONERROR);
            }
        }
    };

    /**
     * Gets whether the cache should always use the flush() method to clear all
     * documents.
     *
     * @return returns whether the cache should always use the flush() method to
     * clear all documents.
     */
    public Boolean getAlwaysFlush() {
        return alwaysFlush;
    }

    /**
     * Sets whether the cache should always use the flush() method to clear all
     * documents.
     * <p>
     * This is a very destructive action, so only use it with care (for instance
     * other caches or clients may store
     * unrelated data in the same Bucket as this cache, and flushing will also
     * destroy said data). Note that flush may not
     * be enabled on the underlying bucket.
     *
     * @param alwaysFlush Whether the cache should always use the flush() method
     * to clear all documents.
     */
    public void setAlwaysFlush(Boolean alwaysFlush) {
        this.alwaysFlush = alwaysFlush;
    }

    /**
     * @return the ttl
     */
    public int getTtl() {
        return ttl;
    }

    /**
     * @param ttl the ttl to set
     */
    public void setTtl(int ttl) {
        this.ttl = ttl;
    }

    /**
     * @return the namespace
     */
    public String getNamespace() {
        return namespace;
    }

    /**
     * @return the timeout
     */
    public long getTimeout() {
        return timeout;
    }

    /**
     * @return the binary
     */
    public boolean isBinary() {
        return binary;
    }
}
