package com.apec.framework.cache.service;

import com.apec.framework.cache.counter.HSCounter;
import com.apec.framework.cache.datastructures.HSCollectionCache;
import com.couchbase.client.java.Bucket;
import org.springframework.cache.Cache;

import javax.management.openmbean.InvalidKeyException;
import java.io.Closeable;

/**
 * Created by l<PERSON><PERSON> on 2018/4/27.
 */
public interface CouchbaseService extends Cache, Closeable
{

    public Bucket getBucket();

    /**
     * 执行覆盖式写入操作;
     * key不合规时抛出{@link InvalidKeyException};
     *
     * @param key 数据id
     * @param value 待插入数据,
     *        JSON模式仅支持基本类型（Char除外），及POJO,简单List<POJO>，Date,BigDecimal,Map不支持
     *        除非使用二进制保存
     * @return
     */
    public void put(Object key, Object value);

    /**
     * 执行覆盖式写入操作,并为key设置过期时间;
     * expiry以秒为单位,expiry<0时,数据写入瞬间失效;expiry=0时数据永久有效;expiry>0时,经过指定的超时时间后,数据失效;
     * key不合规时抛出{@link InvalidKeyException};
     *
     * @param key 数据id
     * @param value 待插入数据
     * @param expiry 数据有效时间(秒)
     * @return
     */
    public void put(Object key, Object value, long expiry);

    /**
     * override为true,执行覆盖式写入操作;override为false,仅当key不存在时执行数据写入;
     * key不合规时抛出{@link InvalidKeyException};
     * override为false,且当key已存在时,抛出{@link KeyAlreadyExistsException}
     *
     * @param key 数据id
     * @param value 待插入数据
     * @param override true表示覆盖写入,false表示非覆盖写入
     * @return
     */
    public void put(Object key, Object value, boolean override);

    /**
     * override为true,执行覆盖式写入操作并设置有效时间;override为false,仅当key不存在时执行数据写入并设置有效时间;
     * expiry以秒为单位,expiry<0时,数据写入瞬间失效;expiry=0时数据永久有效;expiry>0时,经过指定的超时时间后,数据失效;
     * key不合规时抛出{@link InvalidKeyException};
     * override为false,且当key已存在时,抛出{@link KeyAlreadyExistsException}
     *
     * @param key 数据id
     * @param value 待插入数据
     * @param expiry 数据有效时间(秒)
     * @param override true表示覆盖写入,false表示非覆盖写入
     * @return
     */
    public void put(Object key, Object value, long expiry, boolean override);


    /**
     * 在key现有值后追加value;
     * 目前仅支持value类型为String的数据;
     * key不存在时,创建并插入value;
     * key不合规时抛出{@link InvalidKeyException};
     *
     * @param key 数据id
     * @param value
     * @return 操作成功返回true,否则返回false
     */
    public void append(Object key, String value);

    /**
     * 查询key的值并以指定类型返回;
     * key不存在时返回null;
     * key不合规时抛出{@link InvalidKeyException};
     * 分布式缓存底层异常,日志记录异常信息并返回null
     */
    public <V> V get(Object key, Class<V> valueType);


    /**
     * 判断key是否存在;
     * key不合规时抛出{@link InvalidKeyException};
     * 分布式缓存底层异常,日志记录异常信息并返回false
     *
     * @param key 数据id
     * @return key已存在返回true,否则返回false
     */
    public boolean exists(Object key);

    /**
     * 返回指定key的剩余有效时间,以秒为单位,由于查询的是视图,该方法返回的可能不是最新数据,慎用;
     * key不合规时抛出{@link InvalidKeyException};
     * key不存在时抛出{@link KeyDoesNotExistException};
     * 分布式缓存底层异常,日志记录异常信息并返回-1
     *
     * @param key 数据id
     * @return 剩余有效时间
     */
    public long ttl(Object key);

    /**
     * 更新指定key的过期时间,以秒为单位;
     * expiry以秒为单位,expiry<0时,数据写入瞬间失效;expiry=0时数据永久有效;expiry>0时,经过指定的超时时间后,数据失效;
     * key不合规时抛出{@link InvalidKeyException};
     * key不存在时抛出{@link KeyDoesNotExistException};
     * 分布式缓存底层异常,日志记录异常信息并返回false
     *
     * @param key 数据id
     * @param expiry 有效时间
     * @return 更新成功返回true,否则返回false
     */
    public boolean updateExpires(Object key, long expiry);

    /**
     * 重命名key,newKey已存在时,进行覆盖式重命名;
     * oldKey or newKey不合规时抛出{@link InvalidKeyException};
     * oldKey不存在时抛出{@link KeyDoesNotExistException};
     * 分布式缓存底层异常,日志记录异常信息并返回false
     *
     * @param: oldKey
     * @param: newKey
     * @return 操作成功返回true,否则返回false
     */
    public boolean rename(Object oldKey, Object newKey);

    /**
     * 重命名key,当override为true,newKey已存在时,进行覆盖式重命名;
     * 当override为false,newKey已存在时,抛出异常;
     * oldKey or newKey不合规时抛出{@link InvalidKeyException};
     * oldKey不存在时抛出{@link KeyDoesNotExistException};
     * 当override为false且newKey已存在时抛出{@link KeyAlreadyExistsException};
     * 分布式缓存底层异常,日志记录异常信息并返回false
     *
     * @param: oldKey
     * @param: newKey
     * @return 操作成功返回true,否则返回false
     */
    public boolean rename(Object oldKey, Object newKey, boolean override);

    /**
     * 移除指定的key
     * key不合规时抛出{@link InvalidKeyException};
     * key不存在时抛出{@link KeyDoesNotExistException};
     * 分布式缓存底层异常,日志记录异常信息
     *
     * @param key 数据id
     * @return
     */
    public void evict(Object key);

    /**
     * 清空所有数据;
     * 分布式缓存底层异常,日志记录异常信息
     */
    public void clear();

    /**
     * 释放连接资源;
     * 分布式缓存底层异常,日志记录异常信息
     */
    public void close();

    //	/**
    //	 * 获取key的分布式锁,并持有lockTime秒
    //	 * key不合规时抛出{@link InvalidKeyException};
    //	 * @param key
    //	 * @param lockTime 锁定时间
    //	 * @return 加锁成功返回true,否则返回false
    //	 */
    //	boolean lock(Object key,long lockTime);
    //	/**
    //	 * 获取key的分布式锁,并持有lockTime秒,timeout秒内没有获取到锁返回false
    //	 * key不合规时抛出{@link InvalidKeyException};
    //	 * @param key
    //	 * @param lockTime 锁定时间
    //	 * @param timeout 加锁超时时间
    //	 * @return 加锁成功返回true,否则返回false
    //	 */
    //	boolean lock(Object key,long lockTime,long timeout);
    //	/**
    //	 * 释放key的分布式锁,锁不存在时返回false
    //	 * key不合规时抛出{@link InvalidKeyException};
    //	 * @param key
    //	 * @return 解锁成功返回true,否则返回false
    //	 */
    //	public boolean unLock(Object key);

    /**
     * 获取分布式缓存计数器
     *
     * @return
     */
    public HSCounter getCounter();

    /**
     * 获取集合类数据缓存
     *
     * @return
     */
    public HSCollectionCache getCollectionCache();
}
