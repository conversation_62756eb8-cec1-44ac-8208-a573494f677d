package com.apec.framework.cache.manager;

import com.apec.framework.cache.counter.HSCounter;
import com.apec.framework.cache.datastructures.HSCollectionCache;
import com.apec.framework.cache.service.CouchbaseService;
import com.couchbase.client.java.Bucket;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.support.SimpleValueWrapper;

import java.io.Closeable;
import java.util.concurrent.Callable;

/**
 * cache管理接口
 *
 * <AUTHOR>
 *
 */
public interface HSCacheManager extends CacheManager, Closeable {

    public CouchbaseService getHSCache();

    public CouchbaseService getHSCache(String name);

    public static class HSNULLCache implements CouchbaseService
    {

        @Override
        public Bucket getBucket()
        {
            return null;
        }

        @Override
        public void put(Object key, Object value)
        {

        }

        @Override
        public ValueWrapper putIfAbsent(Object key, Object value)
        {
            return new SimpleValueWrapper(null);
        }

        @Override
        public void put(Object key, Object value, long expiry)
        {

        }

        @Override
        public void put(Object key, Object value, boolean override)
        {

        }

        @Override
        public void put(Object key, Object value, long expiry, boolean override)
        {

        }

        @Override
        public void append(Object key, String value)
        {

        }

        @Override
        public String getName()
        {
            return null;
        }

        @Override
        public Object getNativeCache()
        {
            return null;
        }

        @Override
        public ValueWrapper get(Object key)
        {
            return null;
        }

        @Override
        public <V> V get(Object key, Class<V> valueType)
        {
            return null;
        }

        @Override
        public <T> T get(Object key, Callable<T> valueLoader)
        {
            return null;
        }

        @Override
        public boolean exists(Object key)
        {
            return false;
        }

        @Override
        public long ttl(Object key)
        {
            return 0;
        }

        @Override
        public boolean updateExpires(Object key, long expiry)
        {
            return false;
        }

        @Override
        public boolean rename(Object oldKey, Object newKey)
        {
            return false;
        }

        @Override
        public boolean rename(Object oldKey, Object newKey, boolean override)
        {
            return false;
        }

        @Override
        public void evict(Object key)
        {

        }

        @Override
        public void clear()
        {

        }

        @Override
        public void close()
        {

        }

        @Override
        public HSCounter getCounter()
        {
            return null;
        }

        @Override
        public HSCollectionCache getCollectionCache()
        {
            return null;
        }
    }
}
