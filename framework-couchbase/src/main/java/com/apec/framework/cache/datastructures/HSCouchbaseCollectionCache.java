package com.apec.framework.cache.datastructures;

import com.apec.framework.exception.CouchbaseException;
import com.apec.framework.cache.support.HSCouchbaseKeyBuilder;
import com.apec.framework.common.CouchbaseCharacterHandler;
import com.apec.framework.common.TypeConvert;
import com.apec.framework.common.constant.ErrorCodeConsts;
import com.couchbase.client.java.Bucket;
import com.couchbase.client.java.datastructures.collections.CouchbaseArrayList;
import com.couchbase.client.java.datastructures.collections.CouchbaseArraySet;
import com.couchbase.client.java.datastructures.collections.CouchbaseMap;
import com.couchbase.client.java.document.JsonArrayDocument;
import com.couchbase.client.java.document.JsonDocument;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

public class HSCouchbaseCollectionCache implements HSCollectionCache {
    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    private final Bucket client;

    private final HSCouchbaseKeyBuilder keyBuilder;


    public HSCouchbaseCollectionCache(Bucket client, HSCouchbaseKeyBuilder keyBuilder) {
        this.client = client;
        this.keyBuilder = keyBuilder;
    }

    @SuppressWarnings("unchecked")
    @Override
    public <E> boolean listPush(String key, E element) {
        key = keyBuilder.buildKey(key);
        Bucket bucket = client;
        try {
            CouchbaseArrayList<Object> list = new CouchbaseArrayList<>(key, bucket);
            list.add(TypeConvert.convertObjToStr(element));
            return true;
        } catch (Exception e) {
            LOGGER.error("couchbase listPush: ", e);
            return false;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public <E> boolean listSet(String key, int index, E element) {
        key = keyBuilder.buildKey(key);
        Bucket bucket = client;
        try {
            if (!bucket.exists(key)) {
                throw new CouchbaseException(ErrorCodeConsts.CACHE_EXCEPTION, "couchbase key not exit");
            }
            CouchbaseArrayList<Object> list = new CouchbaseArrayList<>(key, bucket);
            list.set(index, TypeConvert.convertObjToStr(element));
            return true;
        } catch (Exception e) {
            LOGGER.error("couchbase listSet: ", e);
            return false;
        }
    }

    private JsonArrayDocument getJsonArrayDocument(String key, Bucket bucket) {
        JsonArrayDocument document = bucket.get(key, JsonArrayDocument.class);
        if (document == null) {
            throw new CouchbaseException(ErrorCodeConsts.CACHE_EXCEPTION, "document is null");
        }
        return document;
    }

    @Override
    public boolean listPop(String key) {
        return listRemove(key, 0);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean listRemove(String key, int index) {
        key = keyBuilder.buildKey(key);
        Bucket bucket = client;
        try {
            if (!bucket.exists(key)) {
                throw new CouchbaseException(ErrorCodeConsts.CACHE_EXCEPTION, "couchbase key not exit");
            }
            CouchbaseArrayList<Object> list = new CouchbaseArrayList<>(key, bucket);
            list.remove(index);
            return true;
        } catch (Exception e) {
            LOGGER.error("couchbase listRemove: ", e);
            return false;
        }
    }

    @Override
    public <E> E listGet(String key, int index, Class<E> elementType) {
        return listGet(key, index, elementType, false);
    }

    @SuppressWarnings("unchecked")
    @Override
    public <E> E listGet(String key, int index, Class<E> elementType, boolean localEnable) {
        key = keyBuilder.buildKey(key);
        Bucket bucket = client;
        try {
                JsonArrayDocument document = getJsonArrayDocument(key, bucket);
                return TypeConvert.convertStrToObj(document.content().get(index).toString(), elementType);
        } catch (Exception e) {
            LOGGER.error("couchbase listGet: ", e);
            return null;
        }
    }

    @Override
    public int listSize(String key) {
        return listSize(key, false);
    }

    @Override
    public int listSize(String key, boolean localEnable) {
        key = keyBuilder.buildKey(key);
        Bucket bucket = client;
        try {
                JsonArrayDocument document = getJsonArrayDocument(key, bucket);
                return document.content().size();
        } catch (Exception e) {
            LOGGER.error("couchbase listSize: ", e);
            return -1;
        }
    }

    @Override
    public <K, V> boolean mapSet(String key, Map<K, V> map) {
        key = keyBuilder.buildKey(key);
        Bucket bucket = client;
        try {
            Map<String, String> result = TypeConvert.convertObjToStr(map);
            if (result == null) {
                new CouchbaseMap<>(key, bucket);
            } else {
                new CouchbaseMap<>(key, bucket, result);
            }
            return true;
        } catch (Exception e) {
            LOGGER.error("couchbase mapSet: ", e);
            return false;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public <K, V> boolean mapAdd(String key, K field, V value) {
        key = keyBuilder.buildKey(key);
        Bucket bucket = client;
        try {
            CouchbaseMap<Object> map = new CouchbaseMap<>(key, bucket);
            map.put(CouchbaseCharacterHandler.quotesReplace(TypeConvert.convertObjToStr(field)),
                    TypeConvert.convertObjToStr(value));
            return true;
        } catch (Exception e) {
            LOGGER.error("couchbase mapAdd: ", e);
            return false;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public <K> long mapDel(String key, K... fields) {
        key = keyBuilder.buildKey(key);
        Bucket bucket = client;
        long delNumber = 0l;
        try {
            if (!bucket.exists(key)) {
                throw new CouchbaseException(ErrorCodeConsts.CACHE_EXCEPTION, "couchbase key not exit");
            }
            CouchbaseMap<Object> map = new CouchbaseMap<>(key, bucket);
            for (K field : fields) {
                if (map.remove(TypeConvert.convertObjToStr(field)) != null) {
                    delNumber++;
                }
            }
            return delNumber;
        } catch (Exception e) {
            LOGGER.error("couchbase mapDel: ", e);
            return -1l;
        }
    }

    private JsonDocument getJsonDocument(String key, Bucket bucket) {
        JsonDocument document = bucket.get(key);
        if (document == null) {
            throw new CouchbaseException(ErrorCodeConsts.CACHE_EXCEPTION, "couchbase document is null");
        }
        return document;
    }

    @Override
    public <K, V> V mapGet(String key, K field, Class<V> valueType) {
        return (V) mapGet(key, field, valueType, false);
    }

    @SuppressWarnings("unchecked")
    @Override
    public <K, V> V mapGet(String key, K field, Class<V> valueType, boolean localEnable) {
        key = keyBuilder.buildKey(key);
        Bucket bucket = client;
        try {
            if (localEnable) {
                JsonDocument document = getJsonDocument(key, bucket);
                Map<K, V> result = TypeConvert.convertStrToObj(document.content().toMap(), field, valueType);
                return result.get(field);
            } else {
                JsonDocument document = getJsonDocument(key, bucket);
                return TypeConvert.convertStrToObj(
                        document.content().toMap().get(TypeConvert.convertObjToStr(field)).toString(), valueType);
            }
        } catch (Exception e) {
            LOGGER.error("couchbase mapGet: ", e);
            return null;
        }
    }

    @Override
    public <K, V> Map<K, V> mapGet(String key, Class<K> fieldType, Class<V> valueType) {
        return (Map<K, V>) mapGet(key, fieldType, valueType, false);
    }

    @SuppressWarnings("unchecked")
    @Override
    public <K, V> Map<K, V> mapGet(String key, Class<K> fieldType, Class<V> valueType, boolean localEnable) {
        key = keyBuilder.buildKey(key);
        Bucket bucket = client;
        try {
                JsonDocument document = getJsonDocument(key, bucket);
                return TypeConvert.convertStrToObj(document.content().toMap(), fieldType, valueType);
        } catch (Exception e) {
            LOGGER.error("couchbase mapGet: ", e);
            return null;
        }
    }

    @Override
    public int mapSize(String key) {
        return mapSize(key, false);
    }

    @SuppressWarnings("unchecked")
    @Override
    public int mapSize(String key, boolean localEnable) {
        key = keyBuilder.buildKey(key);
        Bucket bucket = client;
        try {
                JsonDocument document = getJsonDocument(key, bucket);
                return document.content().toMap().size();
        } catch (Exception e) {
            LOGGER.error("couchbase mapSize: ", e);
            return -1;
        }
    }

    @Override
    public <K> boolean mapContainsField(String key, K field) {
        return mapContainsField(key, field, false);
    }

    @SuppressWarnings("unchecked")
    @Override
    public <K> boolean mapContainsField(String key, K field, boolean localEnable) {
        key = keyBuilder.buildKey(key);
        Bucket bucket = client;
        try {
                JsonDocument document = getJsonDocument(key, bucket);
                return document.content().toMap().containsKey(TypeConvert.convertObjToStr(field));
        } catch (Exception e) {
            LOGGER.error("couchbase mapExists: ", e);
            return false;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public <E> boolean setAdd(String key, E element) {
        key = keyBuilder.buildKey(key);
        Bucket bucket = client;
        try {
            CouchbaseArraySet<Object> set = new CouchbaseArraySet<>(key, bucket);
            set.add(TypeConvert.convertObjToStr(element));
            return true;
        } catch (Exception e) {
            LOGGER.error("couchbase setAdd: ", e);
            return false;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public <E> boolean setRemove(String key, E element) {
        key = keyBuilder.buildKey(key);
        Bucket bucket = client;
        try {
            if (!bucket.exists(key)) {
                throw new CouchbaseException(ErrorCodeConsts.CACHE_EXCEPTION, "couchbase key not exit");
            }
            CouchbaseArraySet<Object> set = new CouchbaseArraySet<>(key, bucket);
            if (!set.remove(element)) {
                return false;
            }
            return true;
        } catch (Exception e) {
            LOGGER.error("couchbase setRemove: ", e);
            return false;
        }
    }

    @Override
    public <E> boolean setContains(String key, E element) {
        return setContains(key, element, false);
    }

    @SuppressWarnings("unchecked")
    @Override
    public <E> boolean setContains(String key, E element, boolean localEnable) {
        key = keyBuilder.buildKey(key);
        Bucket bucket = client;
        try {
                JsonArrayDocument document = getJsonArrayDocument(key, bucket);
                return document.content().toList().contains(TypeConvert.convertObjToStr(element));
        } catch (Exception e) {
            LOGGER.error("couchbase setContains: ", e);
            return false;
        }
    }

    @Override
    public int setSize(String key) {
        return setSize(key, false);
    }

    @Override
    public int setSize(String key, boolean localEnable) {
        key = keyBuilder.buildKey(key);
        Bucket bucket = client;
        try {
                JsonArrayDocument document = getJsonArrayDocument(key, bucket);
                return document.content().size();
        } catch (Exception e) {
            LOGGER.error("couchbase setSize: ", e);
            return -1;
        }
    }

    @Override
    public <E> Set<E> setGet(String key, Class<E> elementType) {
        return setGet(key, elementType, false);
    }

    @SuppressWarnings("unchecked")
    @Override
    public <E> Set<E> setGet(String key, Class<E> elementType, boolean localEnable) {
        key = keyBuilder.buildKey(key);
        Bucket bucket = client;
        try {
                JsonArrayDocument document = getJsonArrayDocument(key, bucket);
                Set<E> result = new HashSet<>();
                for (Object element : document.content().toList()) {
                    result.add((E) TypeConvert.convertStrToObj(element.toString(), elementType));
                }
                return result;
        } catch (Exception e) {
            LOGGER.error("couchbase setGet: ", e);
            return null;
        }
    }

    @Override
    public <E> boolean listContains(String key, E element) {
        return listContains(key, element, false);
    }

    @SuppressWarnings("unchecked")
    @Override
    public <E> boolean listContains(String key, E element, boolean localEnable) {

        key = keyBuilder.buildKey(key);
        Bucket bucket = client;
        try {
                JsonArrayDocument document = getJsonArrayDocument(key, bucket);
                return document.content().toList().contains(TypeConvert.convertObjToStr(element));
        } catch (Exception e) {
            LOGGER.error("couchbase listContains: ", e);
            return false;
        }

    }

    @Override
    public <V> boolean mapContainsValue(String key, V value) {
        return mapContainsValue(key, value, false);
    }

    @SuppressWarnings("unchecked")
    @Override
    public <V> boolean mapContainsValue(String key, V value, boolean localEnable) {
        key = keyBuilder.buildKey(key);
        Bucket bucket = client;
        try {
                JsonDocument document = getJsonDocument(key, bucket);
                return document.content().toMap().containsValue(TypeConvert.convertObjToStr(value));
        } catch (Exception e) {
            LOGGER.error("couchbase mapContainsValue: ", e);
            return false;
        }
    }

    @Override
    public <E> int listIndexOf(String key, E element) {
        return listIndexOf(key, element, false);
    }

    @SuppressWarnings("unchecked")
    @Override
    public <E> int listIndexOf(String key, E element, boolean localEnable) {
        key = keyBuilder.buildKey(key);
        Bucket bucket;
        try {
                bucket = client;
                JsonArrayDocument document = getJsonArrayDocument(key, bucket);
                return document.content().toList().indexOf(TypeConvert.convertObjToStr(element));
        } catch (Exception e) {
            LOGGER.error("couchbase listIndexOf: ", e);
            return -1;
        }
    }

}
