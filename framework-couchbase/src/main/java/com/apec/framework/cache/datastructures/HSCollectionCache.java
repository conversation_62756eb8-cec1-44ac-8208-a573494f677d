package com.apec.framework.cache.datastructures;


import javax.management.openmbean.InvalidKeyException;
import java.util.Map;
import java.util.Set;

public interface HSCollectionCache {

    /**
     * 元素插入list尾部;
     * key不合规时抛出{@link InvalidKeyException};
     * 分布式缓存底层异常,日志记录异常信息并返回false
     *
     * @param key
     * @param element
     * @return 插入成功返回true,否则返回false
     */
    public <E> boolean listPush(String key, E element);

    /**
     * 在list指定下标处插入元素;
     * key不合规时抛出{@link InvalidKeyException};
     * key不存在时抛出{@link KeyDoesNotExistException};
     * index不合规时抛出{@link IndexOutOfBoundsException};
     * 分布式缓存底层异常,日志记录异常信息并返回false
     *
     * @param key
     * @param index 待插入下标
     * @param element 待插入元素
     * @return 插入成功返回true,否则返回false
     */
    public <E> boolean listSet(String key, int index, E element);

    /**
     * 移除list首部元素;
     * 指定元素不存在时返回false;
     * key不合规时抛出{@link InvalidKeyException};
     * key不存在时抛出{@link KeyDoesNotExistException};
     * index不合规时抛出{@link IndexOutOfBoundsException};
     * 分布式缓存底层异常,日志记录异常信息并返回null
     *
     * @param key
     * @return 设置成功返回true,否则返回false
     */
    public boolean listPop(String key);

    /**
     * 移除list指定下标处元素;
     * 指定元素不存在时返回false;
     * key不合规时抛出{@link InvalidKeyException};
     * key不存在时抛出{@link KeyDoesNotExistException};
     * index不合规时抛出{@link IndexOutOfBoundsException};
     * 分布式缓存底层异常,日志记录异常信息并返回null
     *
     * @param key
     * @param index
     * @return 移除成功返回true,否则返回false
     */
    public boolean listRemove(String key, int index);

    /**
     * 获取list指定下标处的元素,并以指定类型返回,下标越界返回null;
     * key不合规时抛出{@link InvalidKeyException};
     * key不存在时抛出{@link KeyDoesNotExistException};
     * index不合规时抛出{@link IndexOutOfBoundsException};
     * 分布式缓存底层异常,日志记录异常信息并返回null
     *
     * @param key
     * @param index
     * @param elementType
     * @return 指定下标处的元素
     */
    public <E> E listGet(String key, int index, Class<E> elementType);

    /**
     * 获取list指定下标处的元素,并以指定类型返回,下标越界返回null;
     * localCache为true时,从本地缓存中获取,若本地缓存不存在指定key,从分布式缓存加载至本地;
     * localCache为false时,直接从分布式缓存获取;
     * key不合规时抛出{@link InvalidKeyException};
     * key不存在时抛出{@link KeyDoesNotExistException};
     * index不合规时抛出{@link IndexOutOfBoundsException};
     * 分布式缓存底层异常,日志记录异常信息并返回null
     *
     * @param key
     * @param index 元素下标
     * @param elementType 元素类型
     * @param localEnable 是否启用本地缓存
     * @return 指定下标处的元素
     */
    public <E> E listGet(String key, int index, Class<E> elementType, boolean localEnable);

    /**
     * 返回指定元素在list中的下标,元素不存在返回-1;
     * key不合规时抛出{@link InvalidKeyException};
     * key不存在时抛出{@link KeyDoesNotExistException};
     * 分布式缓存底层异常,日志记录异常信息并返回-1
     *
     * @param key
     * @param element
     * @return 元素下标
     */
    public <E> int listIndexOf(String key, E element);

    /**
     * 返回指定元素在list中的下标,元素不存在返回-1;
     * localCache为true时,从本地缓存中获取,若本地缓存不存在指定key,从分布式缓存加载至本地;
     * localCache为false时,直接从分布式缓存获取;
     * key不合规时抛出{@link InvalidKeyException};
     * key不存在时抛出{@link KeyDoesNotExistException};
     * 分布式缓存底层异常,日志记录异常信息并返回-1
     *
     * @param key
     * @param element
     * @return 元素下标
     */
    public <E> int listIndexOf(String key, E element, boolean localEnable);

    /**
     * 返回list内的元素个数;
     * key不合规时抛出{@link InvalidKeyException};
     * key不存在时抛出{@link KeyDoesNotExistException};
     * 分布式缓存底层异常,日志记录异常信息并返回-1
     *
     * @param key
     * @return 元素数
     */
    public int listSize(String key);

    /**
     * 返回list内的元素个数;
     * localCache为true时,从本地缓存中获取,若本地缓存不存在指定key,从分布式缓存加载至本地;
     * localCache为false时,直接从分布式缓存获取;
     * key不合规时抛出{@link InvalidKeyException};
     * key不存在时抛出{@link KeyDoesNotExistException};
     * 分布式缓存底层异常,日志记录异常信息并返回-1
     *
     * @param key
     * @param localEnable 是否启用本地缓存
     * @return 元素数
     */
    public int listSize(String key, boolean localEnable);

    /**
     * 判断list中是否包含指定元素;
     * key不合规时抛出{@link InvalidKeyException};
     * key不存在时抛出{@link KeyDoesNotExistException};
     * 分布式缓存底层异常,日志记录异常信息并返回false
     *
     * @param key
     * @param element
     * @return
     */
    public <E> boolean listContains(String key, E element);

    /**
     * 判断list中是否包含指定元素;
     * localCache为true时,从本地缓存中获取,若本地缓存不存在指定key,从分布式缓存加载至本地;
     * localCache为false时,直接从分布式缓存获取;
     * key不合规时抛出{@link InvalidKeyException};
     * key不存在时抛出{@link KeyDoesNotExistException};
     * 分布式缓存底层异常,日志记录异常信息并返回false
     *
     * @param key
     * @param element
     * @param localEnable
     * @return
     */
    public <E> boolean listContains(String key, E element, boolean localEnable);

    /**
     * 存储map;
     * key不合规时抛出{@link InvalidKeyException};
     * 分布式缓存底层异常,日志记录异常信息并返回false
     *
     * @param key
     * @param map
     * @return 设置成功返回true,否则返回false
     */
    public <K, V> boolean mapSet(String key, Map<K, V> map);

    /**
     * map已存在时,为它添加字段和值;map不存在时,新建新的map
     * key不合规时抛出{@link InvalidKeyException};
     * 分布式缓存底层异常,日志记录异常信息并返回false
     *
     * @param key
     * @param field
     * @param value
     * @return 设置成功返回true,否则返回false
     */
    public <K, V> boolean mapAdd(String key, K field, V value);

    /**
     * 移除map中的指定字段
     * key不合规时抛出{@link InvalidKeyException};
     * key不存在时抛出{@link KeyDoesNotExistException};
     * 分布式缓存底层异常,日志记录异常信息并返回-1
     *
     * @param key
     * @param fields 待删除字段
     * @return 返回成功删除的字段数
     */
    @SuppressWarnings("unchecked")
    public <K> long mapDel(String key, K... fields);

    /**
     * 获取map中指定字段的值,并以指定类型返回
     * key不合规时抛出{@link InvalidKeyException};
     * key不存在时抛出{@link KeyDoesNotExistException};
     * 分布式缓存底层异常,日志记录异常信息并返回null
     *
     * @param key
     * @param field
     * @param valueType value类型
     * @return 指定的value
     */
    public <K, V> V mapGet(String key, K field, Class<V> valueType);

    /**
     * 获取map中指定字段的值,并以指定类型返回
     * localCache为true时,从本地缓存中获取,若本地缓存不存在指定key,从分布式缓存加载至本地;
     * localCache为false时,直接从分布式缓存获取;
     * key不合规时抛出{@link InvalidKeyException};
     * key不存在时抛出{@link KeyDoesNotExistException};
     * 分布式缓存底层异常,日志记录异常信息并返回null
     *
     * @param key
     * @param field
     * @param valueType value类型
     * @param 是否启用本地缓存
     * @return 指定的value
     */
    public <K, V> V mapGet(String key, K field, Class<V> valueType, boolean localEnable);

    /**
     * 获取指定map,并以指定格式返回
     * key不合规时抛出{@link InvalidKeyException};
     * key不存在时抛出{@link KeyDoesNotExistException};
     * 分布式缓存底层异常,日志记录异常信息并返回null
     *
     * @param key
     * @param valueType value类型
     * @return 指定的map
     */
    public <K, V> Map<K, V> mapGet(String key, Class<K> fieldType, Class<V> valueType);

    /**
     * 获取指定map,并以指定格式返回
     * localCache为true时,从本地缓存中获取,若本地缓存不存在指定key,从分布式缓存加载至本地;
     * localCache为false时,直接从分布式缓存获取;
     * key不合规时抛出{@link InvalidKeyException};
     * key不存在时抛出{@link KeyDoesNotExistException};
     * 分布式缓存底层异常,日志记录异常信息并返回null
     *
     * @param key
     * @param valueType value类型
     * @param localEnable 是否启用本地缓存
     * @return 指定的map
     */
    public <K, V> Map<K, V> mapGet(String key, Class<K> fieldType, Class<V> valueType, boolean localEnable);

    /**
     * 获取map中的字段数;
     * key不合规时抛出{@link InvalidKeyException};
     * key不存在时抛出{@link KeyDoesNotExistException};
     * 分布式缓存底层异常,日志记录异常信息并返回-1
     *
     * @param key
     * @return map的大小
     */
    public int mapSize(String key);

    /**
     * 获取map中的字段数;
     * localCache为true时,从本地缓存中获取,若本地缓存不存在指定key,从分布式缓存获取;
     * localCache为false时,直接从分布式缓存获取;
     * key不合规时抛出{@link InvalidKeyException};
     * key不存在时抛出{@link KeyDoesNotExistException};
     * 分布式缓存底层异常,日志记录异常信息并返回-1
     *
     * @param key
     * @param localEnable 是否启用本地缓存
     * @return map的大小
     */
    public int mapSize(String key, boolean localEnable);

    /**
     * 判断map中是否存在指定字段
     * key不合规时抛出{@link InvalidKeyException};
     * key不存在时抛出{@link KeyDoesNotExistException};
     * 分布式缓存底层异常,日志记录异常信息并返回false
     *
     * @param key
     * @param field
     * @return 是否存在指定字段
     */
    public <K> boolean mapContainsField(String key, K field);

    /**
     * 判断map中是否存在指定字段
     * localCache为true时,从本地缓存中获取,若本地缓存不存在指定key,从分布式缓存获取;
     * localCache为false时,直接从分布式缓存获取;
     * key不合规时抛出{@link InvalidKeyException};
     * key不存在时抛出{@link KeyDoesNotExistException};
     * 分布式缓存底层异常,日志记录异常信息并返回false
     *
     * @param key
     * @param field
     * @return 是否存在指定字段
     */
    public <K> boolean mapContainsField(String key, K field, boolean localEnable);

    /**
     * 判断map中是否存在指定的值
     * key不合规时抛出{@link InvalidKeyException};
     * key不存在时抛出{@link KeyDoesNotExistException};
     * 分布式缓存底层异常,日志记录异常信息并返回false
     *
     * @param key
     * @param value
     * @return
     */
    public <V> boolean mapContainsValue(String key, V value);

    /**
     * 判断map中是否存在指定的值
     * localCache为true时,从本地缓存中获取,若本地缓存不存在指定key,从分布式缓存获取;
     * localCache为false时,直接从分布式缓存获取;
     * key不合规时抛出{@link InvalidKeyException};
     * key不存在时抛出{@link KeyDoesNotExistException};
     * 分布式缓存底层异常,日志记录异常信息并返回false
     *
     * @param key
     * @param value
     * @param localEnable
     * @return
     */
    public <V> boolean mapContainsValue(String key, V value, boolean localEnable);

    /**
     * 向set中添加元素element
     * key不合规时抛出{@link InvalidKeyException};
     * 分布式缓存底层异常,日志记录异常信息并返回false
     *
     * @param key
     * @param element
     * @return 操作成功返回true,否则返回false
     */
    public <E> boolean setAdd(String key, E element);

    /**
     * 移除set中的指定元素;
     * 指定元素不存在时返回false;
     * key不合规时抛出{@link InvalidKeyException};
     * key不存在时抛出{@link KeyDoesNotExistException};
     * 分布式缓存底层异常,日志记录异常信息并返回false
     *
     * @param key
     * @param element
     * @return 移除成功返回true,否则返回false
     */
    public <E> boolean setRemove(String key, E element);

    /**
     * 判断set中是否包含指定元素
     * key不合规时抛出{@link InvalidKeyException};
     * key不存在时抛出{@link KeyDoesNotExistException};
     * 分布式缓存底层异常,日志记录异常信息并返回false
     *
     * @param key
     * @param element
     * @return 操作成功返回true,否则返回false
     */
    public <E> boolean setContains(String key, E element);

    /**
     * 判断set中是否包含指定元素;
     * localCache为true时,从本地缓存中获取,若本地缓存不存在指定key,从分布式缓存加载至本地;
     * localCache为false时,直接从分布式缓存获取;
     * key不合规时抛出{@link InvalidKeyException};
     * key不存在时抛出{@link KeyDoesNotExistException};
     * 分布式缓存底层异常,日志记录异常信息并返回false
     *
     * @param key
     * @param element
     * @param localEnable 是否启用本地缓存
     * @return
     */
    public <E> boolean setContains(String key, E element, boolean localEnable);

    /**
     * 返回set中的元素个数;
     * key不合规时抛出{@link InvalidKeyException};
     * key不存在时抛出{@link KeyDoesNotExistException};
     * 分布式缓存底层异常,日志记录异常信息并返回-1
     *
     * @param key
     * @return 元素个数
     */
    public int setSize(String key);

    /**
     * 返回set中的元素个数;
     * localCache为true时,从本地缓存中获取,若本地缓存不存在指定key,从分布式缓存获取;
     * localCache为false时,直接从分布式缓存获取;
     * key不合规时抛出{@link InvalidKeyException};
     * key不存在时抛出{@link KeyDoesNotExistException};
     * 分布式缓存底层异常,日志记录异常信息并返回-1
     *
     * @param key
     * @param localEnable 是否启用本地缓存
     * @return 元素个数
     */
    public int setSize(String key, boolean localEnable);

    /**
     * 以指定类型返回set;
     * key不合规时抛出{@link InvalidKeyException};
     * key不存在时抛出{@link KeyDoesNotExistException};
     * 分布式缓存底层异常,日志记录异常信息并返回null
     *
     * @param key
     * @return set
     */
    public <E> Set<E> setGet(String key, Class<E> elementType);

    /**
     * 以指定类型返回set;
     * localCache为true时,从本地缓存中获取,若本地缓存不存在指定key,从分布式缓存加载至本地;
     * localCache为false时,直接从分布式缓存获取;
     * key不合规时抛出{@link InvalidKeyException};
     * key不存在时抛出{@link KeyDoesNotExistException};
     * 分布式缓存底层异常,日志记录异常信息并返回null
     *
     * @param key
     * @param localEnable 是否启用本地缓存
     * @return set
     */
    public <E> Set<E> setGet(String key, Class<E> elementType, boolean localEnable);
}
