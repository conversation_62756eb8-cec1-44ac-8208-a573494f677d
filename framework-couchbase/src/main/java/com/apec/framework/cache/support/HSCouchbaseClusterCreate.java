package com.apec.framework.cache.support;

import com.couchbase.client.java.CouchbaseCluster;
import com.couchbase.client.java.env.CouchbaseEnvironment;
import com.couchbase.client.java.env.DefaultCouchbaseEnvironment;
import org.springframework.util.StringUtils;

import java.util.Properties;

/**
 * <AUTHOR>
 *
 */
public class HSCouchbaseClusterCreate {

    /**
     * 根据字符串创建couchbase集群
     *
     * @param nodesList
     * @return
     */
    public static CouchbaseCluster create(String nodesList) {

        return create(nodesList, new Properties());
    }

    public static CouchbaseCluster create(String nodesList, Properties properties) {
        String[] nodes = StringUtils.split(nodesList, ",; \t\n");

        int cpus = Runtime.getRuntime().availableProcessors();
        int nodeSize = nodes.length;
        CouchbaseEnvironment env = DefaultCouchbaseEnvironment.builder()
                .connectTimeout(longPropertyOr(properties, "connectTimeout", 8 * 1000)) // 默认连接超时时间8s
                .ioPoolSize(intPropertyOr(properties, "ioPoolSize", Math.min(Math.max(cpus, nodeSize), nodeSize * 3)))// 默认io线程数
                .computationPoolSize(intPropertyOr(properties, "computationPoolSize", Math.min(cpus, 8))) // 默认运算线程数
                .build();

        return CouchbaseCluster.create(env, nodes);
    }

    protected static long longPropertyOr(Properties properties, String key, long def) {
        String property = properties.getProperty(key);
        if (property == null) {
            return def;
        }
        return Integer.parseInt(property);
    }

    protected static int intPropertyOr(Properties properties, String key, int def) {
        String property = properties.getProperty(key);
        if (property == null) {
            return def;
        }
        return Integer.parseInt(property);
    }

}
