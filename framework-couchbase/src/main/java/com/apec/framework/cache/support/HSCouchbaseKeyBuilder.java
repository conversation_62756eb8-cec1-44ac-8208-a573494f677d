package com.apec.framework.cache.support;

import com.apec.framework.common.TypeConvert;
import com.apec.framework.common.ValueFilter;

/**
 * Couchbase Key 创建
 */
public class HSCouchbaseKeyBuilder {

    /**
     * 缓存命名空间
     */
    private final String NAMESPACE;

    /**
     * 缓存名
     */
    private final String NAME;

    /**
     * 分隔符
     */
    private final String DELIMITER;

    public HSCouchbaseKeyBuilder(String namespace, String name, String delimiter) {
        NAMESPACE = namespace;
        NAME = name;
        DELIMITER = delimiter;
    }

    /**
     * @return the nAMESPACE
     */
    public String getNAMESPACE() {
        return NAMESPACE;
    }

    /**
     * @return the nAME
     */
    public String getNAME() {
        return NAME;
    }

    /**
     * @return the dELIMITER
     */
    public String getDELIMITER() {
        return DELIMITER;
    }

    /**
     * @param key
     * @return
     */
    public String buildKey(Object key) {
        String keyStr = TypeConvert.convertObjToStr(key);
        String documentId;
        if((NAMESPACE==null || NAMESPACE.trim().length()==0)&&(NAME == null || NAME.trim().length() == 0)){
            documentId=keyStr;
        }else if (NAME == null || NAME.trim().length() == 0) {
            documentId = NAMESPACE + DELIMITER + DELIMITER + keyStr;
        } else {
            documentId = NAMESPACE + DELIMITER + NAME + DELIMITER + keyStr;
        }
        ValueFilter.checkKey(documentId);
        return documentId;
    }
}
