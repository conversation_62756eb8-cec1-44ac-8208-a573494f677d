package com.apec.framework.cache.support;

import com.couchbase.client.java.Bucket;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Method;
import java.util.Map;

/**
 * 查找已经定义的Bucket
 *
 * 用于在应用第三方jar时，第三方事先定义好Bucket的情况，防止创建多个CouchbaseEnv实例
 *
 * <AUTHOR>
 *
 */
public class HSCouchbaseBucketLocator implements ApplicationContextAware
{

    private ApplicationContext applicationContext;

    /**
     * 根据bean类型及方法名，获取Bucket
     *
     * @param beanType
     * @param methodName
     * @return
     */
    public Bucket useBucket(Class<?> beanType, String methodName) {

        Map<String, ?> beans = applicationContext.getBeansOfType(beanType);
        if (beans == null || beans.isEmpty()) {
            return null;
        }
        Method targetMethod = ReflectionUtils.findMethod(beanType, methodName);
        if (targetMethod == null) {
            return null;
        }
        for (Object bean : beans.values()) {
            Object invokeResult = ReflectionUtils.invokeMethod(targetMethod, bean);
            if (invokeResult instanceof Bucket) {
                return (Bucket) invokeResult;
            }
        }
        return null;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException
    {
        this.applicationContext = applicationContext;
    }

}
