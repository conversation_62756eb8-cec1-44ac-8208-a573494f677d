package com.apec.framework.cache.counter;

import javax.management.openmbean.InvalidKeyException;

public interface HSCounter {

    /**
     * 若计数器存在:将指定计数器的值加1后返回;若计数器不存在:创建初始值为1,有效期为永久的计数器,将计数器的初始值返回;
     * key不合规时抛出{@link InvalidKeyException};
     * 分布式缓存底层异常,日志记录异常信息并返回-1
     *
     * @param key
     * @return 计数器的值
     */
    public long counter(String key);

    /**
     * 若计数器存在:将计数器的值加dela后返回;若计数器不存在:创建初始值为1,有效期为永久的计数器,将计数器的初始值返回;
     * key不合规时抛出{@link InvalidKeyException};
     * 分布式缓存底层异常,日志记录异常信息并返回-1
     *
     * @param key
     * @param dela 步长,递增为正,递减为负
     * @return 计数器的值
     */
    public long counter(String key, long dela);

    /**
     * 若计数器存在:将计数器的值加dala后返回;若计数器不存在:创建初始值为initial,有效期为永久的计数器,将计数器的初始值返回;
     * key不合规时抛出{@link InvalidKeyException};
     * 分布式缓存底层异常,日志记录异常信息并返回-1
     *
     * @param key
     * @param dela 步长,递增为正,递减为负
     * @param initial 计数器初始值
     * @return 计数器的值
     */
    public long counter(String key, long dela, long initial);

    /**
     * 若计数器存在:将计数器的值加dala后返回;若计数器不存在:创建初始值为initial,有效期为expiry秒的计数器,将计数器的初始值返回;
     * key不合规时抛出{@link InvalidKeyException};
     * 分布式缓存底层异常,日志记录异常信息并返回-1
     *
     * @param key
     * @param dela 步长,递增为正,递减为负
     * @param initial 计数器初始值
     * @param expiry 计数器有效期
     * @return
     */
    public long counter(String key, long dela, long initial, long expiry);
}
