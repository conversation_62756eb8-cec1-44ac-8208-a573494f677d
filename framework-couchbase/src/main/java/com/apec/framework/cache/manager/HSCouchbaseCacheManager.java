/*
 * Copyright (C) 2015 Couchbase Inc., the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.apec.framework.cache.manager;

import com.apec.framework.cache.service.CouchbaseService;
import com.apec.framework.cache.service.HsCouchbaseServiceImpl;
import com.apec.framework.cache.support.HSCouchbaseCacheBuilder;
import com.couchbase.client.java.Bucket;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.support.AbstractCacheManager;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;

/**
 * The {@link HSCouchbaseCacheManager} orchestrates {@link HsCouchbaseServiceImpl}
 * instances.
 *
 * Since more than one current {@link Bucket} connection can be used for
 * caching, the
 * {@link HSCouchbaseCacheManager} orchestrates and handles them for the Spring
 * {@link Cache} abstraction layer.
 *
 */
public class HSCouchbaseCacheManager extends AbstractCacheManager implements HSCacheManager {

    private HSCouchbaseCacheBuilder hSCouchbaseCacheBuilder;

    private final Map<String, HSCouchbaseCacheBuilder> initialCaches;

    /**
     * Construct a {@link CacheManager} with a "template" {@link HSCouchbaseCacheBuilder}
     * (at least specifying a backing
     * {@link Bucket}).
     *
     * If a list of predetermined cache names is provided, the manager is
     * "static" and these caches will all be
     * prepared using the provided template builder.
     *
     * If no list is provided, the manager will be "dynamic" and additional
     * caches can be added later on just by name,
     * as they'll use the template builder for their configuration.
     *
     * Note that builders are used lazily and should not be mutated after having
     * been passed to this constructor.
     *
     * @param cacheBuilder the template (backing client, optional ttl) to use
     * either for static construction of specified
     * caches or later dynamic construction.
     * @param cacheNames the names of caches recognized by this manager
     * initially. If empty, caches can be added
     * dynamically later. Null names will be ignored.
     * @see HSCouchbaseCacheManager#setDefaultCacheBuilder(HSCouchbaseCacheBuilder) to force
     * activation of dynamic creation later on.
     */
    public HSCouchbaseCacheManager(HSCouchbaseCacheBuilder cacheBuilder, String... cacheNames) {
        if (cacheBuilder == null) {
            throw new NullPointerException("HSCacheBuilder template is mandatory");
        }
        Set<String> names = cacheNames.length == 0 ? Collections.<String> emptySet()
                : new LinkedHashSet<String>(Arrays.asList(cacheNames));
        this.initialCaches = new HashMap<String, HSCouchbaseCacheBuilder>(names.size());
        for (String name : names) {
            if (name != null) {
                this.initialCaches.put(name, cacheBuilder);
            }
        }
        if (this.initialCaches.isEmpty()) {
            this.hSCouchbaseCacheBuilder = cacheBuilder;
        }
    }

    /**
     * Construct a {@link CacheManager} knowing about a predetermined set of
     * caches at construction. The caches are
     * all explicitly described (using a {@link HSCouchbaseCacheBuilder} and the manager
     * cannot create caches dynamically until
     * {@link #setDefaultCacheBuilder(HSCouchbaseCacheBuilder)} is called.
     *
     * Note that builders are used lazily and should not be mutated after having
     * been passed to this constructor.
     *
     * @param initialCaches the caches to make available on startup
     */
    public HSCouchbaseCacheManager(Map<String, HSCouchbaseCacheBuilder> initialCaches) {
        if (initialCaches == null || initialCaches.isEmpty()) {
            throw new IllegalArgumentException("At least one cache builder must be specified.");
        }
        this.initialCaches = new HashMap<String, HSCouchbaseCacheBuilder>(initialCaches);
    }

    /**
     * Set the default cache builder to use to create caches on the fly. Set it
     * to {@code null}
     * to prevent cache to be created at runtime.
     *
     * @param defaultCacheBuilder the cache builder to use
     */
    public void setDefaultCacheBuilder(HSCouchbaseCacheBuilder defaultCacheBuilder) {
        this.hSCouchbaseCacheBuilder = defaultCacheBuilder;
    }

    @Override
    public Cache getCache(String name) {
        Cache cache = super.getCache(name);
        if (cache == null) {
            // addCache不是线程安全的操作，加锁保护下
            synchronized (this) {
                cache = super.getCache(name);
                if (cache == null) {
                    // 自动创建默认的缓存管理对象
                    cache = getMissingCache(name);
                    super.addCache(cache);
                }
            }
        }
        return cache;
    }

    //  @Override
    protected Cache getMissingCache(String name) {
        return (hSCouchbaseCacheBuilder != null ? hSCouchbaseCacheBuilder.build(name) : null);
    }

    @Override
    protected final Collection<? extends Cache> loadCaches() {
        List<Cache> caches = new LinkedList<Cache>();
        for (Map.Entry<String, HSCouchbaseCacheBuilder> entry : initialCaches.entrySet()) {
            caches.add(entry.getValue().build(entry.getKey()));
        }
        return caches;
    }

    @Override
    public void close() throws IOException {
        for(String name : getCacheNames()){
            getHSCache(name).close();
        }
    }

    @Override
    public CouchbaseService getHSCache() {
        return getHSCache("hit20");
    }

    @Override
    public CouchbaseService getHSCache(String name) {
        Cache cache = getCache(name);
        if(cache instanceof CouchbaseService){
            return (CouchbaseService)cache;
        }
        return new HSNULLCache();
    }

}
