package com.apec.framework.exception;

/**
 * Created by l<PERSON><PERSON> on 2018/4/27.
 */
public class CouchbaseException extends RuntimeException
{
    private String message;

    private String errorCode = null;

    public CouchbaseException(String errorCode, String message)
    {
        super(message);
        this.errorCode = errorCode;
        this.message = message;
    }

    public CouchbaseException(String errorCode, Throwable ex)
    {
        super(ex);
        this.errorCode = errorCode;
    }

    public CouchbaseException(String errorCode, String message, Throwable e)
    {
        super(message, e);
        this.errorCode = errorCode;
        this.message = message;
    }

    public String toString()
    {
        return super.toString();
    }
}
