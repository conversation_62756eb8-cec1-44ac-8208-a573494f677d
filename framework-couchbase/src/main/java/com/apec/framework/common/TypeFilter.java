package com.apec.framework.common;

import java.util.HashSet;
import java.util.Set;

public class TypeFilter {
    private static final Set<String> valueTypes = new HashSet<>();
    static {
        valueTypes.add(String.class.getName());
        valueTypes.add(Integer.class.getName());
        valueTypes.add(Long.class.getName());
        valueTypes.add(Double.class.getName());
        valueTypes.add(Boolean.class.getName());
        valueTypes.add(Float.class.getName());
        valueTypes.add(Byte.class.getName());
        valueTypes.add(Character.class.getName());

    }

    /**
     * 检查对象是否属于基本数据类型和String类型
     *
     * @param
     * @return boolean
     */
    public static boolean checkType(Object item) {
        return item == null || item instanceof String || item instanceof Integer || item instanceof Long
                || item instanceof Double || item instanceof Boolean || item instanceof Float;

    }

    /**
     * 检查该类型是否属于基本数据类型和String类型
     *
     * @param
     * @return boolean
     */
    public static boolean checkType(Class<?> type) {
        return valueTypes.contains(type.getName());
    }
}
