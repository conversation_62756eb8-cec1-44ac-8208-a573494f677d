package com.apec.framework.common;

import com.alibaba.fastjson.JSON;
import com.couchbase.client.java.document.json.JsonArray;
import com.couchbase.client.java.document.json.JsonObject;
import com.couchbase.client.java.document.json.JsonValue;
import org.springframework.stereotype.Component;

/**
 * Created by l<PERSON><PERSON> on 2018/4/27.
 */
public class HsBuilderKey
{
    /**
     * 缓存命名空间
     */
    private final String NAMESPACE;

    /**
     * 缓存名
     */
    private final String NAME;

    /**
     * 分隔符
     */
    private final String DELIMITER;

    public HsBuilderKey(String namespace, String name, String delimiter) {
        NAMESPACE = namespace;
        NAME = name;
        DELIMITER = delimiter;
    }

    /**
     * @return the nAMESPACE
     */
    public String getNAMESPACE() {
        return NAMESPACE;
    }

    /**
     * @return the nAME
     */
    public String getNAME() {
        return NAME;
    }

    /**
     * @return the dELIMITER
     */
    public String getDELIMITER() {
        return DELIMITER;
    }

    /**
     * @param key
     * @return
     */
    public String buildKey(Object key) {
        String keyStr = CouchbaseUtil.convertObjToStr(key);
        String documentId;
        if (NAME == null || NAME.trim().length() == 0) {
            documentId = NAMESPACE + DELIMITER + DELIMITER + keyStr;
        } else {
            documentId = NAMESPACE + DELIMITER + NAME + DELIMITER + keyStr;
        }
        return documentId;
    }

}
