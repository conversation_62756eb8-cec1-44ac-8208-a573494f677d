package com.apec.framework.common;

import com.alibaba.fastjson.JSON;
import com.couchbase.client.java.document.Document;
import com.couchbase.client.java.document.JsonDocument;
import com.couchbase.client.java.document.StringDocument;
import com.couchbase.client.java.document.json.JsonObject;
import com.couchbase.client.java.document.json.JsonValue;

import java.util.HashMap;
import java.util.Map;

public class TypeConvert {
    @SuppressWarnings("unchecked")
    public static <V> V convertStrToPrimitive(String value, Class<V> valueType) {
        if (valueType != null) {
            switch (valueType.getName()) {
                case "java.lang.String":
                    return (V) value;
                case "java.lang.Integer":
                    return (V) Integer.valueOf(value.toString());
                case "java.lang.Long":
                    return (V) Long.valueOf(value.toString());
                case "java.lang.Double":
                    return (V) Double.valueOf(value.toString());
                case "java.lang.Boolean":
                    return (V) Boolean.valueOf(value.toString());
                case "java.lang.Float":
                    return (V) Float.valueOf(value.toString());
                case "java.lang.Byte":
                    return (V) Byte.valueOf(value.toString());
                case "java.lang.Character":
                    return (V) Character.valueOf(value.toString().charAt(0));
            }
        }
        return null;
    }

    public static <V> V convertStrToObj(String element, Class<V> type) {
        if (!TypeFilter.checkType(type)) {
            return JSON.parseObject(element, type);
        }

        return (V) convertStrToPrimitive(element, type);
    }

    public static <V> V convertByteToObj(byte[] element, Class<V> type) {
        return convertStrToObj(new String(element), type);
    }

    public static <E> String convertObjToStr(E element) {
        if (TypeFilter.checkType(element)) {
            if (element == null) {
                return null;
            }
            return element.toString();
        } else
            return JSON.toJSONString(element);
    }

    public static <E> byte[] convertObjToByte(E element) {
        String result = convertObjToStr(element);
        if (result == null) {
            return null;
        }
        return result.getBytes();
    }

    public static <K, V> Map<K, V> convertByteToObj(Map<byte[], byte[]> map, Class<K> fieldtype, Class<V> valueType) {
        Map<K, V> result = new HashMap<>();
        for (byte[] bytes : map.keySet()) {
            result.put(TypeConvert.convertByteToObj(bytes, fieldtype),
                       TypeConvert.convertByteToObj(map.get(bytes), valueType));
        }
        return result;
    }

    @SuppressWarnings("unchecked")
    public static <K, V> Map<K, V> convertStrToObj(Map<String, Object> map, K field, Class<V> valueType) {
        return (Map<K, V>) convertStrToObj(map, field.getClass(), valueType);
    }

    public static <K, V> Map<K, V> convertStrToObj(Map<String, Object> map, Class<K> fieldType, Class<V> valueType) {
        Map<K, V> result = new HashMap<>();
        for (String str : map.keySet()) {
            result.put((K) TypeConvert.convertStrToObj(str, fieldType),
                       (V) TypeConvert.convertStrToObj(map.get(str).toString(), valueType));
        }
        return result;
    }

    public static <K, V> Map<String, String> convertObjToStr(Map<K, V> map) {
        if (map == null || map.size() == 0) {
            return null;
        }
        Map<String, String> result = new HashMap<>();

        for (Object field : map.keySet()) {
            result.put(TypeConvert.convertObjToStr(field), TypeConvert.convertObjToStr(map.get(field)));
        }
        return result;
    }

    public static <K, V> Map<byte[], byte[]> convertObjToByte(Map<K, V> map) {
        if (map == null || map.size() == 0) {
            return null;
        }
        Map<byte[], byte[]> result = new HashMap<>();
        for (Object field : map.keySet()) {
            result.put(TypeConvert.convertObjToByte(field), TypeConvert.convertObjToByte(map.get(field)));
        }
        return result;
    }

    @SuppressWarnings("rawtypes")
    public static <V> Document convertToDocument(String key, V value, Long expiry) {
        if (TypeFilter.checkType(value)) {
            return StringDocument.create(key, expiry.intValue(), value == null ? "" : value.toString());
        } else {
            try {
                return JsonDocument.create(key, expiry.intValue(), JsonObject.fromJson(JSON.toJSON(value).toString()));
            } catch (IllegalArgumentException e) {
                return StringDocument.create(key, expiry.intValue(), JSON.toJSON(value).toString());
            }
        }
    }

    @SuppressWarnings("rawtypes")
    public static <V> Document convertToDocument(String key, V value) {
        if (TypeFilter.checkType(value)) {
            return StringDocument.create(key, value == null ? "" : value.toString());
        } else {
            return JsonDocument.create(key, JsonObject.fromJson(JSON.toJSON(value).toString()));
        }
    }
}
