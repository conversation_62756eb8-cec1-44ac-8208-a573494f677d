package com.apec.framework.common;

public class CouchbaseExpiryHandler {

    /**
     * @param expiry
     * @return
     */
    public static long expiryTimeHandler(long expiry) {
        if (expiry > 1) {
            expiry = expiry - 1;
        }
        if (expiry > 2592000) {
            expiry = (System.currentTimeMillis() / 1000) + expiry;
        }
        // 0 为永久有效
        return expiry;
    }
}
