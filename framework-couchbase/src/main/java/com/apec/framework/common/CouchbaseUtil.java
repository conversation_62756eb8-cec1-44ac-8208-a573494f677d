package com.apec.framework.common;

import com.alibaba.fastjson.JSON;
import com.couchbase.client.java.document.json.JsonArray;
import com.couchbase.client.java.document.json.JsonObject;

/**
 * Created by l<PERSON><PERSON> on 2018/4/27.
 */
public class CouchbaseUtil
{

    public static <E> String convertObjToStr(E element) {
        if (checkType(element)) {
            if (element == null) {
                return null;
            }
            return element.toString();
        } else
            return JSON.toJSONString(element);
    }

    public static boolean checkType(Object item) {
        return item == null
               || item instanceof String
               || item instanceof Integer
               || item instanceof Long
               || item instanceof Double
               || item instanceof Boolean
               || item instanceof JsonObject
               || item instanceof JsonArray;
    }

    public static <V> V convertStrToObj(String element, Class<V> type) {
        if (checkType(type)) {
            return JSON.parseObject(element, type);
        }

        return (V) convertStrToPrimitive(element, type);
    }

    public static <V> V convertStrToPrimitive(String value, Class<V> valueType) {
        if (valueType != null) {
            switch (valueType.getName()) {
                case "java.lang.String":
                    return (V) value;
                case "java.lang.Integer":
                    return (V) Integer.valueOf(value.toString());
                case "java.lang.Long":
                    return (V) Long.valueOf(value.toString());
                case "java.lang.Double":
                    return (V) Double.valueOf(value.toString());
                case "java.lang.Boolean":
                    return (V) Boolean.valueOf(value.toString());
                case "java.lang.Float":
                    return (V) Float.valueOf(value.toString());
                case "java.lang.Byte":
                    return (V) Byte.valueOf(value.toString());
                case "java.lang.Character":
                    return (V) Character.valueOf(value.toString().charAt(0));
            }
        }
        return null;
    }
}
