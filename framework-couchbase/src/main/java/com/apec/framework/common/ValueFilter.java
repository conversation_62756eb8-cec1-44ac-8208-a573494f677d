package com.apec.framework.common;

import org.springframework.util.StringUtils;

import javax.management.openmbean.InvalidKeyException;

public class ValueFilter {
    public static void checkKey(String key) {
        if (StringUtils.isEmpty(key)) {
            throw new InvalidKeyException("key must not be null or empty");
        }
        if (key.toString().length() > 250) {
            throw new InvalidKeyException("key must not be larger than 250 bytes");
        }
    }
}
