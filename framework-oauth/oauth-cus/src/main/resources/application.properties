spring.datasource.url=**************************************************************
spring.datasource.username=root
spring.datasource.password=123456
spring.datasource.driver-class-name= com.mysql.jdbc.Driver
spring.datasource.max-idle=10
spring.datasource.max-wait=10000
spring.datasource.min-idle=5
spring.datasource.initial-size=5
#spring.redis.host=**************
#
#spring.datasource.max-idle=5
#spring.datasource.max-wait=10000
#spring.datasource.min-idle=2
#spring.datasource.initial-size=3
#spring.datasource.validation-query=SELECT 1
##spring.datasource.test-on-borrow=true
##spring.datasource.test-while-idle=true
#spring.datasource.time-between-eviction-runs-millis=18800
#spring.datasource.jdbc-interceptors=ConnectionState;SlowQueryReport(threshold=50)

imooc.security.oauth2.clients[0].clientId=client1
imooc.security.oauth2.clients[0].clientSecret=123456
imooc.security.oauth2.clients[0].tokenTime=3600

imooc.security.oauth2.storeType=redis

spring.redis.host=**************