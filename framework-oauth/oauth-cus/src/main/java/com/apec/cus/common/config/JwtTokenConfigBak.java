/*
package com.apec.cus.common.config;

import com.apec.cus.common.jwt.JwtTokenEnhancer;
import com.apec.cus.common.properties.SecurityProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;
import org.springframework.security.oauth2.provider.token.store.JwtTokenStore;

*/
/**
 * @Auther: liliwei
 * @Date: 2018/5/23 11:44
 * @Description:
 *//*

@Configuration
public class JwtTokenConfigBak
{
    @Autowired
    private SecurityProperties securityProperties;

    @Bean
    public JwtTokenStore jwtTokenStore(){
        return new JwtTokenStore(jwtAccessTokenConverter());
    }

    @Bean
    public JwtAccessTokenConverter jwtAccessTokenConverter(){
        JwtAccessTokenConverter jwtAccessTokenConverter=new JwtAccessTokenConverter();
        jwtAccessTokenConverter.setSigningKey(securityProperties.getOauth2Properties().getJwtSingerKey());
        return jwtAccessTokenConverter;
    }

    @Bean
    @ConditionalOnMissingBean(name = "jwtTokenEnhancer")
    public TokenEnhancer tokenEnhancer(){
        return new JwtTokenEnhancer();
    }
}
*/
