package com.apec.cus.service.impl;

import com.apec.cus.bean.User;
import com.apec.cus.service.ClientUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @Auther: liliwei
 * @Date: 2018/5/25 11:11
 * @Description:
 */
@Component
public class ClientUserDetailServiceImpl implements UserDetailsService
{

    @Autowired
    private ClientUserService clientUserService;

   /* @Autowired
    private RoleService roleService;
    @Autowired
    private PermissionService permissionService;*/

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        User userEntity = clientUserService.findByAppId(username);
        if (userEntity == null) {
            throw new UsernameNotFoundException("用户:" + username + ",不存在!");
        }
        Set<GrantedAuthority> grantedAuthorities = new HashSet<>();
        boolean enabled = true; // 可用性 :true:可用 false:不可用
        boolean accountNonExpired = true; // 过期性 :true:没过期 false:过期
        boolean credentialsNonExpired = true; // 有效性 :true:凭证有效 false:凭证无效
        boolean accountNonLocked = true; // 锁定性 :true:未锁定 false:已锁定
       /* List<RcRoleEntity> roleValues = roleService.getRoleValuesByUserId(userEntity.getId());
        for (RcRoleEntity role:roleValues){
            //角色必须是ROLE_开头，可以在数据库中设置
            GrantedAuthority grantedAuthority = new SimpleGrantedAuthority("ROLE_" + role.getValue());
            grantedAuthorities.add(grantedAuthority);
            //获取权限
            List<RcMenuEntity> permissionList = permissionService.getPermissionsByRoleId(role.getId());
            for (RcMenuEntity menu:permissionList
                ) {
                GrantedAuthority authority = new SimpleGrantedAuthority(menu.getCode());
                grantedAuthorities.add(authority);
            }
        }*/
        org.springframework.security.core.userdetails.User user = new org.springframework.security.core.userdetails.User(userEntity.getClientId(), userEntity.getScrecctId(),
                                                                                                                         enabled, accountNonExpired, credentialsNonExpired, accountNonLocked, grantedAuthorities);
        return user;
    }
}
