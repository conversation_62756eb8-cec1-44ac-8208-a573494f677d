/**
 * 
 */
package com.apec.cus.common.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

/**
 * <AUTHOR>
 *
 */
@ConfigurationProperties(prefix = "imooc.security")
public class SecurityProperties
{
	private Oauth2Properties oauth2=new Oauth2Properties();

	public Oauth2Properties getOauth2()
	{
		return oauth2;
	}

	public void setOauth2(Oauth2Properties oauth2)
	{
		this.oauth2 = oauth2;
	}
}

