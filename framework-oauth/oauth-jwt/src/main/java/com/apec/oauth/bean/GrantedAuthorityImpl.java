package com.apec.oauth.bean;

import org.springframework.security.core.GrantedAuthority;

/**
 * @Auther: liliwei
 * @Date: 2018/5/22 10:13
 * @Description:
 */
public class GrantedAuthorityImpl implements GrantedAuthority
{
    private String authority;

    public GrantedAuthorityImpl(String authority) {
        this.authority = authority;
    }

    public void setAuthority(String authority) {
        this.authority = authority;
    }

    @Override
    public String getAuthority() {
        return this.authority;
    }
}
