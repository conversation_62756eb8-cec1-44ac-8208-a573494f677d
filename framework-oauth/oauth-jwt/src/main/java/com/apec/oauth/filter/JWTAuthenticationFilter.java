package com.apec.oauth.filter;

import com.apec.oauth.service.TokenAuthenticationService;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.filter.GenericFilterBean;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * @Auther: liliwei
 * @Date: 2018/5/22 10:35
 * @Description:
 */
public class JWTAuthenticationFilter extends GenericFilterBean
{
    @Override
    public void doFilter(ServletRequest request,
        ServletResponse response,
        FilterChain filterChain)
        throws IOException, ServletException {
        Authentication authentication = TokenAuthenticationService
            .getAuthentication((HttpServletRequest)request);

        SecurityContextHolder.getContext()
            .setAuthentication(authentication);
        filterChain.doFilter(request,response);
    }
}
