package com.apec.client.common.filter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpMethod;
import org.springframework.web.HttpRequestMethodNotSupportedException;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @Auther: liliwei
 * @Date: 2018/5/24 15:54
 * @Description:
 */
public class OauthFilter implements Filter
{

    private static final Logger log = LoggerFactory.getLogger(OauthFilter.class);

    @Override
    public void init(FilterConfig filterConfig) throws ServletException
    {

    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
        throws IOException, ServletException
    {
        HttpServletRequest request=(HttpServletRequest)servletRequest;
        String method = request.getMethod();
        if(HttpMethod.GET.equals(method)){
            log.error("Oauth request error:{}",method);
            throw new HttpRequestMethodNotSupportedException(request.getMethod(), new String[] { "POST" });
        }
        String clientId =request.getParameter("client_id");
        String clientSecret=request.getParameter("client_secret");

        if (clientId == null) {
            log.error("No client credentials presented");
            //throw new BadCredentialsException("No client credentials presented");
        }

        if (clientSecret == null) {
            clientSecret = "";
        }
        clientId = clientId.trim();
        request.setAttribute("clientId",clientId);
        request.setAttribute("clientSecret",clientSecret);

        filterChain.doFilter(servletRequest,servletResponse);
    }

    @Override
    public void destroy()
    {

    }
}
