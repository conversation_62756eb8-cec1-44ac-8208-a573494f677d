package com.apec;

import javafx.application.Application;
import org.apache.tomcat.util.net.openssl.ciphers.Authentication;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.security.oauth2.client.EnableOAuth2Sso;
import org.springframework.context.ApplicationContext;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Auther: liliwei
 * @Date: 2018/5/23 15:19
 * @Description:
 */
@SpringBootApplication
public class ClientAApplication
{
    public static void main(String[] args){
        SpringApplication.run(ClientAApplication.class,args);
    }
}
