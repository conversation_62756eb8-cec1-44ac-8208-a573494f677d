package com.apec;

import org.apache.tomcat.util.net.openssl.ciphers.Authentication;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.security.oauth2.client.EnableOAuth2Sso;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Auther: liliwei
 * @Date: 2018/5/23 15:19
 * @Description:
 */
@SpringBootApplication
@RestController
@EnableOAuth2Sso
public class ClientBApplication
{
    @GetMapping("/user")
    public Authentication user(Authentication user){
        return user;
    }

    public static void main(String[] args){
        SpringApplication.run(ClientBApplication.class, args);
    }
}
