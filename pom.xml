<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.apec</groupId>
    <artifactId>APEC_CJ101_DataDict</artifactId>
    <version>1.5.0-RELEASE</version>
    <packaging>pom</packaging>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>1.4.5.RELEASE</version>
        <relativePath />
    </parent>
    <!--
    全局参数变量
     看下备注
     -->
    <properties>
        <spring-context.version>4.3.7.RELEASE</spring-context.version>
        <spring-data-commons.version>1.12.8.RELEASE</spring-data-commons.version>
        <spring-cloud-commons.version>1.1.9.RELEASE</spring-cloud-commons.version>
        <spring-boot-starter.version>1.4.5.RELEASE</spring-boot-starter.version>
        <magpie-cb-spring.version>1.0.0-RELEASE</magpie-cb-spring.version>

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <java.version>1.8</java.version>
    </properties>
    <modules>
        <module>apec-basic-datadict</module>
        <module>base-datadict-dto</module>
        <module>base-datadict-push-dto</module>
	    <module>datadict-server</module>
        <module>base-data-dict-feign-client</module>
    </modules>



    <!-- 连接到nexus -->
    <distributionManagement>
        <repository>
            <id>nexus-releases</id>
            <name>Nexus Release Repository</name>
            <url>http://192.168.7.116:8081/content/repositories/releases/</url>
        </repository>
        <snapshotRepository>
            <id>nexus-snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://192.168.7.116:8081/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>Camden.SR7</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <finalName>${project.artifactId}-${project.version}</finalName>
        <plugins>
            <!-- 指定jdk -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
            </plugin>
        </plugins>
    </build>


</project>