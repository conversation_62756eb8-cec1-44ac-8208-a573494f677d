#************** eureka.test.magpie.com
#************ mysql.test.magpie.com
#************** redis.test.magpie.com
#************** rabbitmq.test.magpie.com
#************ ftp.test.magpie.com
#spring cloud 配置
spring.application.name=base-datadict-zhao

eureka.instance.hostname=http://**************:1111/

spring.datasource.primary.url=******************************************************************************************************
spring.datasource.primary.username=root
spring.datasource.primary.password=123456
spring.datasource.primary.driver-class-name=com.mysql.jdbc.Driver


redis.database=1
redis.host=redis.test.magpie.com
redis.port=16379
redis.password=foobared
redis.maxActive=8
redis.maxWait=-1
redis.maxIdle=8
redis.minIdle=0
redis.timeout=0

#spring.redis.host=redis.test.magpie.com
#spring.redis.port=7000
#spring.redis.host2=redis.test.magpie.com
#spring.redis.port2=7001
spring.redis.host=redis.test.magpie.com
spring.redis.port=7001
spring.redis.host2=redis.test.magpie.com
spring.redis.port2=7003
spring.redis.timeout=5000
spring.redis.maxRedirections=5


spring.rabbitmq.host=rabbitmq.test.magpie.com
spring.rabbitmq.port=5672
spring.rabbitmq.username=apec
spring.rabbitmq.password=123456
spring.rabbitmq.virtual-host=/apec


ftp.host=ftp.test.magpie.com
ftp.port=21
ftp.username=root
ftp.password=znw@ap-ec
ftp.workingDir=/data/file/
ftp.url.fix=http://shoptest.ap-ec.cn/file


