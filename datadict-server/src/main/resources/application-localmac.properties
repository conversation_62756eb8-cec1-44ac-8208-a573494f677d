
eureka.instance.hostname=eureka.localmac.cncsen.com:1111

server.port=32001

spring.datasource.primary.url=************************************************************************************************************************************************
spring.datasource.primary.username=cncsen
spring.datasource.primary.password=cncsen
spring.datasource.primary.driver-class-name=com.mysql.jdbc.Driver


redis.database=1
redis.host=redis.localmac.cncsen.com
redis.port=6379
redis.password=foobared
redis.maxActive=8
redis.maxWait=-1
redis.maxIdle=8
redis.minIdle=0
redis.timeout=0

spring.redis.host=redis.localmac.cncsen.com
spring.redis.port=7000
spring.redis.host2=redis.localmac.cncsen.com
spring.redis.port2=7001
spring.redis.timeout=5000
spring.redis.maxRedirections=5


spring.rabbitmq.host=rabbitmq.localmac.cncsen.com
spring.rabbitmq.port=5672
spring.rabbitmq.username=apec
spring.rabbitmq.password=123456
spring.rabbitmq.virtual-host=/apec


ftp.host=ftp.localmac.cncsen.com
ftp.port=21
ftp.username=root
ftp.password=znw@ap-ec
ftp.workingDir=/data/file/
ftp.url.fix=http://shoptest.ap-ec.cn/file




