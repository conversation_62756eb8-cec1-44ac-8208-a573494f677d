spring.profiles.active=local
#每次加载hibernate时都会删除上一次的生成的表
#spring.jpa.properties.hibernate.hbm2ddl.auto=create-drop
#第一次加载hibernate时根据model类会自动建立起表的结构（前提是先建立好数据库），以后加载hibernate时根据model类自动更新表结构
spring.jpa.properties.hibernate.hbm2ddl.auto=update
spring.jmx.enabled=false
#spring.jpa.properties.hibernate.show_sql=false
#spring.jpa.properties.hibernate.format_sql=true
#数据库连接超时处理
spring.datasource.primary.time-between-eviction-runs-millis=10000
spring.datasource.primary.min-evictable-idle-time-millis=20000
spring.datasource.primary.test-while-idle=true
#重试机制
spring.cloud.loadbalancer.retry.enabled=true

#========================修改默认message配置start==================================
#指定message的basename，多个以逗号分隔，如果不加包名的话，默认从classpath路径开始，默认: messages
spring.messages.basename=errormessage,conts,cberrormessage,datadicterrormessage
#设定加载的资源文件缓存失效时间，-1的话为永不过期，默认为-1
spring.messages.cache-seconds=-1
#========================修改默认message配置end====================================
#spring cloud 配置
spring.application.name=base-datadict
server.port=10001
#我们可以指定微服务的名称后续在调用的时候只需要使用该名称就可以进行服务的访问
#显示IP地址
eureka.instance.prefer-ip-address=true
eureka.instance.instance-id=${spring.cloud.client.ipAddress}:${server.port}
#对应服务注册中心的配置内容，指定服务注册中心的位置
eureka.client.serviceUrl.defaultZone=http://${eureka.instance.hostname}/eureka/
#机器ID,如果不加默认为1
workerId=1
#指定log4j2配置文件路径
logging.config=classpath:log4j2.xml

rpc.datadict.mgtflush.flushDictAllCacheJob =http://BASE-DATADICT/mgtflush/flushDictAllCacheJob
rpc.datadict.mgtflush.flushDictSubCache = http://BASE-DATADICT/mgtflush/flushDictSubCache
rpc.datadict.mgtflush.flushDictCache = http://BASE-DATADICT/mgtflush/flushDictCache
rpc.datadict.facadebasicSelectListCache.flushCacheJob = http://BASE-DATADICT/facadebasicSelectListCache/flushCacheJob
rpc.datadict.basicDataDictSub.findListExt = http://BASE-DATADICT/basicDataDictSub/findListExt

dictcache.scheduled.flushJob = 0 0 */2 * * ?
thread.pool.poolName = datadict