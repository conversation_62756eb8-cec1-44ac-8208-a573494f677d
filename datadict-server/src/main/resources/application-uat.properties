
eureka.instance.hostname=eureka.uat.magpie.com:1111

spring.datasource.primary.url=***********************************************************************************************
spring.datasource.primary.username=znw
spring.datasource.primary.password=Znw&231
spring.datasource.primary.driver-class-name=com.mysql.jdbc.Driver

redis.database=1
redis.host=*************
redis.port=26379
redis.password=znw@123
redis.maxActive=8
redis.maxWait=-1
redis.maxIdle=8
redis.minIdle=0
redis.timeout=0

spring.redis.host=*************
spring.redis.port=17011
spring.redis.host2=*************
spring.redis.port2=17012
spring.redis.timeout=5000
spring.redis.maxRedirections=5

spring.rabbitmq.host=*************
spring.rabbitmq.port=5674
spring.rabbitmq.username=apec
spring.rabbitmq.password=123456
spring.rabbitmq.virtual-host=/apec


ftp.host=ftp.uat.magpie.com
ftp.port=21
ftp.username=root
ftp.password=znw@ap-ec
ftp.workingDir=/data/file/
ftp.url.fix=http://shoptest.ap-ec.cn/file


