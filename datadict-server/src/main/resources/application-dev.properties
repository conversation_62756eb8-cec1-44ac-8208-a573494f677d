
eureka.instance.hostname=eureka.mw.znwdev.top:1111

spring.datasource.primary.url=****************************************************************************************************
spring.datasource.primary.username=root
spring.datasource.primary.password=Admin!@123
spring.datasource.primary.driver-class-name=com.mysql.jdbc.Driver

redis.database=1
redis.host=redis.mw.znwdev.top
redis.port=16379
redis.password=znw@123
redis.maxActive=8
redis.maxWait=-1
redis.maxIdle=8
redis.minIdle=0
redis.timeout=0

spring.redis.host=redis.mw.znwdev.top
spring.redis.port=7001
spring.redis.host2=redis.mw.znwdev.top
spring.redis.port2=7002
spring.redis.timeout=5000
spring.redis.maxRedirections=5

#  http://rabbit.mw.znwdev.top:15672
spring.rabbitmq.host=rabbitmq.mw.znwdev.top
spring.rabbitmq.port=5672
spring.rabbitmq.username=apec
spring.rabbitmq.password=123456
spring.rabbitmq.virtual-host=/apec

ftp.host=ftp.mw.znwdev.top
ftp.port=21
ftp.username=root
ftp.password=znw@ap-ec
ftp.workingDir=/data/file/
ftp.url.fix=http://shoptest.ap-ec.cn/file


