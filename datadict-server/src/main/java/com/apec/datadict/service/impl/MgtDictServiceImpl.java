package com.apec.datadict.service.impl;

import com.apec.datadict.common.DataDictConts;
import com.apec.datadict.config.DataDictValConfig;
import com.apec.datadict.dto.*;
import com.apec.datadict.service.BasicDataDictService;
import com.apec.datadict.service.MgtDictService;
import com.apec.datadictsub.dto.BasicDataDictSubDTO;
import com.apec.datadictsub.dto.BasicDataDictSubQueryDTO;
import com.apec.datadictsub.service.BasicDataDictSubService;
import com.apec.framework.common.enumtype.EnableFlag;
import com.apec.framework.common.exception.ApecRuntimeException;
import com.apec.framework.common.model.ResultData;
import com.apec.framework.common.util.BeanUtils;
import com.apec.framework.common.util.DateUtils;
import com.apec.framework.common.util.JsonUtils;
import com.apec.magpie.cb.dto.open.OpenPushReqDTO;
import com.apec.magpie.cbrpc.service.RestfulCommonService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class MgtDictServiceImpl implements MgtDictService, DataDictConts {
    private static final Logger LOG =  LoggerFactory.getLogger(MgtDictServiceImpl.class);
    @Autowired
    private DataDictValConfig valConfig;
    @Autowired
    private RestfulCommonService restfulCommonService;
    @Autowired
    private BasicDataDictService basicDataDictService;
    @Autowired
    private BasicDataDictSubService basicDataDictSubService;

    @Override
    public List<BasicDataDictDTO> queryDataDictList(String oecdNo, List<String> dictCodeList, String userId)
    {
        BasicDataDictQueryDTO query = new BasicDataDictQueryDTO();
        query.setOecdNo(oecdNo);
        query.setDictCodeLS(dictCodeList);
        query.setEnableFlag(EnableFlag.Y);
        List<BasicDataDictDTO> listDB = basicDataDictService.findListExt(query,userId);
        return listDB;
    }

    @Override
    public List<BasicDataDictSubDTO> queryDataDictSubList(String oecdNo, List<String> dictCodeList, String userId)
    {
        BasicDataDictSubQueryDTO query = new BasicDataDictSubQueryDTO();
        query.setOecdNo(oecdNo);
        query.setDictCodeLS(dictCodeList);
        query.setEnableFlag(EnableFlag.Y);
        List<BasicDataDictSubDTO> listDB = basicDataDictSubService.findListExt(query,userId);
        return listDB;
    }

    @Override
    public List<BasicDataDictSubDTO> queryDataDictSubList(String oecdNo, String dictCode, List<String> dictCodeSubList, String userId)
    {
        BasicDataDictSubQueryDTO query = new BasicDataDictSubQueryDTO();
        query.setOecdNo(oecdNo);
        query.setDictCode(dictCode);
        query.setDictSubCodeLS(dictCodeSubList);
        query.setEnableFlag(EnableFlag.Y);
        List<BasicDataDictSubDTO> listDB = basicDataDictSubService.findListExt(query,userId);
        return listDB;
    }


    @Override
    public ResultData pushDataDictToTargetEnv(PushDataDictToTargetEnvDTO param, String userId) throws ApecRuntimeException {
        if(StringUtils.isBlank(userId)){
            LOG.error("source request not get login userId.");
            throw new ApecRuntimeException(ERROR_CB_SOURCE_REQUEST_NOT_GET_LOGIN_USER_ID);
        }
        if(StringUtils.isBlank(param.getPushKeyWord())){
            LOG.error("lost param pushKeyWord.");
            throw new ApecRuntimeException(ERROR_CB_LOST_PARAM, new String[]{"pushKeyWord"});
        }
        if(CollectionUtils.isEmpty(param.getDictCodeList())){
            LOG.error("lost param dictCodeList, or dictCodeList is empty.");
            throw new ApecRuntimeException(ERROR_CB_LOST_PARAM, new String[]{"dictCodeList"});
        }
        if(valConfig.getPushDataDictMaxSize() < param.getDictCodeList().size()){
            LOG.error("pushDictToTargetEnv maxSize big then {}.", valConfig.getPushDataDictMaxSize());
            throw new ApecRuntimeException(ERROR_DATA_DICT_PUSH_TARGET_ENV_DATA_TO_BIG, new String[]{ valConfig.getPushDataDictMaxSize().toString() });
        }
        if(StringUtils.isBlank(param.getOecdNo())) {
            param.setOecdNo(DEFAULT_OECD_NO_APEC);
        }
        OpenPushReqDTO pushReq = new OpenPushReqDTO();
        pushReq.setBizSrlNo("pushDictToTargetEnv_" + DateUtils.format(new Date(), DATE_FORMAT_DATETIME_SSS));
        pushReq.setPushKeyWord(param.getPushKeyWord());
        pushReq.setPushReason("sync datadict info.");
        List<BasicDataDictDTO> listDb = queryDataDictList(param.getOecdNo(), param.getDictCodeList(), userId);
        List<PushOneDictDTO> list = listDb.stream().map(dict -> {
            PushOneDictDTO dictRs = new PushOneDictDTO();
            BeanUtils.copyProperties(dict, dictRs);
            if(CollectionUtils.isNotEmpty(dict.getDictSubList())){
                List<PushOneDictSubDTO> dictSubList = dict.getDictSubList().stream().map(dictSub -> {
                    PushOneDictSubDTO dictSubRs = new PushOneDictSubDTO();
                    BeanUtils.copyProperties(dictSub, dictSubRs);
                    return dictSubRs;
                }).collect(Collectors.toList());
                dictRs.setDictSubList(dictSubList);
            }
            return dictRs;
        }).collect(Collectors.toList());
        BeanUtils.copyProperties(listDb, list);
        PushDataDictDTO data = new PushDataDictDTO(param.getOecdNo(), list);
        pushReq.setData(data);
        return restfulCommonService.rpcPushCenterPushTargetEnv(pushReq, userId);
    }

    @Override
    public ExecPushDataDictResDTO execPushDict(ExecPushDataDictReqDTO param, String userId) throws ApecRuntimeException {

        if(StringUtils.isBlank(userId)){
            LOG.error("source request not get login userId.");
            throw new ApecRuntimeException(ERROR_CB_SOURCE_REQUEST_NOT_GET_LOGIN_USER_ID);
        }
        if(StringUtils.isAnyBlank(param.getOecdNo())){
            LOG.error("open client lost param: oecdNo.");
            throw new ApecRuntimeException(ERROR_CB_LOST_PARAM, new String[]{"oecdNo"});
        }
        if(CollectionUtils.isEmpty(param.getDictList())){
            LOG.error("open client lost param: dictList.");
            throw new ApecRuntimeException(ERROR_CB_PARAM_IS_NULL, new String[]{"dictList"});
        }
        List<String> dictCodeList = param.getDictList().stream().map(e -> e.getDictCode()).collect(Collectors.toList());
        List<BasicDataDictDTO> dictDbList = queryDataDictList(param.getOecdNo(), dictCodeList, userId);
        addDataDict(param, dictDbList, userId);
        updateDataDict(param, dictDbList, userId);
        List<BasicDataDictDTO> list = queryDataDictList(param.getOecdNo(), dictCodeList, userId);
        return new ExecPushDataDictResDTO(param.getOecdNo(), list);
    }




    private void addDataDict(ExecPushDataDictReqDTO param, List<BasicDataDictDTO> listDb, String userId)
    {
        List<BasicDataDictDTO> addDictList = param.getDictList().stream().filter(item ->
                !listDb.stream().map(e -> e.getDictCode()).collect(Collectors.toList()).contains(item.getDictCode())
        ).collect(Collectors.toList());

        addDictList.stream().forEach(oneDict -> {
            BasicDataDictDTO save = new BasicDataDictDTO();
            BeanUtils.copyProperties(oneDict, save);
            save.setImg(oneDict.getImg());
            save.setImgId(oneDict.getImgId());
            BasicDataDictDTO db = basicDataDictService.save(save, userId);
            LOG.info("save BasicDataDict, data:{}", JsonUtils.toDefaultJSONString(db));

            oneDict.getDictSubList().stream().forEach(oneSub -> {
                BasicDataDictSubDTO saveOneSub = new BasicDataDictSubDTO();
                BeanUtils.copyProperties(oneSub, saveOneSub);
                BasicDataDictSubDTO oneSubDb = basicDataDictSubService.save(saveOneSub, userId);
                LOG.info("save BasicDataDictSub, data:{}", JsonUtils.toDefaultJSONString(oneSubDb));
            });
        });
    }

    private void updateDataDict(ExecPushDataDictReqDTO param, List<BasicDataDictDTO> list, String userId)
    {
        List<BasicDataDictDTO> dictDbList = list.stream().filter(item ->
                param.getDictList().stream().map(e -> e.getDictCode() ).collect(Collectors.toList()).contains(item.getDictCode())
        ).collect(Collectors.toList());

        Map<String,List<BasicDataDictDTO>> dictListMapParam = param.getDictList().stream().collect(Collectors.groupingBy(e -> e.getDictCode()));
        dictDbList.stream().forEach(oneDictDb -> {
            BasicDataDictDTO oneDictParam = dictListMapParam.get(oneDictDb.getDictCode()).get(ZERO);

            BasicDataDictDTO update = new BasicDataDictDTO();
            BeanUtils.copyPropertiesIgnoreNullFilds(oneDictParam, update);
            update.setId(oneDictDb.getId());
            BasicDataDictDTO db = basicDataDictService.updateDTO(update, userId);
            LOG.info("update BasicDataDict, data:{}", JsonUtils.toDefaultJSONString(db));

            addDataDictSub(oneDictParam.getDictSubList(), oneDictDb.getDictSubList(), userId);
            updateDataDictSub(oneDictParam.getDictSubList(), oneDictDb.getDictSubList(), userId);
        });
    }

    private void addDataDictSub(List<BasicDataDictSubDTO> oneDictSubParamList, List<BasicDataDictSubDTO> oneDictSubDblist, String userId)
    {
        List<BasicDataDictSubDTO> addList = oneDictSubParamList.stream().filter(item ->
                !oneDictSubDblist.stream().map(e -> e.getDictSubCode()).collect(Collectors.toList()).contains(item.getDictSubCode())
        ).collect(Collectors.toList());

        addList.stream().forEach(e -> {
            BasicDataDictSubDTO save = new BasicDataDictSubDTO();
            BeanUtils.copyProperties(e, save);
            save.setImg(e.getImg());
            save.setImgId(e.getImgId());
            BasicDataDictSubDTO db = basicDataDictSubService.save(save, userId);
            LOG.info("save BasicDataDict, data:{}", JsonUtils.toDefaultJSONString(db));
        });
    }

    private void updateDataDictSub(List<BasicDataDictSubDTO> oneDictSubParamList, List<BasicDataDictSubDTO> oneDictSubDblist, String userId)
    {
        List<BasicDataDictSubDTO> dictSubDbList = oneDictSubDblist.stream().filter(item ->
                oneDictSubParamList.stream().map(e -> e.getDictSubCode() ).collect(Collectors.toList()).contains(item.getDictSubCode())
        ).collect(Collectors.toList());

        Map<String,List<BasicDataDictSubDTO>> dictSubListMapParam = oneDictSubParamList.stream().collect(Collectors.groupingBy(e -> e.getDictSubCode()));
        dictSubDbList.stream().forEach(oneDictSubDb -> {
            BasicDataDictSubDTO oneDictSubParam = dictSubListMapParam.get(oneDictSubDb.getDictSubCode()).get(ZERO);

            BasicDataDictSubDTO update = new BasicDataDictSubDTO();
            BeanUtils.copyPropertiesIgnoreNullFilds(oneDictSubParam, update);
            update.setId(oneDictSubDb.getId());
            BasicDataDictSubDTO db = basicDataDictSubService.updateDTO(update, userId);
            LOG.info("update BasicDataDictSub, data:{}", JsonUtils.toDefaultJSONString(db));
        });
    }

}
