package com.apec.datadict.service.impl;

import com.apec.datadict.config.DataDictValConfig;
import com.apec.datadict.dto.BasicDataDictDTO;
import com.apec.datadictsub.dto.BasicDataDictSubDTO;
import com.apec.datadictsub.dto.BasicDataDictSubQueryDTO;
import com.apec.framework.common.exception.ApecRuntimeException;
import com.apec.framework.common.model.ResultData;
import com.apec.framework.common.util.JsonUtils;
import com.apec.framework.springcloud.SpringCloudClient;
import com.apec.magpie.cb.common.utils.SnowFlakeKeyGen;
import com.apec.magpie.cb.constant.IErrorCfg;
import com.apec.datadict.service.MgtDataDictRpcService;
import com.apec.magpie.cb.dto.ApecRpcParamDTO;
import com.apec.magpie.cb.enums.pic.EnumInfoPicShowGroup;
import com.apec.magpie.cb.enums.pic.EnumInfoPicType;
import com.apec.magpie.cbrpc.service.RestfulCommonService;
import com.apec.mgtpic.dto.AddPicReqDTO;
import com.apec.selectlist.dto.BasicSelectListCacheDTO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;

@Service
public class MgtDataDictRpcServiceImpl implements MgtDataDictRpcService, IErrorCfg {

    private final Logger LOG =  LoggerFactory.getLogger( getClass());

    @Autowired
    private SpringCloudClient springCloudClient;

    @Autowired
    private RestfulCommonService restfulCommonService;

    @Autowired
    private DataDictValConfig valConfig;

    @Override
    public ResultData rpcFlushCacheBasicDataDictSubJob(BasicDataDictSubDTO param, String userId)
            throws ApecRuntimeException {
        String msg = "rpc刷新数据字典子项";
        try{
            ApecRpcParamDTO rpcParam = new ApecRpcParamDTO();
            rpcParam.setUserId(userId);
            rpcParam.setFormJSON(param);
            String jsonStr = JsonUtils.toDefaultJSONString(rpcParam);
            LOG.info("调用 {}，参数:{}",msg,jsonStr);
            ResultData rs = springCloudClient.restfulPost(valConfig.getRpcDataDictMgtflushFlushDictSubCache(),jsonStr);

            LOG.info("调用 {}，结果:{}",msg,JsonUtils.toJSONString(rs));
            return rs;
        }catch (Exception e){
            return new ResultData(false,ERROR_CB_RPC_EXCEPTION_SOME_INFO,null);
        }
    }

    @Override
    public ResultData rpcFlushCacheBasicDataDictJob(BasicDataDictDTO param, String userId)
            throws ApecRuntimeException {
        String msg = "rpc刷新数据字典项";
        try{
            ApecRpcParamDTO rpcParam = new ApecRpcParamDTO();
            rpcParam.setUserId(userId);
            rpcParam.setFormJSON(param);
            String jsonStr = JsonUtils.toDefaultJSONString(rpcParam);
            LOG.info("调用 {}，参数:{}",msg,jsonStr);
            ResultData rs = springCloudClient.restfulPost(valConfig.getRpcDataDictMgtflushFlushDictCache(),jsonStr);
            LOG.info("调用 {}，结果:{}",msg,JsonUtils.toJSONString(rs));
            return rs;
        }catch (Exception e){
            return new ResultData(false,ERROR_CB_RPC_EXCEPTION_SOME_INFO,null);
        }
    }

    @Override
    public ResultData rpcFlushSelectListCache(BasicSelectListCacheDTO param, String userId)
            throws ApecRuntimeException {
        String msg = "rpc刷新选择项管理";
        try{
            ApecRpcParamDTO rpcParam = new ApecRpcParamDTO();
            rpcParam.setUserId(userId);
            rpcParam.setFormJSON(param);
            String jsonStr = JsonUtils.toDefaultJSONString(rpcParam);
            LOG.info("调用 {}，参数:{}",msg,jsonStr);
            ResultData rs = springCloudClient.restfulPost(valConfig.getRpcDataDictFacadebasicSelectListCacheFlushCacheJob(),jsonStr);
            LOG.info("调用 {}，结果:{}",msg,JsonUtils.toJSONString(rs));
            return rs;
        }catch (Exception e){
            return new ResultData(false,ERROR_CB_RPC_EXCEPTION_SOME_INFO,null);
        }
    }

    @Override
    public void addDataDictSubImgInPicPool(String methodName, BasicDataDictSubDTO one, String userId)
            throws ApecRuntimeException {
        if(StringUtils.isBlank(one.getImg())){
            LOG.error("img is null, skip add pic pool.");
            return;
        }
        //向图库新增数据
        AddPicReqDTO addPic = new AddPicReqDTO();
        addPic.setId(StringUtils.isBlank(one.getImgId()) ? SnowFlakeKeyGen.nextIdStr() : one.getImgId());
        addPic.setOecdNo(one.getOecdNo());
        addPic.setPlantformId(one.getPlantformId());
        addPic.setDomain("dataDict");
        addPic.setType("dataDictSub");
        addPic.setInfoCode(one.getDictCode() +"|" + one.getDictSubCode());
        addPic.setInfoId(one.getId());
        addPic.setPicPath(one.getImg());
        addPic.setName(one.getDictSubValue());
        addPic.setInfoImgType(EnumInfoPicType.MAIN.getCode());
        addPic.setClsInfoId("datadict");
        addPic.setShowGroupList(Arrays.asList(EnumInfoPicShowGroup.MAIN_SHOW_LIST.getCode()));
        //新增或更新图片
        try{
            restfulCommonService.addPicPool( addPic, StringUtils.isBlank(userId) ? "dataDictSub" : userId);
            LOG.debug("add datadictsub img in pic pool ok.");
        }catch (Exception e){
            LOG.error("add datadictsub img in pic pool fail, param:{}", JsonUtils.toDefaultJSONString(addPic) , e );
        }
    }

    @Override
    public void addDataDictImgInPicPool(String methodName, BasicDataDictDTO one, String userId)
            throws ApecRuntimeException {
        if(StringUtils.isBlank(one.getImg())){
            LOG.error("img is null, skip add pic pool.");
            return;
        }
        //向图库新增数据
        AddPicReqDTO addPic = new AddPicReqDTO();
        addPic.setId(StringUtils.isBlank(one.getImgId()) ? SnowFlakeKeyGen.nextIdStr() : one.getImgId());
        addPic.setOecdNo(one.getOecdNo());
        addPic.setPlantformId(one.getPlantformId());
        addPic.setDomain("dataDict");
        addPic.setType("dataDict");
        addPic.setInfoCode(one.getDictCode());
        addPic.setInfoId(one.getId());
        addPic.setPicPath(one.getImg());
        addPic.setName(one.getDictValue());
        addPic.setInfoImgType(EnumInfoPicType.MAIN.getCode());
        addPic.setClsInfoId("datadict");
        addPic.setShowGroupList(Arrays.asList(EnumInfoPicShowGroup.MAIN_SHOW_LIST.getCode()));
        //新增或更新图片
        try{
            restfulCommonService.addPicPool( addPic, StringUtils.isBlank(userId) ? "dataDict" : userId);
            LOG.debug("add datadict img in pic pool ok.");
        }catch (Exception e){
            LOG.error("add datadict img in pic pool fail, param:{}", JsonUtils.toDefaultJSONString(addPic) , e );
        }
    }

    @Override
    public ResultData rpcFindListExtBasicDataDictSubList(BasicDataDictSubQueryDTO param, String userId)
            throws ApecRuntimeException
    {
        String msg = "rpc 查询字典子项列表";
        try{
            ApecRpcParamDTO rpcParam = new ApecRpcParamDTO();
            rpcParam.setUserId(userId);
            rpcParam.setFormJSON(param);
            String jsonStr = JsonUtils.toDefaultJSONString(rpcParam);
            LOG.info("调用 {}，参数:{}",msg,jsonStr);
            ResultData rs = springCloudClient.restfulPost(valConfig.getRpcDataDictBasicDataDictSubFindListExt(),jsonStr);
            LOG.info("调用 {}，结果:{}",msg,JsonUtils.toJSONString(rs));
            return rs;
        }catch (Exception e){
            return new ResultData(false,ERROR_CB_RPC_EXCEPTION_SOME_INFO,null);
        }
    }

}
