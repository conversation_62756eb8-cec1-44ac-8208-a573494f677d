package com.apec.datadict.service.impl;

import com.apec.aops.ThreadLocalUtil;
import com.apec.cache.base.CacheService;
import com.apec.datadict.dto.BasicDataDictDTO;
import com.apec.datadict.dto.BasicDataDictQueryDTO;
import com.apec.datadict.dto.DictItemSelectListQueryDTO;
import com.apec.datadict.dto.GetDictSomeItemSelectDTO;
import com.apec.datadict.model.BasicDataDict;
import com.apec.datadict.service.BasicDataDictService;
import com.apec.datadictsub.dto.BasicDataDictSubDTO;
import com.apec.datadictsub.dto.BasicDataDictSubQueryDTO;
import com.apec.datadictsub.model.BasicDataDictSub;
import com.apec.datadictsub.service.BasicDataDictSubService;
import com.apec.framework.common.enumtype.EnableFlag;
import com.apec.framework.common.exception.ApecRuntimeException;
import com.apec.framework.common.util.BeanUtils;
import com.apec.framework.common.util.JsonUtils;
import com.apec.magpie.cb.common.utils.EnumSelectListUtils;
import com.apec.magpie.cb.constant.CommonConts;
import com.apec.magpie.cb.dto.SelectDTO;
import com.apec.magpie.cb.dto.SelectQueryDTO;
import com.apec.magpie.cb.enums.EnumSelectListType;
import com.apec.magpie.cb.enums.dataDict.EnumDictLevel;
import com.apec.magpie.cb.enums.dataDict.EnumDictStatus;
import com.apec.magpie.cb.enums.dataDict.EnumDictType;
import com.apec.magpie.cbrpc.dto.SelectListCacheDTO;
import com.apec.magpie.cbrpc.service.RestfulCommonService;
import com.apec.datadict.service.ExtBaseDataDictService;
import com.apec.datadict.common.DataDictConts;
import com.apec.datadict.vo.BasicDataDictAndSubListDTO;
import com.apec.magpie.common.util.BeanUtil;
import com.apec.mgt.service.impl.ApecMgtExecutor;
import com.apec.selectlist.dto.CacheBasicSelectListCacheDTO;
import com.apec.selectlist.service.FacadeBasicSelectListCacheService;
import com.apec.selectlist.dto.BasicSelectListCacheDTO;
import com.apec.selectlist.service.BasicSelectListCacheService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 类 编 号：
 * 类 名 称：DataDictConts
 * 内容摘要：微服务基础接口实现(扩展实现)
 * 创建日期：2018/08/08
 *
 * <AUTHOR>
 */

@Service
public class ExtBaseDataDictServiceImpl implements ExtBaseDataDictService, DataDictConts {

    private Logger logger = LoggerFactory.getLogger(getClass());
    @Autowired
    private BasicDataDictService basicDataDictService;
    @Autowired
    private BasicDataDictSubService basicDataDictSubService;
    @Autowired
    private RestfulCommonService restfulCommonService;

    @Resource
    private CacheService cacheService;

    @Autowired
    private ApecMgtExecutor apecMgtExecutor;

    @Autowired
    private FacadeBasicSelectListCacheService facadeBasicSelectListCacheService;

    @Autowired
    private BasicSelectListCacheService basicSelectListCacheService;

    private static final String MARKET_CONFIG_PRIX = "marketConfig_";

    @Override
    public BasicDataDictAndSubListDTO findDictAndSubDict(BasicDataDictDTO basicDataDictDTO)
            throws ApecRuntimeException {
        if (StringUtils.isBlank(basicDataDictDTO.getDictCode())) {
            throw new ApecRuntimeException(ERROR_DICT_CODE_NOT_NULL);
        }
        BasicDataDictDTO queryDTO = new BasicDataDictDTO();
        queryDTO.setDictCode(basicDataDictDTO.getDictCode());
        queryDTO.setEnableFlag(EnableFlag.Y);
        queryDTO.setDictStatus(EnumDictStatus.OPEN.getCode());
        List<BasicDataDict> list = basicDataDictService.findList(queryDTO, "sys");
        if (CollectionUtils.isEmpty(list)) {
            throw new ApecRuntimeException(ERROR_DICT_CODE_NOT_EXIST);
        }
        BasicDataDictSubDTO basicDataDictSubDTO = new BasicDataDictSubDTO();
        basicDataDictSubDTO.setDictCode(list.get(0).getDictCode());
        basicDataDictSubDTO.setEnableFlag(EnableFlag.Y);
        List<BasicDataDictSub> listSub = basicDataDictSubService.findList(basicDataDictSubDTO, "sys");
        if (CollectionUtils.isEmpty(listSub)) {
            throw new ApecRuntimeException(ERROR_DICT_CODE_NOT_HAVE_SUB_ITEM);
        }

        BasicDataDictAndSubListDTO rsDTO = new BasicDataDictAndSubListDTO();
        BeanUtils.copyPropertiesIgnoreNullFilds(list.get(0), rsDTO);
        rsDTO.setSubDictList(basicDataDictSubService.convertList(listSub));
        return rsDTO;
    }


    @Override
    public List<SelectDTO> querySelectList(SelectQueryDTO selectQueryVo, String userId) {
        List<SelectDTO> relist = new ArrayList<>();
        if (null == selectQueryVo || Strings.isNullOrEmpty(selectQueryVo.getSelectType())) {
            selectQueryVo = new SelectQueryDTO();
            selectQueryVo.setSelectType(EnumSelectListType.ALL_TYPE.getCode());
        }
        EnumSelectListType type = EnumSelectListType.getByCode(selectQueryVo.getSelectType());
        //没有在枚举里面
        if (null == type) {
            logger.info("datadict code:{} in biz db.", selectQueryVo.getSelectType());
            //判断是否特殊缓存从缓存获取。
            CacheBasicSelectListCacheDTO selectMgtDTO = existSelectCacheMgt(selectQueryVo.getSelectType());
            if (null == selectMgtDTO) {
                logger.error("datadict code :{} not exit mgt db.", selectQueryVo.getSelectType());
                return relist;
            }
            String dataCacheKey = CommonConts.CACHE_BASIC_SELECT_LIST_CACHE_MGT_ONE_BIZ_DATA_PREFIX + selectMgtDTO.getSelectTypeCode().trim().toLowerCase();
            boolean flush = isFlushThisSelectCache(selectMgtDTO);
            logger.info("datadict code :{} , is flush:{}", selectQueryVo.getSelectType(), flush);
            String dataCacheVal = cacheService.get(dataCacheKey);
            if (flush || StringUtils.isBlank(dataCacheVal)) {
                SelectListCacheDTO rpcObj = new SelectListCacheDTO();
                BeanUtils.copyPropertiesIgnoreNullFilds(selectMgtDTO, rpcObj);
                List<SelectDTO> listData = restfulCommonService.flushOneTypeSelectList(rpcObj, userId);
                logger.info("datadict code :{} , target server result listData:{}", JsonUtils.toDefaultJSONString(listData));
                syncUpdateSelectListCacheDB(selectMgtDTO.getSelectTypeCode(), listData, selectMgtDTO.getId(), userId);
                cacheService.add(dataCacheKey, JsonUtils.toJSONString(listData), selectMgtDTO.getFlushMinute() * 60);
                return listData;
            }
            //获取
            relist = JsonUtils.parseArray(dataCacheVal, SelectDTO.class);
            return relist;
        } else {
            logger.info("datadict code:{} in enum.", selectQueryVo.getSelectType());
            //查询所有的枚举下拉列表
            relist = EnumSelectListUtils.selectList(type);
            return relist;
        }
    }

    @Override
    public List<SelectDTO> queryDictItemSelectList(BasicDataDictSubDTO query) {
        return getDictCodeSelectListCache(query.getDictCode());
    }

    public List<SelectDTO> getDictCodeSelectListCache(String dictCode, String status) {
        if (StringUtils.isNotBlank(dictCode)) {
            String cacheVal = cacheService.get(getDataDictSelCacheKey(dictCode, status));
            if (StringUtils.isBlank(cacheVal)) {
                if (StringUtils.isNotBlank(dictCode)) {
                    logger.error("dictCode:{} not exist in cache.", dictCode);
                } else {
                    logger.error("allDictCode not exist in cache.");
                }
                flushOneDictCache(dictCode, status);
                cacheVal = cacheService.get(getDataDictSelCacheKey(dictCode, status));
            }
            if (StringUtils.isNotBlank(cacheVal)) {
                List<SelectDTO> list = JsonUtils.parseArray(cacheVal, SelectDTO.class);
                return list;
            }
        }
        return Collections.emptyList();
    }

    public List<SelectDTO> getDictCodeSelectListCache(String dictCode) {
        if (StringUtils.isNotBlank(dictCode)) {
            String cacheVal = cacheService.get(getDataDictSelCacheKey(dictCode));
            if (StringUtils.isBlank(cacheVal)) {
                if (StringUtils.isNotBlank(dictCode)) {
                    logger.error("dictCode:{} not exist in cache.", dictCode);
                } else {
                    logger.error("allDictCode not exist in cache.");
                }
                flushOneDictCache(dictCode);
                cacheVal = cacheService.get(getDataDictSelCacheKey(dictCode));
            }
            if (StringUtils.isNotBlank(cacheVal)) {
                List<SelectDTO> list = JsonUtils.parseArray(cacheVal, SelectDTO.class);
                return list;
            }
        }
        return Collections.emptyList();
    }

    @Override
    public Map<String, List<SelectDTO>> queryMoreDictCodeSelectListMap(DictItemSelectListQueryDTO query) {
        logger.debug("{}", JsonUtils.toDefaultJSONString(query));
        Map<String, List<SelectDTO>> rsMap = new HashMap<>();
        if (null != query && CollectionUtils.isNotEmpty(query.getListData())) {
            query.getListData().stream().forEach(e -> {
                rsMap.put(e.getDictCode(), getDictCodeSelectListCache(e.getDictCode()));
            });
        }
        return rsMap;
    }

    @Override
    public Map<String, List<SelectDTO>> queryMoreDictCodeSelectListMap(DictItemSelectListQueryDTO query, String status) {
        logger.debug("{}", JsonUtils.toDefaultJSONString(query));
        Map<String, List<SelectDTO>> rsMap = new HashMap<>();
        if (null != query && CollectionUtils.isNotEmpty(query.getListData())) {
            query.getListData().stream().forEach(e -> {
                rsMap.put(e.getDictCode(), getDictCodeSelectListCache(e.getDictCode(), status));
            });
        }
        return rsMap;
    }

    @Override
    public List<SelectDTO> getDictSomeItemSelect(GetDictSomeItemSelectDTO param) {
        logger.debug("{}", JsonUtils.toDefaultJSONString(param));
        String cacheVal = cacheService.get(getDataDictSelCacheKey(param.getDictCode()));
        if (StringUtils.isBlank(cacheVal)) {
            flushDictCache();
            cacheVal = cacheService.get(getDataDictSelCacheKey(param.getDictCode()));
        }
        List<SelectDTO> list = JsonUtils.parseArray(cacheVal, SelectDTO.class);
        if (CollectionUtils.isNotEmpty(param.getSubCodeList())) {
            return list.stream().filter(e -> param.getSubCodeList().contains(e.getKey())).collect(Collectors.toList());
        }
        return list;
    }


    @Override
    public List<SelectDTO> initMarkConfigDataDict(String marketId, String userId) {
        //添加市场配置的字典信息（市场字典）
        BasicDataDictDTO basicDataDictDTO = new BasicDataDictDTO();
        basicDataDictDTO.setDictCode(MARKET_CONFIG_PRIX + marketId);
        List<BasicDataDict> listDict = basicDataDictService.findList(basicDataDictDTO, userId);
        if (null == listDict || listDict.isEmpty()) {
            basicDataDictDTO = new BasicDataDictDTO();
            basicDataDictDTO.setDictCode(MARKET_CONFIG_PRIX + marketId);
            basicDataDictDTO.setDictStatus(EnumDictStatus.OPEN.getCode());
            basicDataDictDTO.setEnableFlag(EnableFlag.Y);
            basicDataDictDTO.setDictEnValue("market " + marketId + " config");
            basicDataDictDTO.setDictValue("市场" + marketId + "配置项");
            basicDataDictDTO.setDictLevel(EnumDictLevel.SERVICE.getCode());
            basicDataDictDTO.setDictType(EnumDictType.SYSTEM_DICT.getCode());
//            basicDataDictService.save(basicDataDictDTO, userId);
        }

        // 循环新增所有的配置项
        BasicDataDictSubDTO query = new BasicDataDictSubDTO();
        query.setDictCode(MARKET_CONFIG_PRIX + marketId);

        List<BasicDataDictSub> listData = basicDataDictSubService.findList(query, userId);
        if (CollectionUtils.isNotEmpty(listData)) {
            logger.info("datadict info:{}", JsonUtils.toJSONString(listData));
            return listData.stream().map(e -> new SelectDTO(e.getDictSubCode(), e.getDictSubValue())).collect(Collectors.toList());
        } else {
            logger.error("datadict info is empty.");
            return new ArrayList<>();
        }
    }

    @Override
    public void flushCacheJob(BasicDataDictDTO selectQueryVo) {
        logger.info("刷新字典下拉框缓存{}", JsonUtils.toJSONString(selectQueryVo));
        apecMgtExecutor.getExecutor().execute(() -> {
            BasicDataDictSubDTO querySub = new BasicDataDictSubDTO();
            querySub.setEnableFlag(EnableFlag.Y);
            querySub.setDictCode(selectQueryVo.getDictCode());
            flushDictSubCache(querySub);
        });
    }

    @Override
    public void flushDictAllCacheJob() {
        logger.info("刷新所有字典下拉框缓存");
        apecMgtExecutor.getExecutor().execute(() -> {
            flushDictCache();
        });
    }


    @Override
    public void flushDictSubCache(BasicDataDictSubDTO querySub) {
        if (StringUtils.isNotBlank(querySub.getDictCode())) {
            flushOneDictCache(querySub.getDictCode());
            // 刷新一遍启用子项的缓存
            flushOneDictCache(querySub.getDictCode(), "1");
        } else {
            logger.error("dictCode is null");
        }
    }

    @Override
    public void flushDictCache(BasicDataDictDTO param) {
        flushAllDataDict(getAllDataDict());
    }


    public void syncUpdateSelectListCacheDB(String selCode, List<SelectDTO> listData, String selectListCacheId, String userId) {
        apecMgtExecutor.getExecutor().execute(() -> {
            BasicSelectListCacheDTO dbSel = basicSelectListCacheService.findDTO(selectListCacheId, userId);
            if (null != dbSel) {
                if (null == listData || listData.isEmpty()) {
                    dbSel.setLastFailTime(new Date());
                } else {
                    dbSel.setLastSuccessTime(new Date());
                }
                //更新管理下拉里面的同步数据
                basicSelectListCacheService.updateDTO(dbSel, userId);
            } else {
                logger.error("datadict code:{} , id:{} is not in database.", selCode, selectListCacheId);
            }
        });
    }

    private CacheBasicSelectListCacheDTO existSelectCacheMgt(String type) {
        List<CacheBasicSelectListCacheDTO> listMgt = facadeBasicSelectListCacheService.getSelectListCacheFromCacheServer();
        for (CacheBasicSelectListCacheDTO one : listMgt) {
            if (one.getSelectTypeCode().equals(type)) {
                return one;
            }
        }
        return null;
    }

    private boolean isFlushThisSelectCache(CacheBasicSelectListCacheDTO objCache) {
        //check time
        if (null == objCache.getLastSuccessTime()) {
            return true;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(objCache.getLastSuccessTime());
        calendar.add(Calendar.MINUTE, objCache.getFlushMinute());
        if (calendar.getTime().compareTo(new Date()) > 0) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 刷新一个字典项的下来列表到缓存
     */
    public void flushDictCache() {
        List<BasicDataDictDTO> list = getAllDataDict();
        flushAllDataDict(list);
        list.stream().forEach(e -> {
            flushOneDictCache(e.getDictCode());
        });
    }

    private List<BasicDataDictDTO> getAllDataDict() {
        BasicDataDictQueryDTO query = new BasicDataDictQueryDTO();
        query.setEnableFlag(EnableFlag.Y);
        List<BasicDataDict> list = basicDataDictService.findList(query, ThreadLocalUtil.getUserIdOrNull());

        if (CollectionUtils.isNotEmpty(list)) {
            BasicDataDictSubDTO querySub = new BasicDataDictSubDTO();
            querySub.setEnableFlag(EnableFlag.Y);
            List<BasicDataDictSub> listSub = basicDataDictSubService.findList(querySub, ThreadLocalUtil.getUserIdOrNull());
            Map<String, List<BasicDataDictSub>> mapList = listSub.stream().collect(Collectors.groupingBy(BasicDataDictSub::getDictCode));
            return list.stream().map(e -> {
                BasicDataDictDTO dto = new BasicDataDictDTO();
                BeanUtil.copy(e, dto);
                if (mapList.containsKey(e.getDictCode())) {
                    dto.setDictSubList(mapList.get(e.getDictCode()).stream().map(sub -> {
                        BasicDataDictSubDTO subDTO = new BasicDataDictSubDTO();
                        BeanUtil.copy(sub, subDTO);
                        return subDTO;
                    }).collect(Collectors.toList()));
                } else {
                    dto.setDictSubList(Collections.emptyList());
                }
                return dto;
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    private void flushAllDataDict(List<BasicDataDictDTO> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            List<BasicDataDictDTO> sortList = list.stream().map(e -> {
                if (null == e.getOrderNumber()) {
                    e.setOrderNumber(ZERO);
                }
                return e;
            }).sorted(Comparator.comparing(BasicDataDictDTO::getOrderNumber).reversed())
                    .collect(Collectors.toList());
            List<SelectDTO> oneDictSel = sortList.stream().map(dict -> {
                return new SelectDTO(dict.getDictCode(), dict.getDictValue(), dict.getImg());
            }).collect(Collectors.toList());
            cacheService.add(getDataDictSelCacheKey(null), JsonUtils.toJSONString(oneDictSel));
        } else {
            logger.error("datadict list is empty.");
        }
    }

    private String getDataDictSelCacheKey(String dictCode) {
        if (StringUtils.isBlank(dictCode)) {
            return CommonConts.CACHE_BASIC_SELECT_LIST_DATADICT;
        } else {
            return CommonConts.CACHE_BASIC_SELECT_LIST_DATADICT + "_" + dictCode.toLowerCase();
        }
    }

    private String getDataDictSelCacheKey(String dictCode, String status) {
        if (StringUtils.isBlank(dictCode)) {
            return CommonConts.CACHE_BASIC_SELECT_LIST_DATADICT;
        } else {
            if (StringUtils.isNotBlank(status)) {
                return CommonConts.CACHE_BASIC_SELECT_LIST_DATADICT + "_" + dictCode.toLowerCase() + "_" + status;
            }
            return CommonConts.CACHE_BASIC_SELECT_LIST_DATADICT + "_" + dictCode.toLowerCase();
        }
    }

    private List<BasicDataDictSubDTO> queryDataDictSubListByDictCode(String dictCode) {
        BasicDataDictSubQueryDTO queryDTO = new BasicDataDictSubQueryDTO();
        queryDTO.setDictCode(dictCode);
        queryDTO.setEnableFlag(EnableFlag.Y);
        List<BasicDataDictSubDTO> listSub = basicDataDictSubService.findListExt(queryDTO, "sys");
        return listSub;
    }

    private void flushOneDictCache(String dictCode) {
        List<BasicDataDictSubDTO> listSub = queryDataDictSubListByDictCode(dictCode);
        if (CollectionUtils.isNotEmpty(listSub)) {
            List<BasicDataDictSubDTO> sortList = listSub.stream().map(e -> {
                if (null == e.getOrderNumber()) {
                    e.setOrderNumber(ZERO);
                }
                return e;
            }).sorted(Comparator.comparing(BasicDataDictSubDTO::getOrderNumber))
                    .collect(Collectors.toList());
            List<SelectDTO> selList = sortList.stream().map(e -> {
                return new SelectDTO(e.getDictSubCode(), e.getDictSubValue(), e.getImg());
            }).collect(Collectors.toList());
            cacheService.add(getDataDictSelCacheKey(dictCode), JsonUtils.toJSONString(selList));
            logger.info("flushOneDictCache dictCode:{} success, selList:{}", dictCode, JsonUtils.toDefaultJSONString(selList));
        } else {
            logger.error("flushOneDictCache dictCode:{} fail, listSub is empty.", dictCode);
        }
    }

    private void flushOneDictCache(String dictCode, String status) {
        List<BasicDataDictSubDTO> listSub = queryDataDictSubListByDictCode(dictCode);
        if (CollectionUtils.isNotEmpty(listSub)) {
            List<BasicDataDictSubDTO> sortList = listSub.stream().map(e -> {
                if (null == e.getOrderNumber()) {
                    e.setOrderNumber(ZERO);
                }
                return e;
            }).sorted(Comparator.comparing(BasicDataDictSubDTO::getOrderNumber))
                    .collect(Collectors.toList());
            // 状态为空返回全部
            List<SelectDTO> selList = sortList.stream()
                    .filter(e -> StringUtils.isBlank(status) || status.equals(e.getDictStatus()))
                    .map(e -> {
                        return new SelectDTO(e.getDictSubCode(), e.getDictSubValue(), e.getImg());
                    }).collect(Collectors.toList());
            cacheService.add(getDataDictSelCacheKey(dictCode, status), JsonUtils.toJSONString(selList));
            logger.info("flushOneDictCache dictCode:{} success, selList:{}", dictCode, JsonUtils.toDefaultJSONString(selList));
        } else {
            logger.error("flushOneDictCache dictCode:{} fail, listSub is empty.", dictCode);
        }
    }
}
