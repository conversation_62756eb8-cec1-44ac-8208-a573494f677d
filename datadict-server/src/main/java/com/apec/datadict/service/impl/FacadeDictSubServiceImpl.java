package com.apec.datadict.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.apec.datadict.dto.*;
import com.apec.datadict.service.BasicDataDictService;
import com.apec.datadict.service.MgtDictService;
import com.apec.datadictsub.dto.BasicDataDictSubDTO;
import com.apec.datadictsub.dto.BasicDataDictSubQueryDTO;
import com.apec.datadictsub.service.BasicDataDictSubService;
import com.apec.framework.common.exception.ApecRuntimeException;
import com.apec.framework.common.util.BeanUtils;
import com.apec.framework.common.util.DateUtils;
import com.apec.framework.common.util.JsonUtils;
import com.apec.magpie.cb.dto.CommonItemDTO;
import com.apec.magpie.cb.dto.DataDictSysConfigDTO;
import com.apec.magpie.cb.dto.SelectDTO;
import com.apec.magpie.cb.dto.SortAttrDTO;
import com.apec.magpie.cb.enums.EnumDictCode;
import com.apec.datadict.common.DataDictConts;
import com.apec.datadict.service.FacadeDictSubService;
import com.apec.magpie.cb.enums.EnumEnableStatus;
import com.apec.magpie.cb.enums.dataDict.EnumDictLevel;
import com.apec.magpie.cb.enums.dataDict.EnumDictType;
import com.apec.systemtrademode.dto.BasicSystemTradeModeDTO;
import com.apec.systemtrademode.dto.BasicSystemTradeModeQueryDTO;
import com.apec.systemtrademode.service.BasicSystemTradeModeService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
/**
 * 类 编 号：BL.datadict.Service.FacadeDictSubServiceImpl
 * 类 名 称：FacadeDictSubServiceImpl
 * 内容摘要：数据字典服务
 * <AUTHOR>
 * @date 2020-04-23 18:35:33
 */

@Service
public class FacadeDictSubServiceImpl implements FacadeDictSubService, DataDictConts {

    private static final Logger LOG =  LoggerFactory.getLogger(FacadeDictSubServiceImpl.class);
    @Autowired
    private BasicDataDictSubService basicDataDictSubService;
    @Autowired
    private BasicDataDictService basicDataDictService;
    @Autowired
    private BasicSystemTradeModeService basicSystemTradeModeService;
    @Autowired
    private MgtDictService mgtDictService;
    @Override
    public List<BasicDataDictSubDTO> queryNormalTradeMode(QueryNormalTradeModeDTO queryNormalTradeModeDTO) {
        BasicSystemTradeModeQueryDTO modelQueryDTO = new BasicSystemTradeModeQueryDTO();
        if(!Strings.isNullOrEmpty(queryNormalTradeModeDTO.getSubsystemCodings()))
        {
            modelQueryDTO.setSubSystemCodeLS(Arrays.asList(queryNormalTradeModeDTO.getSubsystemCodings().split(",")));
        }
        if(!Strings.isNullOrEmpty(queryNormalTradeModeDTO.getTradingModeCodings()))
        {
            modelQueryDTO.setTradeModeCodeLS(Arrays.asList(queryNormalTradeModeDTO.getTradingModeCodings().split(",")));
        }
        String subsystemLogo = EnumDictCode.SUB_SYSTEM_CODE.getCode();
        String tradingModeLogo = EnumDictCode.TRADE_MODEL.getCode();
        modelQueryDTO.setSubSystemLogo(subsystemLogo);
        modelQueryDTO.setTradeModeLogo(tradingModeLogo);

        List<BasicSystemTradeModeDTO> listTrade = basicSystemTradeModeService.findListExt(modelQueryDTO,"sys");
        List<String> listCodes = listTrade.stream().map( e -> e.getTradeModeCode()).distinct().collect(Collectors.toList());
        BasicDataDictSubQueryDTO subQueryDTO =  new BasicDataDictSubQueryDTO();
        subQueryDTO.setDictSubCodeLS(listCodes);

        List<BasicDataDictSubDTO> listRs = basicDataDictSubService.findListExt(subQueryDTO,"sys");
        return listRs;
    }

    @Override
    public Boolean whetherNormalOFSystem(String systemId) throws ApecRuntimeException {

        return null;
    }

    @Override
    public JSONObject queryDataDictSubListKV(DictItemSelectListQueryDTO param)
            throws ApecRuntimeException {
        if(CollectionUtils.isEmpty(param.getListData()) || StringUtils.isBlank(param.getDictStatus())){
            LOG.error("lost param,param:{}", JsonUtils.toJSONString(param));
            throw new ApecRuntimeException(ERROR_CB_LOST_PARAM,
                    new String[]{
                            Arrays.asList(
                                    "dictCode","dictStatus"
                            ).toString()
                    }
            );
        }
        List<SortAttrDTO> sortList = new ArrayList<>();
        sortList.add(new SortAttrDTO("orderNumber","asc"));

        JSONObject rsData = new JSONObject();
        param.getListData().stream().forEach(one -> {
            BasicDataDictSubQueryDTO queryDTO = new BasicDataDictSubQueryDTO();
            queryDTO.setDictCode(one.getDictCode());
            if(StringUtils.isNotBlank(param.getDictStatus())){
                queryDTO.setDictStatus(param.getDictStatus());
            }
            queryDTO.setSortList(sortList);
            List<BasicDataDictSubDTO> listData = basicDataDictSubService.findListExt(queryDTO,"faceUser");
            List<SelectDTO> listSel =  listData.stream().map(e -> new SelectDTO(e.getDictSubCode(),e.getDictSubValue())).collect(Collectors.toList());
            rsData.put(one.getDictCode(),listSel);
        });

        return rsData;
    }


    @Override
    public DataDictSysConfigDTO updateDataDictSysConfig(DataDictSysConfigDTO param, String userId)
            throws  ApecRuntimeException
    {
        if(param.checkReqParamIllegal4DataDictSysConfigUpdate()){
            throw new ApecRuntimeException(ERROR_CB_LOST_PARAM, new String[]{DataDictSysConfigDTO.getLostParamStr4DataDictSysConfigUpdate()});
        }
        return updateMallGoodsDateNum(param, userId);
    }
    @Override
    public DataDictSysConfigDTO queryDataDictSysConfig(DataDictSysConfigDTO param, String userId)
            throws  ApecRuntimeException
    {
        if(param.checkReqParamIllegal4DataDictSysConfigQuery()){
            throw new ApecRuntimeException(ERROR_CB_LOST_PARAM, new String[]{DataDictSysConfigDTO.getLostParamStr4DataDictSysConfigQuery()});
        }
        BasicDataDictDTO dataDict = getDefaultDataDict(param, userId);
        List<BasicDataDictSubDTO> list = mgtDictService.queryDataDictSubList(dataDict.getOecdNo(),dataDict.getDictCode(), Arrays.asList(param.getDictSubCode()), userId);
        if(CollectionUtils.isEmpty(list)){
            return param;
        }
        else {
            BasicDataDictSubDTO dbDictSub = list.get(ZERO);
            DataDictSysConfigDTO dictSysConfig = new DataDictSysConfigDTO();
            BeanUtils.copyProperties(dbDictSub, dictSysConfig);
            return dictSysConfig;
        }
    }


    public DataDictSysConfigDTO updateMallGoodsDateNum(DataDictSysConfigDTO dictSysConfig, String userId)
            throws  ApecRuntimeException
    {
        BasicDataDictDTO dataDict = getDefaultDataDict(dictSysConfig, userId);
        List<BasicDataDictSubDTO> list = mgtDictService.queryDataDictSubList(dataDict.getOecdNo(),dataDict.getDictCode(), Arrays.asList(dictSysConfig.getDictSubCode()), userId);
        BasicDataDictSubDTO dbDictSub = null;
        if(CollectionUtils.isEmpty(list)){
            BasicDataDictSubDTO dictSub = new BasicDataDictSubDTO();
            dictSub.setOecdNo(dataDict.getOecdNo());
            dictSub.setPlantformId(dataDict.getPlantformId());
            dictSub.setDictCode(dataDict.getDictCode());
            dictSub.setDictSubCode(dictSysConfig.getDictSubCode());
            dictSub.setDictSubEnValue(dictSysConfig.getDictSubValue());
            dictSub.setDictSubValue(dictSysConfig.getDictSubValue());
            dictSub.setRemarks("超级系统配置，不可动");
            dictSub.setDictStatus(EnumEnableStatus.OPEN.getCode());
            dbDictSub = basicDataDictSubService.save(dictSub, userId);
        }
        else {
            BasicDataDictSubDTO sub = list.get(ZERO);
            sub.setDictSubValue(dictSysConfig.getDictSubValue());
            sub.setDictSubEnValue(dictSysConfig.getDictSubValue());
            dbDictSub = basicDataDictSubService.updateDTO(sub, userId);
        }
        DataDictSysConfigDTO resultCfg = new DataDictSysConfigDTO();
        BeanUtils.copyProperties(dbDictSub, resultCfg);
        return resultCfg;
    }

    private String getDefaultDataDict4DictCode(DataDictSysConfigDTO dictSysConfig) {
        return  (StringUtils.isBlank(dictSysConfig.getOecdNo()) ? DEFAULT_OECD_NO_APEC : dictSysConfig.getOecdNo()) + "." + dictSysConfig.getDictCode();
    }

    private BasicDataDictDTO getDefaultDataDict(DataDictSysConfigDTO dictSysConfig, String userId)
    {
        BasicDataDictDTO dict = new BasicDataDictDTO();
        dict.setOecdNo(StringUtils.isBlank(dictSysConfig.getOecdNo()) ? DEFAULT_OECD_NO_APEC : dictSysConfig.getOecdNo());
        dict.setPlantformId(StringUtils.isBlank(dictSysConfig.getPlatformId()) ? DEFAULT_OECD_NO_APEC : dictSysConfig.getPlatformId());
        dict.setDictCode(getDefaultDataDict4DictCode(dictSysConfig));
        dict.setDictEnValue("super system config,not update");
        dict.setDictValue("超级系统配置，不可动");
        dict.setRemarks("超级系统配置，不可动");
        dict.setDictLevel(EnumDictLevel.OPERATION.getCode());
        dict.setDictStatus(EnumEnableStatus.OPEN.getCode());
        dict.setDictType(EnumDictType.SYSTEM_DICT.getCode());

        BasicDataDictQueryDTO query = new BasicDataDictQueryDTO();
        query.setDictCode(dict.getDictCode());
        List<BasicDataDictDTO> list = mgtDictService.queryDataDictList(dict.getOecdNo(), Arrays.asList(dict.getDictCode()), userId);
        if(CollectionUtils.isEmpty(list)){
           return basicDataDictService.save(dict, "sys");
        }
        else{
            return list.get(ZERO);
        }
    }
}
