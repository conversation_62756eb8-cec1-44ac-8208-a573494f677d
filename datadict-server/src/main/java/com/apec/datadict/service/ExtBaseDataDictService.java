package com.apec.datadict.service;

import com.apec.datadict.dto.BasicDataDictDTO;
import com.apec.datadict.dto.DictItemSelectListQueryDTO;
import com.apec.datadict.dto.GetDictSomeItemSelectDTO;
import com.apec.datadictsub.dto.BasicDataDictSubDTO;
import com.apec.framework.common.exception.ApecRuntimeException;
import com.apec.magpie.cb.dto.SelectDTO;
import com.apec.magpie.cb.dto.SelectQueryDTO;
import com.apec.datadict.vo.BasicDataDictAndSubListDTO;

import java.util.List;
import java.util.Map;

/**
 * 类 编 号：
 * 类 名 称：BaseDataDictService
 * 内容摘要：微服务基础接口
 * 创建日期：2018/08/08
 *
 * <AUTHOR>
 */
public interface ExtBaseDataDictService {

    /**
     * 根据id查询字典基本信息和字典子项信息列表
     *
     * @param basicDataDictDTO
     * @return
     * @throws ApecRuntimeException
     */
    BasicDataDictAndSubListDTO findDictAndSubDict(BasicDataDictDTO basicDataDictDTO)
            throws ApecRuntimeException;

    /**
     * 查询下拉列表
     *
     * @param selectQueryVo
     * @return
     */
    List<SelectDTO> querySelectList(SelectQueryDTO selectQueryVo, String userId);

    /**
     * 查询字典子项下拉列表
     *
     * @param selectQueryVo
     * @return
     */
    List<SelectDTO> queryDictItemSelectList(BasicDataDictSubDTO selectQueryVo);

    /**
     * 查询字典子项下拉列表(不刷新)
     *
     * @param selectQueryVo
     * @return
     */
    Map<String, List<SelectDTO>> queryMoreDictCodeSelectListMap(DictItemSelectListQueryDTO selectQueryVo);

    /**
     * 查询字典子项下拉列表(不刷新),状态筛选
     *
     * @param selectQueryVo
     * @return
     */
    Map<String, List<SelectDTO>> queryMoreDictCodeSelectListMap(DictItemSelectListQueryDTO selectQueryVo, String status);


    List<SelectDTO> getDictSomeItemSelect(GetDictSomeItemSelectDTO param);

    /**
     * @param marketId
     * @return
     */
    List<SelectDTO> initMarkConfigDataDict(String marketId, String userId);


    /**
     * 刷新一个字典缓存
     *
     * @param selectQueryVo
     */
    void flushCacheJob(BasicDataDictDTO selectQueryVo);

    /**
     * 使用任务刷新整个字典缓存
     */
    void flushDictAllCacheJob();

    /**
     * 刷新一个字典子项到缓存
     *
     * @param param
     */
    void flushDictSubCache(BasicDataDictSubDTO param);

    /**
     * 刷新字典项缓存
     *
     * @param param
     */
    void flushDictCache(BasicDataDictDTO param);

}
