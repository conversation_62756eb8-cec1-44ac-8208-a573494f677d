package com.apec.datadict.service;

import com.apec.datadict.dto.BasicDataDictDTO;
import com.apec.datadictsub.dto.BasicDataDictSubDTO;
import com.apec.datadictsub.dto.BasicDataDictSubQueryDTO;
import com.apec.framework.common.exception.ApecRuntimeException;
import com.apec.framework.common.model.ResultData;
import com.apec.selectlist.dto.BasicSelectListCacheDTO;

public interface MgtDataDictRpcService {
    /**
     * @param param
     * @param userId 用户编号
     * @throws ApecRuntimeException
     * <AUTHOR>
     * @date 2019-01-23 03:29:12
     */
    ResultData rpcFlushCacheBasicDataDictSubJob(BasicDataDictSubDTO param, String userId)
            throws ApecRuntimeException;

    /**
     * @param param
     * @param userId 用户编号
     * @throws ApecRuntimeException
     * <AUTHOR>
     * @date 2019-01-23 03:29:12
     */
    ResultData rpcFlushCacheBasicDataDictJob(BasicDataDictDTO param, String userId)
            throws ApecRuntimeException;

    /**
     * 刷新管理选择项数据
     * @param param
     * @param userId
     * @return
     * @throws ApecRuntimeException
     */
    ResultData rpcFlushSelectListCache(BasicSelectListCacheDTO param, String userId)
            throws ApecRuntimeException;
    /**
     * 完善数据字典数据
     * @param param
     * @param userId
     * @throws ApecRuntimeException
     */
    void addDataDictSubImgInPicPool(String methodType, BasicDataDictSubDTO param, String userId)
        throws ApecRuntimeException;

    /**
     * 完善数据字典数据
     * @param param
     * @param userId
     * @throws ApecRuntimeException
     */
    void addDataDictImgInPicPool(String methodType, BasicDataDictDTO param, String userId)
            throws ApecRuntimeException;

    ResultData rpcFindListExtBasicDataDictSubList(BasicDataDictSubQueryDTO param, String userId)
            throws ApecRuntimeException;
}
