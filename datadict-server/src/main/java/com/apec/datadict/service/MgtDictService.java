package com.apec.datadict.service;

import com.apec.datadict.dto.BasicDataDictDTO;
import com.apec.datadict.dto.ExecPushDataDictReqDTO;
import com.apec.datadict.dto.ExecPushDataDictResDTO;
import com.apec.datadict.dto.PushDataDictToTargetEnvDTO;
import com.apec.datadictsub.dto.BasicDataDictSubDTO;
import com.apec.framework.common.exception.ApecRuntimeException;
import com.apec.framework.common.model.ResultData;

import java.util.List;
/**
 * 类 编 号：BL.datadict.Service.MgtDictService
 * 类 名 称：MgtDictService
 * 内容摘要：数据字典服务
 * <AUTHOR>
 * @date 2020-04-23 18:35:33
 */
public interface MgtDictService {

    /**
     *
     * @param oecdNo
     * @param dictCodeList
     * @param userId
     * @return
     */
    List<BasicDataDictDTO> queryDataDictList(String oecdNo, List<String> dictCodeList, String userId);

    /**
     *
     * @param oecdNo
     * @param dictCodeList
     * @param userId
     * @return
     */
    List<BasicDataDictSubDTO> queryDataDictSubList(String oecdNo, List<String> dictCodeList, String userId);
    List<BasicDataDictSubDTO> queryDataDictSubList(String oecdNo, String dictCode, List<String> dictCodeSubList, String userId);

    /**
     * 推送数据字典到目标环境
     * @param param
     * @return
     * @throws ApecRuntimeException
     */
    ResultData pushDataDictToTargetEnv(PushDataDictToTargetEnvDTO param, String userId)
            throws ApecRuntimeException;

    /**
     * 处理推送过来的数据字典
     * @param para
     * @return
     * @throws ApecRuntimeException
     */
    ExecPushDataDictResDTO execPushDict(ExecPushDataDictReqDTO para, String userId)
            throws ApecRuntimeException;
}
