package com.apec.datadict.service;

import com.alibaba.fastjson.JSONObject;
import com.apec.datadictsub.dto.BasicDataDictSubDTO;
import com.apec.framework.common.exception.ApecRuntimeException;
import com.apec.datadict.dto.DictItemSelectListQueryDTO;
import com.apec.datadict.dto.QueryNormalTradeModeDTO;
import com.apec.magpie.cb.dto.CommonItemDTO;
import com.apec.magpie.cb.dto.DataDictSysConfigDTO;

import java.util.List;

/**
 * 类 编 号：
 * 类 名 称：FacadeBaseDataDictService
 * 内容摘要：微服务基础接口（外部服务调用）
 * 创建日期：2018/08/09
 * <AUTHOR>
 */
public interface FacadeDictSubService {

    /**
     * 根据关系查询当前可用的交易模式
     * 属性: subsystemCodings  "子系统编码组，用逗号分割"；('trade','FS','LX')
     * 来自枚举EnumTradeModelCode   根据需要自行拼接
     * 现在只有LX("LX", "现货延期系统"),FS("FS", "现货订单系统"),TRADE("trade","现货供销系统")可以查数据
     * 属性: tradingModeCodings "子交易模式组，用逗号分隔"；('1','2','3')
     * 来自枚举EnumTradeModelCode   根据需要自行拼接
     * @param queryNormalTradeModeDTO
     * @return
     * @Service(functionId = "300592", desc = "根据关系查询当前可用的交易模式")
     */
    List<BasicDataDictSubDTO> queryNormalTradeMode(QueryNormalTradeModeDTO queryNormalTradeModeDTO)
        throws  ApecRuntimeException;
    /**
     * 验证系统状态是否可用
     * @param systemId
     * @Service(functionId = "313803", desc = "验证系统状态是否可用")
     */

    Boolean whetherNormalOFSystem(String systemId)
            throws  ApecRuntimeException;

    /**
     * 查询状态的数据字典子项列表
     * @param param
     * @return
     * @throws ApecRuntimeException
     */
    JSONObject queryDataDictSubListKV(DictItemSelectListQueryDTO param)
            throws  ApecRuntimeException;

    DataDictSysConfigDTO updateDataDictSysConfig(DataDictSysConfigDTO param, String userId)
            throws  ApecRuntimeException;
    DataDictSysConfigDTO queryDataDictSysConfig(DataDictSysConfigDTO param, String userId)
            throws  ApecRuntimeException;



}
