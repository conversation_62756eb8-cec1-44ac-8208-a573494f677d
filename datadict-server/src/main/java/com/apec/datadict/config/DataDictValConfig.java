package com.apec.datadict.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Data
@Component
public class DataDictValConfig {

    @Value("${rpc.datadict.mgtflush.flushDictAllCacheJob}")
    private String rpcDataDictMgtflushFlushDictAllCacheJob;

    @Value("${rpc.datadict.mgtflush.flushDictSubCache}")
    private String rpcDataDictMgtflushFlushDictSubCache;

    @Value("${rpc.datadict.mgtflush.flushDictCache}")
    private String rpcDataDictMgtflushFlushDictCache;

    @Value("${rpc.datadict.facadebasicSelectListCache.flushCacheJob}")
    private String rpcDataDictFacadebasicSelectListCacheFlushCacheJob;


    @Value("${rpc.datadict.basicDataDictSub.findListExt}")
    private String rpcDataDictBasicDataDictSubFindListExt;


    @Value("${open.push.center.datadict.maxSize: 50}")
    private Integer pushDataDictMaxSize;

}
