package com.apec.datadict.aop;

import com.apec.datadict.service.MgtDataDictRpcService;
import com.apec.framework.common.constant.ErrorCodeConsts;
import com.apec.framework.common.enumtype.EnableFlag;
import com.apec.framework.common.exception.ApecRuntimeException;
import com.apec.framework.common.util.BeanUtils;
import com.apec.framework.common.util.JsonUtils;
import com.apec.magpie.cb.constant.CommonConts;
import com.apec.magpie.cb.constant.IContsDB;
import com.apec.magpie.cb.constant.IErrorCfg;
import com.apec.magpie.cb.enums.EnumIsStatus;
import com.apec.magpie.cb.enums.EnumServiceMethod;
import com.apec.magpie.cb.enums.marketInfo.EnumClassificationType;
import com.apec.selectlist.constants.BasicSelectListCacheContant;
import com.apec.selectlist.dto.BasicSelectListCacheDTO;
import com.apec.selectlist.dto.BasicSelectListCacheQueryDTO;
import com.apec.selectlist.model.BasicSelectListCache;
import com.apec.selectlist.service.BasicSelectListCacheService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 类 名 称：
 * 内容摘要：
 */
@Aspect
@Component
public class ServiceSelectListCacheAspect implements Ordered,CommonConts, IContsDB, ErrorCodeConsts, IErrorCfg, BasicSelectListCacheContant {

    private static final Logger LOG = LoggerFactory.getLogger(ServiceSelectListCacheAspect.class);

    @Autowired
    @Lazy
    private BasicSelectListCacheService modelService;
    @Autowired
    private MgtDataDictRpcService mgtRpcService;

    @Pointcut("execution(* com.apec.selectlist.service.BasicSelectListCacheService.save(..))")
    public void saveModel() {
    }
    @Pointcut("execution(* com.apec.selectlist.service.BasicSelectListCacheService.update(..))")
    public void updateModel() {
    }

    @Pointcut("execution(* com.apec.selectlist.service.BasicSelectListCacheService.updateDTO(..))")
    public void updateDTOModel() {
    }
    @Pointcut("execution(* com.apec.selectlist.service.BasicSelectListCacheService.delete(..))")
    public void deleteModel() {
    }

    @Around("saveModel()")
    public Object doSaveAround(ProceedingJoinPoint joinPoint)
            throws Throwable
    {
        Object[] args = joinPoint.getArgs();
        BasicSelectListCacheDTO param = JsonUtils.parseObject(args[ZERO].toString(), BasicSelectListCacheDTO.class);
        String userId = null == args[ONE] ? "joinPoint" : args[ONE].toString();

        BasicSelectListCacheDTO updateObject = validateAdd(param,userId);
        args[ZERO]= updateObject;
        args[ONE]  = userId;
        return joinPoint.proceed(args);
    }

    @Around("updateModel()")
    public Object doUpdateAround(ProceedingJoinPoint joinPoint)
            throws Throwable
    {
        Object[] args = joinPoint.getArgs();
        BasicSelectListCacheDTO param = JsonUtils.parseObject(args[ZERO].toString(),BasicSelectListCacheDTO.class);
        String userId = null == args[ONE] ? "joinPoint" : args[ONE].toString();
        BasicSelectListCacheDTO updateObject = validateUpdate(param,userId);
        BasicSelectListCache updatePo= new BasicSelectListCache();
        BeanUtils.copyPropertiesIgnoreNullFilds(updateObject,updatePo);
        args[ZERO]= updatePo;
        args[ONE]  = userId;
        return joinPoint.proceed(args);
    }

    @Around("updateDTOModel()")
    public Object doUpdateDTOAround(ProceedingJoinPoint joinPoint)
            throws Throwable
    {
        Object[] args = joinPoint.getArgs();
        BasicSelectListCacheDTO param = JsonUtils.parseObject(args[ZERO].toString(),BasicSelectListCacheDTO.class);
        String userId = null == args[ONE] ? "joinPoint" : args[ONE].toString();
        BasicSelectListCacheDTO updateObject = validateUpdate(param,userId);
        args[ZERO]= updateObject;
        args[ONE]  = userId;
        return joinPoint.proceed(args);
    }

    @Around("deleteModel()")
    public Object doDeleteAround(ProceedingJoinPoint joinPoint)
            throws Throwable
    {
        Object[] args = joinPoint.getArgs();
        String userId = null == args[ONE] ? "joinPoint" : args[ONE].toString();
        validateDelete(args[ZERO].toString(),userId);
        return joinPoint.proceed(args);
    }


    @AfterReturning(value = "saveModel() || updateModel() || updateDTOModel() || deleteModel()",returning="result")
    public void afterReturning(JoinPoint point, Object result){
        String methodName = point.getSignature().getName();
        List<Object> args = Arrays.asList(point.getArgs());
        if(LOG.isDebugEnabled()) {
            LOG.debug("连接点方法为：" + methodName + ",参数为：" + args + ",目标方法执行结果为：" + result);
        }

        String id = "";
        if(EnumServiceMethod.SAVE.getCode().equals(methodName)
                || EnumServiceMethod.UPDATE.getCode().equals(methodName)
                || EnumServiceMethod.UPDATE_DTO.getCode().equals(methodName)) {
            BasicSelectListCacheDTO param = JsonUtils.parseObject(point.getArgs()[ZERO].toString(),BasicSelectListCacheDTO.class);
            if(null != param){
                id = param.getId();
            }
        }
        else if(EnumServiceMethod.DELETE.getCode().equals(methodName)){
            id = point.getArgs()[ZERO].toString();
        }

        if(StringUtils.isNotBlank(id)){
            BasicSelectListCache one =  modelService.find(id,args.get(ONE).toString());
            if(null != one){
                mgtRpcService.rpcFlushSelectListCache(modelService.convertPoToDto(one), args.get(ONE).toString());
            }
        }else{
            LOG.error("获取{}的id失败,rs:{}",methodName,JsonUtils.toJSONString(result));
        }
    }

    /**
     * 验证修改
     * @param param
     * @param userId
     * @return
     * @throws ApecRuntimeException
     */
    private BasicSelectListCacheDTO validateUpdate(BasicSelectListCacheDTO param, String userId)
            throws ApecRuntimeException
    {
        checkParam(param);

        if(StringUtils.isBlank(param.getId())){
            throw new ApecRuntimeException(ERROR_CB_LOST_PARAM,
                    new String[]{
                            Arrays.asList(
                                    IContsDB.DB_COL_NAME_ID
                            ).toString()
                    }
            );
        }
        BasicSelectListCacheDTO dbDTO = modelService.findDTO(param.getId(),userId);
        if(null == dbDTO){
            LOG.error("query BasicSelectListCacheDTO fail,id:{}",param.getId());
            throw new ApecRuntimeException(ERROR_CB_QUERY_FAIL_DATA_NOT_EXIST_BY_ID);
        }
        if(!(param.getOecdNo().equals(dbDTO.getOecdNo()) && param.getPlantformId().equals(dbDTO.getPlantformId())
                && param.getSelectTypeCode().equals(dbDTO.getSelectTypeCode())
        )){
            LOG.error("BasicSelectListCacheDTO key data cant update,param:{},db:{}",JsonUtils.toJSONString(param),JsonUtils.toJSONString(dbDTO));
            throw new ApecRuntimeException(ERROR_CB_KEY_DATA_CANT_UPDATE);
        }

        BasicSelectListCacheQueryDTO query = new BasicSelectListCacheQueryDTO();
        query.setOecdNo(param.getOecdNo());
        query.setPlantformId(param.getPlantformId());
        query.setEnableFlag(EnableFlag.Y);
        List<BasicSelectListCacheDTO> list = modelService.findListExt(query, userId);

        BasicSelectListCacheDTO finalParam = param;
        List<BasicSelectListCacheDTO> listEQ = list.stream().filter(e -> (
                        (
                                e.getSelectTypeCode().equals(finalParam.getSelectTypeCode()) || 
                                e.getSelectTypeName().equals(finalParam.getSelectTypeName())
                        ) 
                        && 
                        !e.getId().equals(finalParam.getId())
                )
        ).collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(listEQ)) {
            LOG.error("config data exist,db val:{}",JsonUtils.toJSONString(list.get(0)));
            throw new ApecRuntimeException(ERROR_CB_CFG_DATA_EXIST);
        }


        param = replenishRefData(param,userId);

        return param;
    }

    /**
     * 检查基本参数
     * @param param
     */
    private void checkParam(BasicSelectListCacheDTO param){
        if(null == param){
            throw new ApecRuntimeException(ERROR_CB_PARAM_IS_NULL);
        }
        if(StringUtils.isAnyBlank(
                param.getOecdNo(),
                param.getPlantformId(),
                param.getSelectTypeCode(),
                param.getSelectTypeName(),
                param.getServiceName(),
                param.getExtQueryPath()
        )) {
            LOG.error("lost param,param:{}",JsonUtils.toJSONString(param));
            throw new ApecRuntimeException(ERROR_CB_LOST_PARAM,
                    new String[]{
                            Arrays.asList(
                                    IContsDB.DB_ATTR_NAME_OECD_NO,
                                    IContsDB.DB_ATTR_NAME_PLANTFORM_ID,
                                    C_BASICSELECTLISTCACHE_ATTRNAME_SELECTTYPECODE,
                                    C_BASICSELECTLISTCACHE_ATTRNAME_SELECTTYPENAME,
                                    C_BASICSELECTLISTCACHE_ATTRNAME_SERVICENAME,
                                    C_BASICSELECTLISTCACHE_ATTRNAME_EXTQUERYPATH
                            ).toString()
                    }
            );
        }
//        BeanValidator.check(param);
    }

    /**
     * 验证新增
     * @param param
     * @param userId
     * @return
     */
    private BasicSelectListCacheDTO validateAdd(BasicSelectListCacheDTO param,String userId)
    {
        checkParam(param);
        //设置默认缺省值
        if(StringUtils.isBlank(param.getOecdNo())){
            param.setOecdNo(CommonConts.DEFAULT_OECD_NO_APEC);
        }
        if(StringUtils.isBlank(param.getPlantformId())){
            param.setPlantformId(CommonConts.DEFAULT_PLANTFORM_ID_DAZONG);
        }

        BasicSelectListCacheQueryDTO query = new BasicSelectListCacheQueryDTO();
        query.setOecdNo(param.getOecdNo());
        query.setPlantformId(param.getPlantformId());
        query.setEnableFlag(EnableFlag.Y);
        List<BasicSelectListCacheDTO> list = modelService.findListExt(query, userId);

        BasicSelectListCacheDTO finalParam = param;
        List<BasicSelectListCacheDTO> listEQ = list.stream().filter(e -> (e.getSelectTypeCode().equals(finalParam.getSelectTypeCode()) || e.getSelectTypeName().equals(finalParam.getSelectTypeName())))
                .collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(listEQ)) {
            LOG.error("config data exist,db val:{}",JsonUtils.toJSONString(list.get(0)));
            throw new ApecRuntimeException(ERROR_CB_CFG_DATA_EXIST);
        }

        param = replenishRefData(param,userId);

        if(StringUtils.isBlank(param.getStatus())) {
            param.setStatus(EnumIsStatus.YES.getCode());
        }
        param.setEnableFlag(EnableFlag.Y);
        return param;
    }


    /**
     * 完善数据
     * @param param
     * @param userId
     * @return
     * @throws ApecRuntimeException
     */
    private BasicSelectListCacheDTO replenishRefData(BasicSelectListCacheDTO param, String userId)
            throws ApecRuntimeException
    {
        return param;
    }

    private void validateDelete(String id, String userId)
            throws ApecRuntimeException
    {
        if(StringUtils.isBlank(id)){
            throw new ApecRuntimeException(ERROR_QUERY_DATA_NOT_EXIST_BY_ID);
        }

        BasicSelectListCacheDTO dbDTO = modelService.findDTO(id,userId);
        if(null == dbDTO){
            throw new ApecRuntimeException(ERROR_QUERY_DATA_NOT_EXIST_BY_ID);
        }
    }

    @Override
    public int getOrder() {
        return 2002;
    }


}
