package com.apec.datadict.aop;


import com.apec.datadict.common.DataDictConts;
import com.apec.datadict.service.MgtDataDictRpcService;
import com.apec.datadict.dto.BasicDataDictDTO;
import com.apec.datadict.dto.BasicDataDictQueryDTO;
import com.apec.datadict.model.BasicDataDict;
import com.apec.datadict.service.BasicDataDictService;
import com.apec.datadictsub.dto.BasicDataDictSubDTO;
import com.apec.datadictsub.dto.BasicDataDictSubQueryDTO;
import com.apec.framework.common.dto.PageDTO;
import com.apec.framework.common.enumtype.EnableFlag;
import com.apec.framework.common.exception.ApecRuntimeException;
import com.apec.framework.common.model.ResultData;
import com.apec.framework.common.util.BeanUtils;
import com.apec.framework.common.util.JsonUtils;
import com.apec.magpie.cb.common.utils.SnowFlakeKeyGen;
import com.apec.magpie.cb.constant.CommonConts;
import com.apec.magpie.cb.enums.EnumIsStatus;
import com.apec.magpie.cb.enums.EnumServiceMethod;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 类 编 号：BL.AOP.Service.ServiceDataDictAspect
 * 类 名 称：ServiceAttrAspect
 * 内容摘要：服务层拦截器
 * <AUTHOR>
 * @date 2020-04-23 15:11:12
 */
@Aspect
@Component
public class ServiceDataDictAspect implements Ordered, DataDictConts {

    private static final Logger LOG = LoggerFactory.getLogger(ServiceDataDictAspect.class);

    @Autowired
    @Lazy
    private BasicDataDictService modelService;
    @Autowired
    private MgtDataDictRpcService mgtRpcService;

    @Pointcut("execution(* com.apec.datadict.service.BasicDataDictService.save(..))")
    public void saveModel() {
    }

    @Pointcut("execution(* com.apec.datadict.service.BasicDataDictService.update(..))")
    public void updateModel() {
    }

    @Pointcut("execution(* com.apec.datadict.service.BasicDataDictService.updateDTO(..))")
    public void updateDTOModel() {
    }

    @Pointcut("execution(* com.apec.datadict.service.BasicDataDictService.updateStatus(..))")
    public void updateStatusModel() {
    }

    @Pointcut("execution(* com.apec.datadict.service.BasicDataDictService.delete(..))")
    public void deleteModel() {
    }

    @Pointcut("execution(* com.apec.datadict.service.BasicDataDictService.findListExt(..))")
    public void findListExtModel() {
    }

    @Pointcut("execution(* com.apec.datadict.service.BasicDataDictService.findByPageExt(..))")
    public void findByPageExtModel() {
    }

    @Around("saveModel()")
    public Object doSaveModelAround(ProceedingJoinPoint joinPoint)
            throws Throwable
    {
        Object[] args = joinPoint.getArgs();
        BasicDataDictDTO param = JsonUtils.parseObject(args[ZERO].toString(), BasicDataDictDTO.class);
        String userId = null == args[ONE] ? "joinPoint" : args[ONE].toString();

        BasicDataDictDTO updateObject = validateAdd(param,userId);
        args[ZERO]= updateObject;
        args[ONE]  = userId;
        return joinPoint.proceed(args);
    }

    @Around("updateDTOModel()")
    public Object doUpdateDTOModelAround(ProceedingJoinPoint joinPoint)
            throws Throwable
    {
        Object[] args = joinPoint.getArgs();
        BasicDataDictDTO param = JsonUtils.parseObject(args[ZERO].toString(),BasicDataDictDTO.class);
        String userId = null == args[ONE] ? "joinPoint" : args[ONE].toString();
        BasicDataDictDTO updateObject = validateUpdate(param,userId);
        args[ZERO]= updateObject;
        args[ONE]  = userId;
        return joinPoint.proceed(args);
    }

    @Around("updateModel()")
    public Object doUpdateModelAround(ProceedingJoinPoint joinPoint)
            throws Throwable
    {
        Object[] args = joinPoint.getArgs();
        BasicDataDictDTO param = JsonUtils.parseObject(args[ZERO].toString(),BasicDataDictDTO.class);
        String userId = null == args[ONE] ? "joinPoint" : args[ONE].toString();
        BasicDataDictDTO updateObject = validateUpdate(param,userId);
        BasicDataDict updatePo= new BasicDataDict();
        BeanUtils.copyPropertiesIgnoreNullFilds(updateObject,updatePo);
        args[ZERO]= updatePo;
        args[ONE]  = userId;
        return joinPoint.proceed(args);
    }

    @Around("deleteModel()")
    public Object doDeleteModelAround(ProceedingJoinPoint joinPoint)
            throws Throwable
    {
        Object[] args = joinPoint.getArgs();
        String userId = null == args[ONE] ? "joinPoint" : args[ONE].toString();
        validateDelete(args[ZERO].toString(),userId);
        return joinPoint.proceed(args);
    }

    private void validateDelete(String id, String userNo)
            throws ApecRuntimeException
    {
        if(StringUtils.isBlank(id)){
            throw new ApecRuntimeException(ERROR_CB_LOST_PARAM,new String[]{DB_COL_NAME_ID});
        }

        BasicDataDictDTO dbDTO = modelService.findDTO(id,userNo);
        if(null == dbDTO){
            LOG.error("query BasicClsExternalCodeDTO fail,id:{}",dbDTO.getId());
            throw new ApecRuntimeException(ERROR_CB_QUERY_FAIL_DATA_NOT_EXIST_BY_ID, Arrays.asList("openPath",dbDTO.getId()));
        }
    }


    @Around("findListExtModel()")
    public Object doAroundFindListExt(ProceedingJoinPoint joinPoint)
            throws Throwable
    {
        Object[] args = joinPoint.getArgs();
        String userId = null == args[ONE] ? "joinPoint" : args[ONE].toString();
        Object object = joinPoint.proceed(args);
        return completeBasicDataDictListRs((List<BasicDataDictDTO>) object, userId);
    }
    @Around("findByPageExtModel()")
    public Object doAroundFindByPageExt(ProceedingJoinPoint joinPoint)
            throws Throwable
    {
        Object[] args = joinPoint.getArgs();
        String userId = null == args[ONE] ? "joinPoint" : args[ONE].toString();
        Object object = joinPoint.proceed(args);
        PageDTO<BasicDataDictDTO> rsPage = (PageDTO<BasicDataDictDTO>)object;
        List<BasicDataDictDTO> rowList = completeBasicDataDictListRs(rsPage.getRows(), userId);
        rsPage.setRows(rowList);
        return rsPage;
    }


    @AfterReturning(value = "saveModel() || updateModel() || updateDTOModel() || updateStatusModel() || deleteModel()",returning="result")
    public void afterReturning(JoinPoint point, Object result){
        String methodName = point.getSignature().getName();
        List<Object> args = Arrays.asList(point.getArgs());
        if(LOG.isDebugEnabled()) {
            LOG.debug("连接点方法为：" + methodName + ",参数为：" + args + ",目标方法执行结果为：" + result);
        }

        String id = "";
        if(EnumServiceMethod.SAVE.getCode().equals(methodName)
                || EnumServiceMethod.UPDATE.getCode().equals(methodName)
                || EnumServiceMethod.UPDATE_DTO.getCode().equals(methodName)) {
            BasicDataDictDTO param = JsonUtils.parseObject(point.getArgs()[ZERO].toString(),BasicDataDictDTO.class);
            if(null != param){
                id = param.getId();
            }
        }
        else if(EnumServiceMethod.DELETE.getCode().equals(methodName) || EnumServiceMethod.UPDATE_STATUS.getCode().equals(methodName)){
            id = point.getArgs()[ZERO].toString();
        }

        if(StringUtils.isNotBlank(id)){
            BasicDataDict one =  modelService.find(id,args.get(ONE).toString());
            if(null != one){
                BasicDataDictDTO keyParam = new BasicDataDictDTO();
                keyParam.setId(id);
                keyParam.setOecdNo(one.getOecdNo());
                keyParam.setPlantformId(one.getPlantformId());
                keyParam.setDictCode(one.getDictCode());
                mgtRpcService.rpcFlushCacheBasicDataDictJob(keyParam, args.get(ONE).toString());
            }
            //新增或更新图片
            if(EnumServiceMethod.SAVE.getCode().equals(methodName)
                    || EnumServiceMethod.UPDATE.getCode().equals(methodName)
                    || EnumServiceMethod.UPDATE_DTO.getCode().equals(methodName)){
                mgtRpcService.addDataDictImgInPicPool(methodName, modelService.convertPoToDto(one), args.get(ONE).toString());
            }

        }else{
            LOG.error("获取{}的id失败,rs:{}",methodName, JsonUtils.toJSONString(result));
        }
    }

    /**
     * 验证修改
     * @param param
     * @param userNo
     * @return
     * @throws ApecRuntimeException
     */
    private BasicDataDictDTO validateUpdate(BasicDataDictDTO param,String userNo)
            throws ApecRuntimeException
    {
        checkParam(param);

        if(StringUtils.isBlank(param.getId())){
            throw new ApecRuntimeException(ERROR_CB_LOST_PARAM,new String[]{DB_COL_NAME_ID});
        }
        BasicDataDictDTO dbDTO = modelService.findDTO(param.getId(),userNo);
        if(null == dbDTO){
            LOG.error("query BasicDataDictDTO fail,id:{}",param.getId());
            throw new ApecRuntimeException(ERROR_CB_QUERY_FAIL_DATA_NOT_EXIST_BY_ID, Arrays.asList(M_EXTERNALCODE,param.getId()));
        }
//        if(!(param.getOecdNo().equals(dbDTO.getOecdNo()) && param.getPlantformId().equals(dbDTO.getPlantformId()) )){
//            LOG.error("BasicDataDictDTO key data cant update,param:{},db:{}", JsonUtils.toJSONString(param), JsonUtils.toJSONString(dbDTO));
//            throw new ApecRuntimeException(ERROR_CB_KEY_DATA_CANT_UPDATE, new String[]{ "oecdNo  plantformId" });
//        }
        if(!(param.getDictCode().equals(dbDTO.getDictCode()))){
            LOG.error("BasicDataDictDTO key data cant update,param:{},db:{}", JsonUtils.toJSONString(param), JsonUtils.toJSONString(dbDTO));
            throw new ApecRuntimeException(ERROR_CB_KEY_DATA_CANT_UPDATE, new String[]{ "dictCode" });
        }
        if(StringUtils.isNotBlank(param.getImg()) && !param.getImg().equals(dbDTO.getImg())){
            param.setImgId(SnowFlakeKeyGen.nextIdStr());
        }
        return param;
    }

    /**
     * 检查基本参数
     * @param param
     */
    private void checkParam(BasicDataDictDTO param){
        if(null == param){
            throw new ApecRuntimeException(ERROR_CB_PARAM_IS_NULL);
        }
        if(StringUtils.isAnyBlank(
//                param.getOecdNo(),param.getPlantformId(),
                param.getDictCode(), param.getDictCode(),
                param.getDictValue(),param.getDictEnValue()
                )){
            LOG.error("lost param,param:{}", JsonUtils.toJSONString(param));
            throw new ApecRuntimeException(ERROR_CB_LOST_PARAM,
                    new String[]{
                            Arrays.asList(
//                                    DB_ATTR_NAME_OECD_NO,
//                                    DB_ATTR_NAME_PLANTFORM_ID,
                                    DB_COL_NAME_ID,
                                    C_BASICDATADICT_ATTRNAME_DICTCODE,
                                    C_BASICDATADICT_ATTRNAME_DICTCODE,
                                    C_BASICDATADICT_ATTRNAME_DICTVALUE,
                                    C_BASICDATADICT_ATTRNAME_DICTENVALUE
                            ).toString()
                    }
            );
        }

//        BeanValidator.check(param);
    }

    /**
     * 验证新增
     * @param param
     * @param userNo
     * @return
     */
    private BasicDataDictDTO validateAdd(BasicDataDictDTO param,String userNo)
    {
        checkParam(param);
        //设置默认缺省值
        if(StringUtils.isBlank(param.getOecdNo())){
            param.setOecdNo(CommonConts.DEFAULT_OECD_NO_APEC);
        }
        if(StringUtils.isBlank(param.getPlantformId())){
            param.setPlantformId(CommonConts.DEFAULT_PLANTFORM_ID_DAZONG);
        }

        BasicDataDictQueryDTO query = new BasicDataDictQueryDTO();
        query.setEnableFlag(EnableFlag.Y);
        query.setOecdNo(param.getOecdNo());
        query.setPlantformId(param.getPlantformId());

        query.setDictCode(param.getDictCode());

        //启用与禁用
        List<BasicDataDictDTO> list = modelService.findListExt(query, userNo);

        if(CollectionUtils.isNotEmpty(list)){
            LOG.error("config data exist,db val:{}", JsonUtils.toJSONString(list.get(0)));
            throw new ApecRuntimeException(ERROR_CB_CFG_DATA_EXIST);
        }

        if(StringUtils.isBlank(param.getStatus())) {
            param.setStatus(EnumIsStatus.YES.getCode());
        }
        if(StringUtils.isNotBlank(param.getImg())){
            param.setImgId(SnowFlakeKeyGen.nextIdStr());
        }
        param.setDictCode(param.getDictCode());
        param.setEnableFlag(EnableFlag.Y);
        return param;
    }


    private  List<BasicDataDictDTO> completeBasicDataDictListRs(List<BasicDataDictDTO> listDict, String userId) {
        if(CollectionUtils.isEmpty(listDict)){
            return Collections.emptyList();
        }
        List<String> dictCodeList = listDict.stream().map(e -> e.getDictCode()).collect(Collectors.toList());
        List<BasicDataDictSubDTO> dataDictSubList = queryBasicDataDictSubList(listDict.get(ZERO).getOecdNo(), dictCodeList, userId);
        Map<String,List<BasicDataDictSubDTO>> dictSubListMap = dataDictSubList.stream().collect(Collectors.groupingBy(e -> e.getDictCode()));

        return  listDict.stream().map( oneDictDb -> {
            if(dictSubListMap.containsKey(oneDictDb.getDictCode())) {
                oneDictDb.setDictSubList(
                        dictSubListMap.get(oneDictDb.getDictCode()).stream().map(oneDictSub -> {
                            if(null == oneDictSub.getOrderNumber()){
                                oneDictSub.setOrderNumber(ZERO);
                            }
                            return oneDictSub;
                        }).sorted(Comparator.comparing(BasicDataDictSubDTO::getOrderNumber))
                                .collect(Collectors.toList()));
            }
            else {
                oneDictDb.setDictSubList(Collections.emptyList());
            }
            return oneDictDb;
        }).collect(Collectors.toList());
    }

    private List<BasicDataDictSubDTO> queryBasicDataDictSubList(String oeceNo, List<String> dictCodeList, String userId) {
        BasicDataDictSubQueryDTO query = new BasicDataDictSubQueryDTO();
        query.setOecdNo(oeceNo);
        query.setEnableFlag(EnableFlag.Y);
        query.setDictCodeLS(dictCodeList);
        ResultData resultData = mgtRpcService.rpcFindListExtBasicDataDictSubList(query, userId);
        if(resultData.isSucceed() && null != resultData.getData() && StringUtils.isNotBlank(resultData.getData().toString())){
            try{
                return JsonUtils.parseArray(resultData.getData().toString(), BasicDataDictSubDTO.class);
            }catch (Exception e){
                LOG.error(e.getMessage(), e);
            }
        }
        return Collections.emptyList();
    }

    @Override
    public int getOrder() {
        return 2001;
    }

}
