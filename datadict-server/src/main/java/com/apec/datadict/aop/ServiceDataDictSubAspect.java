package com.apec.datadict.aop;


import com.apec.datadictsub.constants.BasicDataDictSubContant;
import com.apec.datadictsub.dto.BasicDataDictSubDTO;
import com.apec.datadictsub.dto.BasicDataDictSubQueryDTO;
import com.apec.datadictsub.model.BasicDataDictSub;
import com.apec.datadictsub.service.BasicDataDictSubService;
import com.apec.framework.common.constant.ErrorCodeConsts;
import com.apec.framework.common.enumtype.EnableFlag;
import com.apec.framework.common.exception.ApecRuntimeException;
import com.apec.framework.common.util.BeanUtils;
import com.apec.framework.common.util.JsonUtils;
import com.apec.magpie.cb.common.utils.SnowFlakeKeyGen;
import com.apec.magpie.cb.constant.CommonConts;
import com.apec.magpie.cb.constant.IContsDB;
import com.apec.magpie.cb.constant.IErrorCfg;
import com.apec.magpie.cb.enums.EnumIsStatus;
import com.apec.magpie.cb.enums.EnumServiceMethod;
import com.apec.datadict.service.MgtDataDictRpcService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 内容摘要：
 */
@Aspect
@Component
public class ServiceDataDictSubAspect implements Ordered, CommonConts, IContsDB, ErrorCodeConsts, IErrorCfg, BasicDataDictSubContant {

    private static final Logger LOG = LoggerFactory.getLogger(ServiceDataDictSubAspect.class);

    @Autowired
    @Lazy
    private BasicDataDictSubService modelService;
    @Autowired
    private MgtDataDictRpcService mgtRpcService;

    @Pointcut("execution(* com.apec.datadictsub.service.BasicDataDictSubService.save(..))")
    public void saveModel() {
    }

    @Pointcut("execution(* com.apec.datadictsub.service.BasicDataDictSubService.update(..))")
    public void updateModel() {
    }

    @Pointcut("execution(* com.apec.datadictsub.service.BasicDataDictSubService.updateDTO(..))")
    public void updateDTOModel() {
    }

    @Pointcut("execution(* com.apec.datadictsub.service.BasicDataDictSubService.updateStatus(..))")
    public void updateStatusModel() {
    }

    @Pointcut("execution(* com.apec.datadictsub.service.BasicDataDictSubService.delete(..))")
    public void deleteModel() {
    }


    @Around("saveModel()")
    public Object doSaveModelAround(ProceedingJoinPoint joinPoint)
            throws Throwable
    {
        Object[] args = joinPoint.getArgs();
        BasicDataDictSubDTO param = JsonUtils.parseObject(args[ZERO].toString(), BasicDataDictSubDTO.class);
        String userId = null == args[ONE] ? "joinPoint" : args[ONE].toString();

        BasicDataDictSubDTO updateObject = validateAdd(param,userId);
        args[ZERO]= updateObject;
        args[ONE]  = userId;
        return joinPoint.proceed(args);
    }

    @Around("updateDTOModel()")
    public Object doUpdateDTOModelAround(ProceedingJoinPoint joinPoint)
            throws Throwable
    {
        Object[] args = joinPoint.getArgs();
        BasicDataDictSubDTO param = JsonUtils.parseObject(args[ZERO].toString(),BasicDataDictSubDTO.class);
        String userId = null == args[ONE] ? "joinPoint" : args[ONE].toString();
        BasicDataDictSubDTO updateObject = validateUpdate(param,userId);
        args[ZERO]= updateObject;
        args[ONE]  = userId;
        return joinPoint.proceed(args);
    }

    @Around("updateModel()")
    public Object doUpdateModelAround(ProceedingJoinPoint joinPoint)
            throws Throwable
    {
        Object[] args = joinPoint.getArgs();
        BasicDataDictSubDTO param = JsonUtils.parseObject(args[ZERO].toString(),BasicDataDictSubDTO.class);
        String userId = null == args[ONE] ? "joinPoint" : args[ONE].toString();
        BasicDataDictSubDTO updateObject = validateUpdate(param,userId);
        BasicDataDictSub updatePo= new BasicDataDictSub();
        BeanUtils.copyPropertiesIgnoreNullFilds(updateObject,updatePo);
        args[ZERO]= updatePo;
        args[ONE]  = userId;
        return joinPoint.proceed(args);
    }

    @Around("deleteModel()")
    public Object doDeleteModelAround(ProceedingJoinPoint joinPoint)
            throws Throwable
    {
        Object[] args = joinPoint.getArgs();
        String userId = null == args[ONE] ? "joinPoint" : args[ONE].toString();
        validateDelete(args[ZERO].toString(),userId);
        return joinPoint.proceed(args);
    }

    private void validateDelete(String id, String userNo)
            throws ApecRuntimeException
    {
        if(StringUtils.isBlank(id)){
            throw new ApecRuntimeException(ERROR_CB_LOST_PARAM,new String[]{DB_COL_NAME_ID});
        }

        BasicDataDictSubDTO dbDTO = modelService.findDTO(id,userNo);
        if(null == dbDTO){
            LOG.error("query BasicClsExternalCodeDTO fail,id:{}",dbDTO.getId());
            throw new ApecRuntimeException(ERROR_CB_QUERY_FAIL_DATA_NOT_EXIST_BY_ID, Arrays.asList("openPath",dbDTO.getId()));
        }
    }


    @AfterReturning(value = "saveModel() || updateModel() || updateDTOModel() || updateStatusModel() || deleteModel()",returning="result")
    public void afterReturning(JoinPoint point, Object result){
        String methodName = point.getSignature().getName();
        List<Object> args = Arrays.asList(point.getArgs());
        if(LOG.isDebugEnabled()) {
            LOG.debug("连接点方法为：" + methodName + ",参数为：" + args + ",目标方法执行结果为：" + result);
        }

        String id = "";
        if(EnumServiceMethod.SAVE.getCode().equals(methodName)
                || EnumServiceMethod.UPDATE.getCode().equals(methodName)
                || EnumServiceMethod.UPDATE_DTO.getCode().equals(methodName)) {
            BasicDataDictSubDTO param = JsonUtils.parseObject(point.getArgs()[ZERO].toString(),BasicDataDictSubDTO.class);
            if(null != param){
                id = param.getId();
            }
        }
        else if(EnumServiceMethod.DELETE.getCode().equals(methodName) || EnumServiceMethod.UPDATE_STATUS.getCode().equals(methodName)){
            id = point.getArgs()[ZERO].toString();
        }

        if(StringUtils.isNotBlank(id)){
            BasicDataDictSub one =  modelService.find(id,args.get(ONE).toString());
            if(null != one){
                BasicDataDictSubDTO keyParam = new BasicDataDictSubDTO();
                keyParam.setId(id);
                keyParam.setOecdNo(one.getOecdNo());
                keyParam.setPlantformId(one.getPlantformId());
                keyParam.setDictCode(one.getDictCode());
                mgtRpcService.rpcFlushCacheBasicDataDictSubJob(keyParam, args.get(ONE).toString());
            }
            //新增或更新图片
            if(EnumServiceMethod.SAVE.getCode().equals(methodName)
                    || EnumServiceMethod.UPDATE.getCode().equals(methodName)
                    || EnumServiceMethod.UPDATE_DTO.getCode().equals(methodName)){
                mgtRpcService.addDataDictSubImgInPicPool(methodName, modelService.convertPoToDto(one), args.get(ONE).toString());
            }

        }else{
            LOG.error("获取{}的id失败,rs:{}",methodName, JsonUtils.toJSONString(result));
        }
    }

    /**
     * 验证修改
     * @param param
     * @param userNo
     * @return
     * @throws ApecRuntimeException
     */
    private BasicDataDictSubDTO validateUpdate(BasicDataDictSubDTO param,String userNo)
            throws ApecRuntimeException
    {
        checkParam(param);

        if(StringUtils.isBlank(param.getId())){
            throw new ApecRuntimeException(ERROR_CB_LOST_PARAM,new String[]{DB_COL_NAME_ID});
        }
        BasicDataDictSubDTO dbDTO = modelService.findDTO(param.getId(),userNo);
        if(null == dbDTO){
            LOG.error("query BasicDataDictSubDTO fail,id:{}",param.getId());
            throw new ApecRuntimeException(ERROR_CB_QUERY_FAIL_DATA_NOT_EXIST_BY_ID, Arrays.asList(M_EXTERNALCODE,param.getId()));
        }
//        if(!(param.getOecdNo().equals(dbDTO.getOecdNo()) && param.getPlantformId().equals(dbDTO.getPlantformId()) )){
//            LOG.error("BasicDataDictSubDTO key data cant update,param:{},db:{}", JsonUtils.toJSONString(param), JsonUtils.toJSONString(dbDTO));
//            throw new ApecRuntimeException(ERROR_CB_KEY_DATA_CANT_UPDATE, new String[]{ "oecdNo  plantformId" });
//        }
        if(!(param.getDictCode().equals(dbDTO.getDictCode()))){
            LOG.error("BasicDataDictSubDTO key data cant update,param:{},db:{}", JsonUtils.toJSONString(param), JsonUtils.toJSONString(dbDTO));
            throw new ApecRuntimeException(ERROR_CB_KEY_DATA_CANT_UPDATE, new String[]{ "dictCode" });
        }
        if(!(param.getDictSubCode().equals(dbDTO.getDictSubCode()))){
            LOG.error("BasicDataDictSubDTO key data cant update,param:{},db:{}", JsonUtils.toJSONString(param), JsonUtils.toJSONString(dbDTO));
            throw new ApecRuntimeException(ERROR_CB_KEY_DATA_CANT_UPDATE, new String[]{ "dictSubCode" });
        }
        if(StringUtils.isNotBlank(param.getImg()) && !param.getImg().equals(dbDTO.getImg())){
            param.setImgId(SnowFlakeKeyGen.nextIdStr());
        }
        return param;
    }

    /**
     * 检查基本参数
     * @param param
     */
    private void checkParam(BasicDataDictSubDTO param){
        if(null == param){
            throw new ApecRuntimeException(ERROR_CB_PARAM_IS_NULL);
        }
        if(StringUtils.isAnyBlank(
//                param.getOecdNo(),param.getPlantformId(),
                param.getDictCode(), param.getDictSubCode(),
                param.getDictSubValue(),param.getDictSubEnValue()
                )){
            LOG.error("lost param,param:{}", JsonUtils.toJSONString(param));
            throw new ApecRuntimeException(ERROR_CB_LOST_PARAM,
                    new String[]{
                            Arrays.asList(
                                    DB_ATTR_NAME_OECD_NO,
                                    DB_ATTR_NAME_PLANTFORM_ID,
                                    DB_COL_NAME_ID,
                                    C_BASICDATADICTSUB_ATTRNAME_DICTCODE,
                                    C_BASICDATADICTSUB_ATTRNAME_DICTSUBCODE,
                                    C_BASICDATADICTSUB_ATTRNAME_DICTSUBVALUE,
                                    C_BASICDATADICTSUB_ATTRNAME_DICTSUBENVALUE
                            ).toString()
                    }
            );
        }

//        BeanValidator.check(param);
    }

    /**
     * 验证新增
     * @param param
     * @param userNo
     * @return
     */
    private BasicDataDictSubDTO validateAdd(BasicDataDictSubDTO param,String userNo)
    {
        checkParam(param);
        //设置默认缺省值
        if(StringUtils.isBlank(param.getOecdNo())){
            param.setOecdNo(CommonConts.DEFAULT_OECD_NO_APEC);
        }
        if(StringUtils.isBlank(param.getPlantformId())){
            param.setPlantformId(CommonConts.DEFAULT_PLANTFORM_ID_DAZONG);
        }

        BasicDataDictSubQueryDTO query = new BasicDataDictSubQueryDTO();
        query.setEnableFlag(EnableFlag.Y);
        query.setOecdNo(param.getOecdNo());
        query.setPlantformId(param.getPlantformId());

        query.setDictCode(param.getDictCode());
        query.setDictSubCode(param.getDictSubCode());

        //启用与禁用
        List<BasicDataDictSubDTO> list = modelService.findListExt(query, userNo);

        if(CollectionUtils.isNotEmpty(list)){
            LOG.error("config data exist,db val:{}", JsonUtils.toJSONString(list.get(0)));
            throw new ApecRuntimeException(ERROR_CB_CFG_DATA_EXIST);
        }

        if(StringUtils.isBlank(param.getStatus())) {
            param.setStatus(EnumIsStatus.YES.getCode());
        }
        if(StringUtils.isNotBlank(param.getImg())){
            param.setImgId(SnowFlakeKeyGen.nextIdStr());
        }
        param.setDictSubCode(param.getDictSubCode());
        param.setEnableFlag(EnableFlag.Y);
        return param;
    }


    @Override
    public int getOrder() {
        return 2001;
    }

}
