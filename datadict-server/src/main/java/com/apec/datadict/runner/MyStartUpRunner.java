package com.apec.datadict.runner;


import com.apec.datadict.service.ExtBaseDataDictService;
import com.apec.enums.EnumEnvCode;
import com.apec.selectlist.service.MgtSelectListService;
import com.apec.selectlist.model.BasicSelectListCache;
import com.apec.springmvc.utils.SpringContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;


@Component
@Order(1)
public class MyStartUpRunner implements ApplicationRunner {

    private Logger log = LoggerFactory.getLogger(MyStartUpRunner.class);

    @Autowired
    private ExtBaseDataDictService thisService;

    @Autowired
    private MgtSelectListService mgtSelectListService;

    @Override
    public void run(ApplicationArguments applicationArguments) {
        if(EnumEnvCode.DEV.getCode().equals(SpringContext.getActiveProfile())){
            log.info("active profile eq dev, not flush.");
            return;
        }

        try {
            log.info("****** >>>>>> run job run ...");
            thisService.flushDictAllCacheJob();
            log.info("****** >>>>>> run job finish.");
        } catch (Exception e) {
            log.error("run exception. " ,e );
        }

        try {
            log.info("****** >>>>>> run job run ...");
            mgtSelectListService.flushSelectListCacheJob(new BasicSelectListCache(),"job");
            log.info("****** >>>>>> run job finish.");
        } catch (Exception e) {
            log.error("run exception. " ,e );
        }

    }

}
