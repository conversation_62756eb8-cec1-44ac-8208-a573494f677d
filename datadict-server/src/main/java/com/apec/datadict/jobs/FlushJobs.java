package com.apec.datadict.jobs;

import com.apec.datadict.service.ExtBaseDataDictService;
import com.apec.selectlist.service.MgtSelectListService;
import com.apec.selectlist.model.BasicSelectListCache;
import com.apec.springmvc.utils.SpringContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class FlushJobs {

    private static Logger log = LoggerFactory.getLogger(FlushJobs.class);
    @Autowired
    private ExtBaseDataDictService thisService;


    @Autowired
    private MgtSelectListService mgtSelectListService;

    @Scheduled(cron = "${dictcache.scheduled.flushJob}")
    public void flushJob() {
        if(SpringContext.getActiveProfile().equals("dev")){
            log.info("active profile eq dev, not flush.");
            return;
        }

        try{
            log.info("****** >>>>>> flushDictCacheJob job run ...");
            thisService.flushDictAllCacheJob();
            log.info("****** >>>>>> flushDictCacheJob job finish.");
        }
        catch (Exception e){
            log.error("****** >>>>>> flushDictCacheJob job exception.", e);
        }

        try{
            log.info("****** >>>>>> flushSelectListCacheJob job run ...");
            mgtSelectListService.flushSelectListCacheJob(new BasicSelectListCache(),"job");
            log.info("****** >>>>>> flushSelectListCacheJob job finish.");
        }
        catch (Exception e){
            log.error("****** >>>>>> flushSelectListCacheJob job exception.", e);
        }

    }
}
