package com.apec.datadict.web;

import com.apec.annotations.SystemControllerLog;
import com.apec.aops.ThreadLocalUtil;
import com.apec.datadict.dto.BasicDataDictDTO;
import com.apec.datadict.dto.ExecPushDataDictReqDTO;
import com.apec.datadict.dto.ExecPushDataDictResDTO;
import com.apec.datadict.dto.PushDataDictToTargetEnvDTO;
import com.apec.datadict.service.MgtDictService;
import com.apec.framework.common.util.SpringBizUtils;
import com.apec.framework.common.model.ResultData;
import com.apec.magpie.cb.constant.IConts;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 类 编 号：BL.datadict.Controller.MgtDictController
 * 类 名 称：MgtDictController
 * 内容摘要：数据字典数据，推送数据处理业务
 *
 * <AUTHOR>
 * @date 2020-04-23 18:35:33
 */

@RestController
@RequestMapping("/mgtdict")
public class MgtDictController implements IConts {

    @Autowired
    private MgtDictService mgtDictService;

    @RequestMapping(value = "/pushDataDictToTargetEnv", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @SystemControllerLog(description = "推送数据字典到目标环境", author = "roothomes", lastDate = "2020-04-23")
    public ResultData pushDataDictToTargetEnv(@RequestBody String reqJsonData) {
        PushDataDictToTargetEnvDTO obj = SpringBizUtils.getFormJSON(reqJsonData, PushDataDictToTargetEnvDTO.class);
        return mgtDictService.pushDataDictToTargetEnv(obj, ThreadLocalUtil.getUserIdOrNull());
    }

    @RequestMapping(value = "/execPushDict", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @SystemControllerLog(description = "处理推送过来的数据字典", author = "roothomes", lastDate = "2020-04-23")
    public ResultData execPushDict(@RequestBody String reqJsonData) {
        ExecPushDataDictReqDTO obj = SpringBizUtils.getFormJSON(reqJsonData, ExecPushDataDictReqDTO.class);
        ExecPushDataDictResDTO data = mgtDictService.execPushDict(obj, ThreadLocalUtil.getUserIdOrNull());
        return SpringBizUtils.getResultData(true, data, "");
    }


}
