package com.apec.datadict.web;

import com.apec.annotations.SystemControllerLog;
import com.apec.datadict.dto.BasicDataDictDTO;
import com.apec.datadictsub.dto.BasicDataDictSubDTO;
import com.apec.framework.common.util.SpringBizUtils;
import com.apec.framework.common.model.ResultData;
import com.apec.datadict.service.ExtBaseDataDictService;
import com.apec.magpie.cb.constant.IConts;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/mgtflush")
public class MgtFlushDataDictController implements IConts {

    @Autowired
    private ExtBaseDataDictService thisService;

    /**
     * 使用任务刷新整个字典缓存
     *
     * @param reqJsonData
     * @return
     */
    @SystemControllerLog(description = "使用任务刷新整个字典缓存", author = "roothomes", lastDate = "2019-09-11")
    @RequestMapping(value = "/flushDictAllCacheJob", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ResultData flushDictAllCacheJob(@RequestBody String reqJsonData) {
        thisService.flushDictAllCacheJob();
        return SpringBizUtils.getResultData(true, null, "");
    }

    /**
     * 刷新一个字典子项到缓存
     *
     * @param json JSON请求参数
     * @return
     * <AUTHOR>
     * @date 2019-01-23 03:29:12
     */
    @SystemControllerLog(description = "刷新一个字典子项到缓存", author = "roothomes", lastDate = "2020-04-17")
    @RequestMapping(value = "/flushDictSubCache", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public ResultData flushDictSubCache(@RequestBody String json) {
        BasicDataDictSubDTO dto = SpringBizUtils.getFormJSON(json, BasicDataDictSubDTO.class);
        thisService.flushDictSubCache(dto);
        return SpringBizUtils.getResultData(true, null, null);
    }

    /**
     * 刷新一个字典子项到缓存
     *
     * @param json JSON请求参数
     * @return
     * <AUTHOR>
     * @date 2019-01-23 03:29:12
     */
    @SystemControllerLog(description = "刷新一个字典子项到缓存", author = "roothomes", lastDate = "2020-04-17")
    @RequestMapping(value = "/flushDictCache", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public ResultData flushDictCache(@RequestBody String json) {
        BasicDataDictDTO dto = SpringBizUtils.getFormJSON(json, BasicDataDictDTO.class);
        thisService.flushDictCache(dto);
        return SpringBizUtils.getResultData(true, null, null);
    }

}
