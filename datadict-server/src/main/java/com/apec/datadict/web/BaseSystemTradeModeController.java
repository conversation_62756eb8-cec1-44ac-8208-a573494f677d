package com.apec.datadict.web;


import com.apec.annotations.SystemControllerLog;
import com.apec.aops.ThreadLocalUtil;
import com.apec.framework.common.util.SpringBizUtils;
import com.apec.framework.common.dto.PageDTO;
import com.apec.framework.common.model.PageJSON;
import com.apec.framework.common.model.ResultData;
import com.apec.magpie.cb.common.utils.SortUtils;
import com.apec.magpie.cb.constant.IConts;
import com.apec.systemtrademode.dto.BasicSystemTradeModeDTO;
import com.apec.systemtrademode.dto.BasicSystemTradeModeQueryDTO;
import com.apec.systemtrademode.model.BasicSystemTradeMode;
import com.apec.systemtrademode.service.BasicSystemTradeModeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 类 编 号：
 * 类 名 称：BaseSystemTradeModeController
 * 内容摘要：后台管理控制层
 * 创建日期：2018/08/09
 *
 * <AUTHOR>
 */

@RestController
@RequestMapping("/basicsystemtrademode")
public class BaseSystemTradeModeController implements IConts {

    @Autowired
    BasicSystemTradeModeService service;

    /**
     * 添加数据
     *
     * @param json JSON请求参数
     * @return
     */
    @SystemControllerLog(code = "xxx", description = "添加数据", author = "无名", lastDate = "2019-06-18")
    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public ResultData save(@RequestBody String json) {
        BasicSystemTradeModeDTO dto = SpringBizUtils.getFormJSON(json, BasicSystemTradeModeDTO.class);
        BasicSystemTradeModeDTO data = service.save(dto, ThreadLocalUtil.getUserIdOrNull());
        return SpringBizUtils.getResultData(true, data, null);
    }


    /**
     * 根据主键id逻辑删除
     *
     * @param json JSON请求参数
     * @return
     */
    @SystemControllerLog(code = "xxx", description = "根据主键id逻辑删除", author = "无名", lastDate = "2019-06-18")
    @RequestMapping(value = "/delete", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public ResultData delete(@RequestBody String json) {
        BasicSystemTradeModeDTO obj = SpringBizUtils.getFormJSON(json, BasicSystemTradeModeDTO.class);
        BasicSystemTradeMode data = service.delete(obj.getId(), ThreadLocalUtil.getUserIdOrNull());
        return SpringBizUtils.getResultData(true, data, null);
    }


    /**
     * 根据主键id物理删除
     *
     * @param json JSON请求参数
     * @return
     */
    @SystemControllerLog(code = "xxx", description = "根据主键id物理删除", author = "无名", lastDate = "2019-06-18")
    @RequestMapping(value = "/deleteDB", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public ResultData deleteDB(@RequestBody String json) {
        BasicSystemTradeModeDTO vo = SpringBizUtils.getFormJSON(json, BasicSystemTradeModeDTO.class);
        Boolean data = service.deleteDB(vo.getId(), ThreadLocalUtil.getUserIdOrNull());
        return SpringBizUtils.getResultData(true, data, null);
    }


    /**
     * 根据主键id更新模型
     *
     * @param json JSON请求参数
     * @return
     */
    @SystemControllerLog(code = "xxx", description = "根据主键id更新模型", author = "无名", lastDate = "2019-06-18")
    @RequestMapping(value = "/update", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public ResultData update(@RequestBody String json) {
        BasicSystemTradeMode obj = SpringBizUtils.getFormJSON(json, BasicSystemTradeMode.class);
        BasicSystemTradeMode data = service.update(obj, ThreadLocalUtil.getUserIdOrNull());
        return SpringBizUtils.getResultData(true, data, null);
    }

    /**
     * 根据主键id更新模型
     *
     * @param json JSON请求参数
     * @return
     */
    @SystemControllerLog(code = "xxx", description = "根据主键id更新模型", author = "无名", lastDate = "2019-06-18")
    @RequestMapping(value = "/updateDTO", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public ResultData updateDTO(@RequestBody String json) {
        BasicSystemTradeModeDTO obj = SpringBizUtils.getFormJSON(json, BasicSystemTradeModeDTO.class);
        BasicSystemTradeModeDTO data = service.updateDTO(obj, ThreadLocalUtil.getUserIdOrNull());
        return SpringBizUtils.getResultData(true, data, null);
    }


    /**
     * 根据主键id更新模型状态
     *
     * @param json JSON请求参数
     * @return
     */
    @SystemControllerLog(code = "xxx", description = "根据主键id更新模型状态", author = "无名", lastDate = "2019-06-18")
    @RequestMapping(value = "/updateStatus", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public ResultData updateStatus(@RequestBody String json) {
        BasicSystemTradeMode obj = SpringBizUtils.getFormJSON(json, BasicSystemTradeMode.class);
        BasicSystemTradeMode data = service.updateStatus(obj.getId(), obj.getStatus(), ThreadLocalUtil.getUserIdOrNull());
        return SpringBizUtils.getResultData(true, data, null);
    }

    /**
     * 根据主键id更新模型排序值
     *
     * @param json JSON请求参数
     * @return
     */
    @SystemControllerLog(code = "xxx", description = "根据主键id更新模型排序值", author = "无名", lastDate = "2019-06-18")
    @RequestMapping(value = "/updateOrderNumber", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public ResultData updateOrderNumber(@RequestBody String json) {
        BasicSystemTradeMode obj = SpringBizUtils.getFormJSON(json, BasicSystemTradeMode.class);
        BasicSystemTradeMode data = service.updateOrderNumber(obj.getId(), obj.getOrderNumber(), ThreadLocalUtil.getUserIdOrNull());
        return SpringBizUtils.getResultData(true, data, null);
    }


    /**
     * 根据主键id查询模型
     *
     * @param json JSON请求参数
     * @return
     */
    @SystemControllerLog(code = "xxx", description = "根据主键id查询模型", author = "无名", lastDate = "2019-06-18")
    @RequestMapping(value = "/find", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public ResultData selectById(@RequestBody String json) {
        BasicSystemTradeMode obj = SpringBizUtils.getFormJSON(json, BasicSystemTradeMode.class);
        BasicSystemTradeMode data = service.find(obj.getId(), ThreadLocalUtil.getUserIdOrNull());
        return SpringBizUtils.getResultData(true, data, null);
    }


    /**
     * 根据主键id查询模型
     *
     * @param json JSON请求参数
     * @return
     */
    @SystemControllerLog(code = "xxx", description = "根据主键id查询模型", author = "无名", lastDate = "2019-06-18")
    @RequestMapping(value = "/findDTO", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public ResultData findDTO(@RequestBody String json) {
        BasicSystemTradeModeDTO dto = SpringBizUtils.getFormJSON(json, BasicSystemTradeModeDTO.class);
        BasicSystemTradeModeDTO data = service.findDTO(dto.getId(), ThreadLocalUtil.getUserIdOrNull());
        return SpringBizUtils.getResultData(true, data, null);
    }

    /**
     * 查询集合(基础模型)
     *
     * @param json JSON请求参数
     * @return
     */
    @SystemControllerLog(code = "xxx", description = "查询集合(基础模型)", author = "无名", lastDate = "2019-06-18")
    @RequestMapping(value = "/findList", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public ResultData findList(@RequestBody String json) {
        BasicSystemTradeModeDTO dto = SpringBizUtils.getFormJSON(json, BasicSystemTradeModeDTO.class);
        List<BasicSystemTradeMode> data = service.findList(dto, ThreadLocalUtil.getUserIdOrNull());
        return SpringBizUtils.getResultData(true, data, null);
    }


    /**
     * 查询集合(扩展模型)
     *
     * @param json JSON请求参数
     * @return
     */
    @SystemControllerLog(code = "xxx", description = "查询集合(扩展模型)", author = "无名", lastDate = "2019-06-18")
    @RequestMapping(value = "/findListExt", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ResultData findListExt(@RequestBody String json) {
        BasicSystemTradeModeQueryDTO obj = SpringBizUtils.getFormJSON(json, BasicSystemTradeModeQueryDTO.class);
        List<BasicSystemTradeModeDTO> data = service.findListExt(obj, ThreadLocalUtil.getUserIdOrNull());
        return SpringBizUtils.getResultData(true, data, null);
    }

    /**
     * 分页查询模型列表
     *
     * @param json JSON请求参数
     * @return
     */
    @SystemControllerLog(code = "xxx", description = "分页查询模型列表", author = "无名", lastDate = "2019-06-18")
    @RequestMapping(value = "/findByPage", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public ResultData findByPage(@RequestBody String json) {
        BasicSystemTradeModeDTO dto = SpringBizUtils.getFormJSON(json, BasicSystemTradeModeDTO.class);
        PageRequest pageRequest = SortUtils.genPageRequest(dto);
        PageDTO<BasicSystemTradeModeDTO> data = service.findByPageDTO(dto, pageRequest, ThreadLocalUtil.getUserIdOrNull());
        return SpringBizUtils.getResultData(true, data, null);
    }


    /**
     * 分页
     *
     * @param json JSON请求参数
     * @return
     */
    @SystemControllerLog(code = "xxx", description = "分页查询模型列表", author = "无名", lastDate = "2019-06-18")
    @RequestMapping(value = "/findByPageExt", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ResultData findByPageExt(@RequestBody String json) {
        BasicSystemTradeModeQueryDTO dto = SpringBizUtils.getFormJSON(json, BasicSystemTradeModeQueryDTO.class);
        PageRequest request = SortUtils.genPageRequest(dto);
        PageDTO<BasicSystemTradeModeDTO> data = service.findByPageExt(dto, request, ThreadLocalUtil.getUserIdOrNull());
        return SpringBizUtils.getResultData(true, data, null);
    }

}
