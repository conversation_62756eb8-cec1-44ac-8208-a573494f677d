package com.apec.datadict.web;


import com.alibaba.fastjson.JSONObject;
import com.apec.annotations.SystemControllerLog;
import com.apec.aops.ThreadLocalUtil;
import com.apec.datadictsub.dto.BasicDataDictSubDTO;
import com.apec.framework.common.util.SpringBizUtils;
import com.apec.framework.common.model.ResultData;
import com.apec.datadict.dto.DictItemSelectListQueryDTO;
import com.apec.datadict.service.FacadeDictSubService;
import com.apec.datadict.dto.QueryNormalTradeModeDTO;
import com.apec.magpie.cb.constant.IConts;
import com.apec.magpie.cb.dto.DataDictSysConfigDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 类 编 号：
 * 类 名 称：FacadeDictSubController
 * 内容摘要：后台管理控制层
 * 创建日期：2018/08/09
 *
 * <AUTHOR>
 * @Service(functionId = "300592", desc = "根据关系查询当前可用的交易模式")
 */

@RestController
@RequestMapping("/facadedictsub")
public class FacadeDictSubController implements IConts {

    @Autowired
    private FacadeDictSubService facadeDictSubService;


    /**
     * 根据关系查询当前可用的交易模式(外服服务调用)
     *
     * @param reqJsonData 请求JSON数据
     */
    @RequestMapping(value = "/queryNormalTradeMode", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @SystemControllerLog(description = "根据关系查询当前可用的交易模式", author = "roothomes", lastDate = "2019-09-11")
    public ResultData<List<BasicDataDictSubDTO>> queryNormalTradeMode(@RequestBody String reqJsonData) {
        QueryNormalTradeModeDTO obj = SpringBizUtils.getFormJSON(reqJsonData, QueryNormalTradeModeDTO.class);
        List<BasicDataDictSubDTO> rs = facadeDictSubService.queryNormalTradeMode(obj);
        return SpringBizUtils.getResultData(true, rs, "");
    }

    @RequestMapping(value = "/queryDataDictSubListKV", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @SystemControllerLog(description = "根据状态查询数据字典子项KV", author = "roothomes", lastDate = "2019-09-11")
    public ResultData queryDataDictSubListKV(@RequestBody String reqJsonData) {
        DictItemSelectListQueryDTO obj = SpringBizUtils.getFormJSON(reqJsonData, DictItemSelectListQueryDTO.class);
        JSONObject data = facadeDictSubService.queryDataDictSubListKV(obj);
        return SpringBizUtils.getResultData(true, data, "");
    }

    @RequestMapping(value = "/updateDataDictSysConfig", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @SystemControllerLog(description = "更新数据字典系统配置", author = "roothomes", lastDate = "2019-09-11")
    public ResultData updateDataDictSysConfig(@RequestBody String json) {
        DataDictSysConfigDTO obj = SpringBizUtils.getFormJSON(json, DataDictSysConfigDTO.class);
        DataDictSysConfigDTO data = facadeDictSubService.updateDataDictSysConfig(obj, ThreadLocalUtil.getUserIdOrNull());
        return SpringBizUtils.getResultData(true, data, "");
    }

    @RequestMapping(value = "/queryDataDictSysConfig", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @SystemControllerLog(description = "查询数据字典系统配置", author = "roothomes", lastDate = "2019-09-11")
    public ResultData queryDataDictSysConfig(@RequestBody String json) {
        DataDictSysConfigDTO obj = SpringBizUtils.getFormJSON(json, DataDictSysConfigDTO.class);
        DataDictSysConfigDTO data = facadeDictSubService.queryDataDictSysConfig(obj, ThreadLocalUtil.getUserIdOrNull());
        return SpringBizUtils.getResultData(true, data, "");
    }


}
