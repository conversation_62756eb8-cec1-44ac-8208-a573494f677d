package com.apec.datadict.web;


import com.apec.annotations.SystemControllerLog;
import com.apec.aops.ThreadLocalUtil;
import com.apec.datadict.dto.BasicDataDictDTO;
import com.apec.datadict.dto.GetDictSomeItemSelectDTO;
import com.apec.datadictsub.dto.BasicDataDictSubDTO;
import com.apec.framework.common.util.SpringBizUtils;
import com.apec.framework.common.model.ResultData;

import com.apec.magpie.cb.constant.IConts;
import com.apec.magpie.cb.dto.JsonItemDTO;
import com.apec.magpie.cb.dto.SelectDTO;
import com.apec.magpie.cb.dto.SelectQueryDTO;
import com.apec.datadict.service.ExtBaseDataDictService;
import com.apec.datadict.common.DataDictConts;
import com.apec.datadict.dto.DictItemSelectListQueryDTO;
import com.apec.datadict.vo.BasicDataDictAndSubListDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 类 编 号：
 * 类 名 称：BaseDataDictController
 * 内容摘要：后台管理控制层
 * 创建日期：2018/08/08
 *
 * <AUTHOR>
 */

@RestController
@RequestMapping("/extbasedatadict")
public class ExtBaseDataDictController implements IConts {

    @Autowired
    private ExtBaseDataDictService thisService;

    /**
     * 查询
     *
     * @param reqJsonData 请求JSON数据
     */
    @RequestMapping(value = "/findDictAndSubDict", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @SystemControllerLog(description = "查询字典与子项信息", author = "roothomes", lastDate = "2019-09-11")
    public ResultData<BasicDataDictAndSubListDTO> findDictAndSubDict(@RequestBody String reqJsonData) {
        BasicDataDictDTO obj = SpringBizUtils.getFormJSON(reqJsonData, BasicDataDictDTO.class);
        BasicDataDictAndSubListDTO rs = thisService.findDictAndSubDict(obj);
        return SpringBizUtils.getResultData(true, rs, "");
    }


    /**
     * 查询下来框信息
     *
     * @param reqJsonData
     * @return
     */
    @RequestMapping(value = "/getSelect", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @SystemControllerLog(description = "查询下来框信息", author = "roothomes", lastDate = "2019-09-11")
    public ResultData<List<SelectDTO>> getSelect(@RequestBody String reqJsonData) {
        SelectQueryDTO obj = SpringBizUtils.getFormJSON(reqJsonData, SelectQueryDTO.class);
        List<SelectDTO> rs = thisService.querySelectList(obj, ThreadLocalUtil.getUserIdOrNull());
        return SpringBizUtils.getResultData(true, rs, "");
    }

    /**
     * 查询字典下来框信息
     *
     * @param reqJsonData
     * @return
     */
    @RequestMapping(value = "/getDictItemSelect", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @SystemControllerLog(description = "查询字典下来框信息", author = "roothomes", lastDate = "2019-09-11")
    public ResultData<List<SelectDTO>> getDictItemSelect(@RequestBody String reqJsonData) {
        BasicDataDictSubDTO obj = SpringBizUtils.getFormJSON(reqJsonData, BasicDataDictSubDTO.class);
        List<SelectDTO> rs = thisService.queryDictItemSelectList(obj);
        return SpringBizUtils.getResultData(true, rs, "");
    }

    /**
     * 查询字典项的指定子项下来框信息
     *
     * @param reqJsonData
     * @return 创建日期：2020/05/18
     * <AUTHOR>
     */
    @RequestMapping(value = "/getDictSomeItemSelect", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @SystemControllerLog(description = "查询字典项的指定子项下来框信息", author = "roothomes", lastDate = "2019-09-11")
    public ResultData<List<SelectDTO>> getDictSomeItemSelect(@RequestBody String reqJsonData) {
        GetDictSomeItemSelectDTO obj = SpringBizUtils.getFormJSON(reqJsonData, GetDictSomeItemSelectDTO.class);
        List<SelectDTO> rs = thisService.getDictSomeItemSelect(obj);
        return SpringBizUtils.getResultData(true, rs, "");
    }

    /**
     * 查询字典下来框信息(多个一起查询)
     *
     * @param reqJsonData
     * @return
     */
    @RequestMapping(value = "/getDictItemSelectList", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @SystemControllerLog(description = "查询字典下来框信息(多个一起查询)", author = "roothomes", lastDate = "2019-09-11")
    public ResultData getDictItemSelectList(@RequestBody String reqJsonData) {
        DictItemSelectListQueryDTO obj = SpringBizUtils.getFormJSON(reqJsonData, DictItemSelectListQueryDTO.class);
        Map<String, List<SelectDTO>> data = thisService.queryMoreDictCodeSelectListMap(obj);
        return SpringBizUtils.getResultData(true, data, "");
    }

    /**
     * 查询字典下来框信息(多个一起查询)（排除禁用）
     *
     * @param reqJsonData
     * @return
     */
    @RequestMapping(value = "/getValidDictItemSelectList", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @SystemControllerLog(description = "查询字典下来框信息(多个一起查询)（排除禁用）", author = "roothomes", lastDate = "2019-09-11")
    public ResultData getValidDictItemSelectList(@RequestBody String reqJsonData) {
        DictItemSelectListQueryDTO obj = SpringBizUtils.getFormJSON(reqJsonData, DictItemSelectListQueryDTO.class);
        Map<String, List<SelectDTO>> data = thisService.queryMoreDictCodeSelectListMap(obj, "1");
        return SpringBizUtils.getResultData(true, data, "");
    }

    /**
     * 查询字典下来框信息
     *
     * @param reqJsonData
     * @return
     */
    @RequestMapping(value = "/getDictItemSelectKV", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @SystemControllerLog(description = "查询字典下来框信息(kv)", author = "roothomes", lastDate = "2019-09-11")
    public ResultData<List<JsonItemDTO>> getDictItemSelectKV(@RequestBody String reqJsonData) {
        BasicDataDictSubDTO obj = SpringBizUtils.getFormJSON(reqJsonData, BasicDataDictSubDTO.class);
        List<SelectDTO> rs = thisService.queryDictItemSelectList(obj);
        List<JsonItemDTO> data = new ArrayList<>();
        if (null != rs && !rs.isEmpty()) {
            int i = 0;
            for (SelectDTO e : rs) {
                i++;
                data.add(new JsonItemDTO(e.getKey(), e.getVal(), i));
            }
        }
        return SpringBizUtils.getResultData(true, data, "");
    }

    /**
     * 查询下来框信息
     *
     * @param reqJsonData
     * @return
     */
    @RequestMapping(value = "/initMarkConfigDataDict", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @SystemControllerLog(description = "查询下来框信息(kv)", author = "roothomes", lastDate = "2019-09-11")
    public ResultData<List<SelectDTO>> initMarkConfigDataDict(@RequestBody String reqJsonData) {
        SelectQueryDTO obj = SpringBizUtils.getFormJSON(reqJsonData, SelectQueryDTO.class);
        List<SelectDTO> rs = thisService.querySelectList(obj, ThreadLocalUtil.getUserIdOrNull());
        return SpringBizUtils.getResultData(true, rs, "");
    }

    /**
     * 判断是否下拉列表的key,在该字典的字典子项是否存在
     *
     * @param reqJsonData
     * @return
     */
    @RequestMapping(value = "/checkSelectInDictItemK", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @SystemControllerLog(description = "判断是否下拉列表的key,在该字典的字典子项是否存在.", author = "roothomes", lastDate = "2019-09-11")
    public ResultData<SelectDTO> checkSelectInDictItemK(@RequestBody String reqJsonData) {
        BasicDataDictSubDTO obj = SpringBizUtils.getFormJSON(reqJsonData, BasicDataDictSubDTO.class);
        SelectDTO existSel = null;
        List<SelectDTO> rs = thisService.queryDictItemSelectList(obj);
        if (null == rs || rs.isEmpty()) {
            return SpringBizUtils.getResultData(false, null, DataDictConts.ERROR_DICT_CODE_NOT_EXIST);
        } else {
            boolean flag = false;
            for (SelectDTO one : rs) {
                if (one.getKey().equals(obj.getDictSubCode())) {
                    flag = true;
                    existSel = one;
                    break;
                }
            }
            if (!flag) {
                return SpringBizUtils.getResultData(false, null, DataDictConts.ERROR_DICT_CODE_NOT_HAVE_SUB_ITEM);
            }
        }
        return SpringBizUtils.getResultData(true, existSel, "");
    }


    /**
     * 判断是否下拉列表的value,在该字典的字典子项是否存在
     *
     * @param reqJsonData
     * @return
     */
    @RequestMapping(value = "/checkSelectInDictItemV", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @SystemControllerLog(description = "判断是否下拉列表的value,在该字典的字典子项是否存在.", author = "roothomes", lastDate = "2019-09-11")
    public ResultData<SelectDTO> checkSelectInDictItemV(@RequestBody String reqJsonData) {
        BasicDataDictSubDTO obj = SpringBizUtils.getFormJSON(reqJsonData, BasicDataDictSubDTO.class);
        SelectDTO existSel = null;
        List<SelectDTO> rs = thisService.queryDictItemSelectList(obj);
        if (null == rs || rs.isEmpty()) {
            return SpringBizUtils.getResultData(false, null, DataDictConts.ERROR_DICT_CODE_NOT_EXIST);
        } else {
            boolean flag = false;
            for (SelectDTO one : rs) {
                if (one.getVal().equals(obj.getDictSubValue())) {
                    flag = true;
                    existSel = one;
                    break;
                }
            }
            if (!flag) {
                return SpringBizUtils.getResultData(false, null, DataDictConts.ERROR_DICT_CODE_NOT_HAVE_SUB_ITEM);
            }
        }
        return SpringBizUtils.getResultData(true, existSel, "");
    }


}
