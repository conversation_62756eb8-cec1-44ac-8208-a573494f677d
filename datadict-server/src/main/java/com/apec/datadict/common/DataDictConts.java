package com.apec.datadict.common;

import com.apec.datadict.constants.BasicDataDictContant;
import com.apec.datadictsub.constants.BasicDataDictSubContant;
import com.apec.framework.common.constant.ErrorCodeConsts;
import com.apec.magpie.cb.constant.*;

public interface DataDictConts extends
        ErrorCodeConsts,
        IMgtCommonConts,
        BasicDataDictContant,
        BasicDataDictSubContant
{

    /****************    dataDict start  01117****     *****************/
    /**
     * 推送数据字典到目标环境的数据大于{0}
     */
    String ERROR_DATA_DICT_PUSH_TARGET_ENV_DATA_TO_BIG = ERROR_DATA_DICT_PRIFIX + "600";

    /**
     * 子系统代码
     */
    String ERROR_SUB_SYSTEM_CODE_NOT_NULL = "";
    String ERROR_SUB_SYSTEM_CODE_NOT_EXIST = "";
    /**
     *字典编码
     */
    String ERROR_DICT_CODE_NOT_NULL = "";
    /**
     * 字典编码不存在
     */
    String ERROR_DICT_CODE_NOT_EXIST = "";
    /**
     * 字典编码没有子项
     */
    String ERROR_DICT_CODE_NOT_HAVE_SUB_ITEM = "";

    /**
     *数据字典的值
     */
    String ERROR_DICT_VALUE_NOT_NULL = "";
    /**
     *字典英文值
     */
    String ERROR_DICT_EN_VALUE_NOT_NULL = "";
    /**
     *字典类型  1-系统字典，2-业务字典
     */
    String ERROR_DICT_TYPE_NOT_NULL = "";
    /**
     *字典维护级别  0-运维，1-业务
     */
    String ERROR_DICT_LEVEL_NOT_NULL = "";
    /**
     *字典启用状态 0-启用 1-禁用
     */
    String ERROR_DICT_STATUS_NOT_NULL = "";



}
