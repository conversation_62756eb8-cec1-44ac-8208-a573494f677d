package com.apec.selectlist.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CacheBasicSelectListCacheDTO {
    private String id;
    private String failMsg;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastFailTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastSuccessTime;
    private Integer flushMinute;
    private String extQueryPath;
    private String serviceName;
    private String selectTypeName;
    private String selectTypeCode;
}
