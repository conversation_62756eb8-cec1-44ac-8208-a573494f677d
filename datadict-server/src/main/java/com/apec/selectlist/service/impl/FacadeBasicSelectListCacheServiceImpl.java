package com.apec.selectlist.service.impl;


import com.apec.cache.base.CacheService;
import com.apec.framework.common.util.BeanUtils;
import com.apec.framework.common.util.JsonUtils;
import com.apec.magpie.cb.constant.CommonConts;
import com.apec.magpie.cb.dto.JobMsgDTO;
import com.apec.magpie.cb.dto.JobReportDTO;
import com.apec.magpie.cb.dto.JobRequestDTO;
import com.apec.magpie.cb.enums.EnumCheck;
import com.apec.magpie.cb.enums.EnumJobMsgType;
import com.apec.mgt.service.impl.ApecMgtExecutor;
import com.apec.selectlist.constants.BasicSelectListCacheContant;

import java.lang.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.apec.selectlist.dto.CacheBasicSelectListCacheDTO;
import com.apec.selectlist.dto.BasicSelectListCacheDTO;
import com.apec.selectlist.dto.BasicSelectListCacheQueryDTO;
import com.apec.selectlist.service.BasicSelectListCacheService;
import com.apec.selectlist.service.FacadeBasicSelectListCacheService;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2018-09-19 07:00:31
 */
@Service
public class FacadeBasicSelectListCacheServiceImpl implements FacadeBasicSelectListCacheService, BasicSelectListCacheContant {
    private final Logger LOG = LoggerFactory.getLogger(getClass());

    @Resource(name = "redisService")
    private CacheService cacheService;

    @Autowired
    private BasicSelectListCacheService service;

    @Autowired
    private ApecMgtExecutor apecMgtExecutor;

    @Override
    public JobReportDTO jobFlushSelectList(JobRequestDTO jobRequestDTO) {
        String tip = " 基础下拉框缓存管理 ";
        JobReportDTO jobReportDTO = new JobReportDTO();
        jobReportDTO.setJobNo(jobRequestDTO.getJobNo());
        jobReportDTO.setJobName(jobRequestDTO.getJobName());
        jobReportDTO.setJobBatchNo(jobRequestDTO.getJobBatchNo());
        jobReportDTO.setJobCheckFlag(EnumCheck.CHECK.getCode());

        JobMsgDTO jobMsgDTO = new JobMsgDTO();
        jobMsgDTO.setMsgTime(new Date());
        jobMsgDTO.setMsgType(EnumJobMsgType.RPC_START.getCode());
        jobMsgDTO.setMsg("服务刷新" + tip + "的job开始执行。");

        removeMsgFromCache(jobRequestDTO);
        addFlushMsgInCache(jobRequestDTO, jobMsgDTO);

        //TODO 执行任务刷新，并设置数据入缓存
        apecMgtExecutor.getExecutor().execute(() -> {
            List<BasicSelectListCacheDTO> listAll = service.findListExt(new BasicSelectListCacheQueryDTO(), "job");
            putSelectListCacheInCacheServer(listAll);
            for (BasicSelectListCacheDTO one : listAll) {
                try {
                    JobMsgDTO oneMsg = new JobMsgDTO();
                    oneMsg.setMsgTime(new Date());
                    oneMsg.setMsgType(EnumJobMsgType.RPC_TIP.getCode());
                    oneMsg.setMsg("selectListCache data:" + one.getSelectTypeCode() + " " + one.getSelectTypeName() + "  " + one.getServiceName() + one.getExtQueryPath() + " " + one.getFlushMinute());
                    addFlushMsgInCache(jobRequestDTO, oneMsg);
                    LOG.info(JsonUtils.toJSONString(one));
                } catch (Exception e) {
                    e.printStackTrace();
                    JobMsgDTO lastOne = new JobMsgDTO();
                    lastOne.setMsgTime(new Date());
                    lastOne.setMsgType(EnumJobMsgType.RPC_ERROR.getCode());
                    lastOne.setMsg("服务刷新" + tip + "的job执行完毕。");
                    addFlushMsgInCache(jobRequestDTO, jobMsgDTO);
                }
            }
            JobMsgDTO lastOne = new JobMsgDTO();
            lastOne.setMsgTime(new Date());
            lastOne.setMsgType(EnumJobMsgType.RPC_END.getCode());
            lastOne.setMsg("服务刷新" + tip + "的job执行完毕。");
            addFlushMsgInCache(jobRequestDTO, lastOne);
            LOG.info(JsonUtils.toJSONString(lastOne));
        });

        jobReportDTO.setJobMsgs(getFlushMsgFromCache(jobRequestDTO));
        return jobReportDTO;
    }

    private String getJobCacheKey(JobRequestDTO jobRequestDTO) {
        return CommonConts.CACHE_BASIC_SELECT_LIST_CACHE_JOB_FLUSH_MSGLIST + jobRequestDTO.getJobNo() + "_" + jobRequestDTO.getJobBatchNo();
    }

    private void removeMsgFromCache(JobRequestDTO jobRequestDTO) {
        cacheService.remove(getJobCacheKey(jobRequestDTO));
    }

    /**
     * 获取日志集合
     *
     * @param jobRequestDTO
     * @return
     */
    private List<JobMsgDTO> getFlushMsgFromCache(JobRequestDTO jobRequestDTO) {
        String val = cacheService.get(getJobCacheKey(jobRequestDTO));
        List<JobMsgDTO> listMsg = null;
        if (Strings.isNullOrEmpty(val)) {
            listMsg = new ArrayList<>();
        } else {
            listMsg = JsonUtils.parseArray(val, JobMsgDTO.class);
        }
        return listMsg;
    }

    @Override
    public void putSelectListCacheInCacheServer(List<BasicSelectListCacheDTO> list) {
        List<CacheBasicSelectListCacheDTO> cacheData = new ArrayList<>();
        list.stream().forEach(e -> {
            CacheBasicSelectListCacheDTO dto = new CacheBasicSelectListCacheDTO();
            BeanUtils.copyPropertiesIgnoreNullFilds(e, dto);
            setOneSelectListCacheInCacheServer(dto);
            cacheData.add(dto);
        });
        cacheService.add(CommonConts.CACHE_BASIC_SELECT_LIST_CACHE_MGT, JsonUtils.toJSONString(cacheData));
    }

    @Override
    public List<CacheBasicSelectListCacheDTO> getSelectListCacheFromCacheServer() {
        String val = cacheService.get(CommonConts.CACHE_BASIC_SELECT_LIST_CACHE_MGT);
        List<CacheBasicSelectListCacheDTO> listMsg = null;
        if (Strings.isNullOrEmpty(val)) {
            listMsg = new ArrayList<>();
        } else {
            listMsg = JsonUtils.parseArray(val, CacheBasicSelectListCacheDTO.class);
        }
        return listMsg;
    }

    @Override
    public void setOneSelectListCacheInCacheServer(CacheBasicSelectListCacheDTO one) {
        String key = CommonConts.CACHE_BASIC_SELECT_LIST_CACHE_MGT_ONE_PREFIX + one.getSelectTypeCode().trim().toLowerCase();
        cacheService.add(key, JsonUtils.toDefaultJSONString(one));
    }

    @Override
    public CacheBasicSelectListCacheDTO getOneSelectListCacheFromCacheServer(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        String key = CommonConts.CACHE_BASIC_SELECT_LIST_CACHE_MGT_ONE_PREFIX + code.trim().toLowerCase();
        String val = cacheService.get(key);
        if (StringUtils.isNotBlank(val)) {
            CacheBasicSelectListCacheDTO one = JsonUtils.parseObject(val, CacheBasicSelectListCacheDTO.class);
            return one;
        }
        return null;
    }


    /**
     * 添加日志
     *
     * @param jobRequestDTO
     * @param msg
     */
    private void addFlushMsgInCache(JobRequestDTO jobRequestDTO, JobMsgDTO msg) {
        List<JobMsgDTO> listMsg = getFlushMsgFromCache(jobRequestDTO);
        listMsg.add(msg);
        cacheService.add(getJobCacheKey(jobRequestDTO), JsonUtils.toJSONString(listMsg), 5 * 60);
    }

    @Override
    public JobReportDTO checkFlushSelectListJob(JobRequestDTO jobRequestDTO) {
        JobReportDTO jobReportDTO = new JobReportDTO();
        jobReportDTO.setJobNo(jobRequestDTO.getJobNo());
        jobReportDTO.setJobName(jobRequestDTO.getJobName());
        jobReportDTO.setJobBatchNo(jobRequestDTO.getJobBatchNo());
        jobReportDTO.setJobMsgs(getFlushMsgFromCache(jobRequestDTO));
        return jobReportDTO;
    }
}
