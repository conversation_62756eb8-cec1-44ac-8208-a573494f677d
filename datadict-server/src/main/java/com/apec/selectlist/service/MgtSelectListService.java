package com.apec.selectlist.service;

import com.apec.framework.common.exception.ApecRuntimeException;
import com.apec.selectlist.model.BasicSelectListCache;

public interface MgtSelectListService {
    /**
     * 后台任务刷新对象模型所有数据
     *
     * @param entity
     * @param userId 用户编号
     * @throws ApecRuntimeException
     * <AUTHOR>
     * @date 2018-09-19 07:00:31
     */
    void flushSelectListCacheJob(BasicSelectListCache entity, String userId)
            throws ApecRuntimeException;
}
