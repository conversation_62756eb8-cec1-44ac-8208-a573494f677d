package com.apec.selectlist.service;


import java.util.List;

import com.apec.magpie.cb.dto.JobReportDTO;
import com.apec.magpie.cb.dto.JobRequestDTO;
import com.apec.selectlist.dto.CacheBasicSelectListCacheDTO;
import com.apec.selectlist.dto.BasicSelectListCacheDTO;

/**
 * <AUTHOR>
 * @date 2018-09-19 07:00:31
 */
public interface FacadeBasicSelectListCacheService {

    /**
     * 向缓存中设置下拉框管理数据
     *
     * @param list
     */
    void putSelectListCacheInCacheServer(List<BasicSelectListCacheDTO> list);

    /**
     * 从缓存中获取下拉框管理数据
     *
     * @return
     */
    List<CacheBasicSelectListCacheDTO> getSelectListCacheFromCacheServer();

    /**
     * 设置数据入缓存
     *
     * @param one
     */
    void setOneSelectListCacheInCacheServer(CacheBasicSelectListCacheDTO one);

    /**
     * 提取缓存数据
     *
     * @param code
     * @return
     */
    CacheBasicSelectListCacheDTO getOneSelectListCacheFromCacheServer(String code);

    /**
     * job 刷新数据
     *
     * @param jobRequestDTO
     * @return
     */
    JobReportDTO jobFlushSelectList(JobRequestDTO jobRequestDTO);

    /**
     * 检查刷新job的结果
     *
     * @param jobRequestDTO
     * @return
     */
    JobReportDTO checkFlushSelectListJob(JobRequestDTO jobRequestDTO);
}
