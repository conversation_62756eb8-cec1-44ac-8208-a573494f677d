package com.apec.selectlist.service.impl;

import com.apec.cache.base.CacheService;
import com.apec.framework.common.enumtype.EnableFlag;
import com.apec.framework.common.exception.ApecRuntimeException;
import com.apec.framework.common.util.JsonUtils;
import com.apec.magpie.cb.dto.SortAttrDTO;
import com.apec.mgt.service.impl.ApecMgtExecutor;
import com.apec.selectlist.constants.BasicSelectListCacheContant;
import com.apec.selectlist.service.FacadeBasicSelectListCacheService;
import com.apec.selectlist.service.MgtSelectListService;
import com.apec.selectlist.dto.BasicSelectListCacheDTO;
import com.apec.selectlist.dto.BasicSelectListCacheQueryDTO;
import com.apec.selectlist.model.BasicSelectListCache;
import com.apec.selectlist.service.BasicSelectListCacheService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
public class MgtSelectListServiceImpl implements MgtSelectListService, BasicSelectListCacheContant {


    private final Logger LOG = LoggerFactory.getLogger(getClass());

    @Autowired
    private BasicSelectListCacheService basicSelectListCacheService;

    @Resource
    private CacheService cacheService;

    @Autowired
    private FacadeBasicSelectListCacheService facadeBasicSelectListCacheService;
    @Autowired
    private ApecMgtExecutor apecMgtExecutor;

    @Override
    public void flushSelectListCacheJob(BasicSelectListCache entity, String userId) throws ApecRuntimeException {
        apecMgtExecutor.getExecutor().execute(() -> {
            flushCache(entity);
        });
    }

    /**
     * 刷新类型的数据到数据库 （修改状态就需要刷新）
     *
     * @param entity
     * <AUTHOR>
     * @date 2018-09-19 07:00:31
     */
    private void flushCache(BasicSelectListCache entity) {
        BasicSelectListCacheQueryDTO queryDTO = new BasicSelectListCacheQueryDTO();
        queryDTO.setEnableFlag(EnableFlag.Y);
        queryDTO.setStatus(STATUS_VALID);
        List<SortAttrDTO> sortList = new ArrayList<>();
        sortList.add(new SortAttrDTO("orderNumber", "asc"));
        queryDTO.setSortList(sortList);
        List<BasicSelectListCacheDTO> list = basicSelectListCacheService.findListExt(queryDTO, "job");
        cacheService.add(getCacheKey(entity), JsonUtils.toJSONString(list));
        facadeBasicSelectListCacheService.putSelectListCacheInCacheServer(list);
        LOG.debug("刷新数据到缓存：" + getCacheKey(entity) + " \n" + JsonUtils.toJSONString(list));
    }

    /**
     * 管理选择下拉模型的配置管理。
     *
     * @param entity 对象
     * @return
     */
    private String getCacheKey(BasicSelectListCache entity) {
        return CACHE_PREFIX + "BasicSelectListCache_List";
    }
}
