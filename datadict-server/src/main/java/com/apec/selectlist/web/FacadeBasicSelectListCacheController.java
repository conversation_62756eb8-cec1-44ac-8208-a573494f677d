package com.apec.selectlist.web;

import com.apec.annotations.SystemControllerLog;
import com.apec.aops.ThreadLocalUtil;
import com.apec.framework.common.util.SpringBizUtils;
import com.apec.magpie.cb.constant.IConts;
import com.apec.magpie.cb.dto.JobReportDTO;
import com.apec.magpie.cb.dto.JobRequestDTO;
import com.apec.selectlist.service.FacadeBasicSelectListCacheService;

import java.lang.*;

import com.apec.framework.common.model.ResultData;
import com.apec.selectlist.service.MgtSelectListService;
import com.apec.selectlist.model.BasicSelectListCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 类 编 号：BL_PU6030202_1000_facadecontroller
 * 类 名 称：FacadeBasicSelectListCacheController
 * 内容摘要：业务模型ExtController类
 *
 * <AUTHOR>
 * @date 2018-09-19 07:00:31
 */

@RestController
@RequestMapping("/facadebasicSelectListCache")
public class FacadeBasicSelectListCacheController implements IConts {

    @Autowired
    FacadeBasicSelectListCacheService service;

    @Autowired
    private MgtSelectListService mgtSelectListService;

    /**
     * hello
     *
     * @param json JSON请求参数
     * @return
     * <AUTHOR>
     * @date 2018-09-19 07:00:31
     */
    @RequestMapping(value = "/hello", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @SystemControllerLog(description = "hello", author = "roothomes", lastDate = "2019-09-11")
    public ResultData hello(@RequestBody String json) {
        return SpringBizUtils.getResultData(true, null, null);
    }


    /**
     * 刷新数据缓存
     *
     * @param json JSON请求参数
     * @return
     * <AUTHOR>
     * @date 2018-09-19 07:00:31
     */
    @RequestMapping(value = "/flushCacheJob", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @SystemControllerLog(description = "刷新数据缓存", author = "roothomes", lastDate = "2019-09-11")
    public ResultData flushCacheJob(@RequestBody String json) {
        BasicSelectListCache entity = SpringBizUtils.getFormJSON(json, BasicSelectListCache.class);
        mgtSelectListService.flushSelectListCacheJob(entity, ThreadLocalUtil.getUserIdOrNull());
        return SpringBizUtils.getResultData(true, null, null);
    }

    /**
     * job刷新市场列表
     *
     * @param reqJsonData
     * @return
     */
    @RequestMapping(value = "/jobFlushBasicSelectList", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @SystemControllerLog(description = "job刷新市场列表", author = "roothomes", lastDate = "2019-09-11")
    public ResultData<JobReportDTO> jobFlushBasicSelectList(@RequestBody String reqJsonData) {
        JobRequestDTO obj = SpringBizUtils.getFormJSON(reqJsonData, JobRequestDTO.class);
        JobReportDTO rs = service.jobFlushSelectList(obj);
        return SpringBizUtils.getResultData(true, rs, "");
    }


    /**
     * 检查刷新市场job的结果
     *
     * @param reqJsonData
     * @return
     */
    @RequestMapping(value = "/checkFlushBasicSelectListJob", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @SystemControllerLog(description = "检查刷新市场job的结果", author = "roothomes", lastDate = "2019-09-11")
    public ResultData<JobReportDTO> checkFlushBasicSelectListJob(@RequestBody String reqJsonData) {
        JobRequestDTO obj = SpringBizUtils.getFormJSON(reqJsonData, JobRequestDTO.class);
        JobReportDTO rs = service.checkFlushSelectListJob(obj);
        return SpringBizUtils.getResultData(true, rs, "");
    }

}
