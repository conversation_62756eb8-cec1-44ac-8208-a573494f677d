#!/bin/bash
#pname=$1
pname=datadict-server-1.5.0-RELEASE.jar
puser=root

 
pid=`ps aux | grep $pname | grep $puser | grep -v grep | awk '{print $2}'`
 
if [[ -z $pid ]]; then
    echo "I can NOT find $pname running by $puser"
fi
 
kill -9 $pid >/dev/null 2>&1
exec nohup java -Xmx256m -Xss256k -jar $pname --spring.profiles.active=uat --cacheType=single   --logging.level.com.apec=DEBUG --eureka.instance.hostname=eureka.uat.mtmall.com:1111 5 >/dev/null 2>&1 &

