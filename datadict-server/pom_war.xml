<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.apec</groupId>
    <artifactId>datadict-server</artifactId>
    <version>1.7.0-RELEASE</version>
    <parent>
        <groupId>com.apec</groupId>
        <artifactId>APEC_CJ101_DataDict</artifactId>
        <version>1.5.0-RELEASE</version>
    </parent>

    <packaging>war</packaging>

    <description>
        1.7.0-RELEASE   2020-12-28  add shangxw
                            magpie-cb  apec-basic-* 1.5.0-RELEASE
                            magpie-cb-springcloud-util  1.1.0-RELEASE
        1.6.0-RELEASE   add shangxw  framework 1.5.0
                            apec-basic-*  1.0.0-RELEASE
    </description>
    <dependencies>
        <!--依赖项底层接口和模块 start -->
        <dependency>
            <groupId>com.apec</groupId>
            <artifactId>magpie-cb-springcloud-util</artifactId>
            <version>1.1.0-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.apec</groupId>
            <artifactId>magpie-cb-base-formjson-log</artifactId>
            <version>1.1.0-RELEASE</version>
        </dependency>
        <!--依赖项底层接口和模块 end -->
        <dependency>
            <groupId>com.apec</groupId>
            <artifactId>magpie-cb-rpc</artifactId>
            <version>1.6.0-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.apec</groupId>
            <artifactId>basic-spring-thread</artifactId>
            <version>1.0.0-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.apec</groupId>
            <artifactId>magpie-cb-base-rpc-shell</artifactId>
            <version>1.0.0-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.apec</groupId>
            <artifactId>apec-basic-datadict</artifactId>
            <version>1.6.1-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.apec</groupId>
            <artifactId>server-dto-pic</artifactId>
            <version>1.0.0-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.apec</groupId>
            <artifactId>apec-basic-systemtrademode</artifactId>
            <version>1.5.0-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.apec</groupId>
            <artifactId>base-datadict-dto</artifactId>
            <version>1.0.0-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.apec</groupId>
            <artifactId>base-datadict-push-dto</artifactId>
            <version>1.0.0-RELEASE</version>
        </dependency>


        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>2.10.2</version>
        </dependency>

        <!-- spring cloud依赖包 start -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-dbcp2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
            <version>${spring-boot-starter.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-eureka</artifactId>
            <version>${spring-boot-starter.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <version>${spring-boot-starter.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <version>${spring-boot-starter.version}</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>5.6.2</version>
            <scope>test</scope>
        </dependency>

        <!-- 去除一些tomcat不需要的依赖 -->
        <dependency>
            <groupId>org.eclipse.jetty.websocket</groupId>
            <artifactId>javax-websocket-client-impl</artifactId>
            <version>9.3.16.v20170120</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.eclipse.jetty.websocket</groupId>
            <artifactId>javax-websocket-server-impl</artifactId>
            <version>9.3.16.v20170120</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
            <version>2.6.2</version>
            <scope>provided</scope>
        </dependency>

    </dependencies>


    <!-- 打包方式 -->
    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <configuration>
                    <packagingExcludes>WEB-INF/lib/jetty-*.jar</packagingExcludes>
                    <warSourceExcludes>src/main/resources/**</warSourceExcludes>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>