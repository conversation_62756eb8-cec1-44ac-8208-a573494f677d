#!/bin/sh
current_path=`pwd`
jar_num=`ls -l |grep "jar$"|wc -l`
if [ "$jar_num" -lt 1 ];then
    echo 目录"$current_path"内无jar包，停止启动
    exit
fi
if [ "$jar_num" -gt 1 ];then
    echo 目录"$current_path" jar包数量过多:$jar_num，停止启动
    exit
fi
jar_name=`ls -l | grep 'jar$' | awk '{print $9}'`
jar_pid=`ps -ef |grep $jar_name | grep -v  grep | awk '{print $2}'`
if [ -z "$jar_pid" ];then
    echo 目录"$current_path" jar包:"$jar_name" 对应原进程ID不存在
else
    echo 目录"$current_path" jar包:"$jar_name" 对应原进程ID:"$jar_pid"
    `kill -9 $jar_pid`
    echo 杀死原进程"$jar_pid"
fi

