package com.apec.framework.jpa.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.*;

import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.domain.Persistable;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import com.apec.framework.common.enumtype.EnableFlag;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * 类 编 号：
 * 类 名 称：BaseModel
 * 内容摘要：框架基本实体类,所有的实体需集成该类
 * 完成日期：2018-01-16
 * 编码作者：
 */
@MappedSuperclass
@EntityListeners({AuditingEntityListener.class})
public abstract class BaseModel<PK extends Serializable> implements Persistable<PK>
{

    private static final long serialVersionUID = -2477722490039509121L;

    @Id
    @Column(name = "ID")
    private PK id;

    @Column(name = "STATUS")
    private String status = "1";

    @Enumerated(EnumType.STRING)
    @Column(name = "ENABLE_FLAG")
    private EnableFlag enableFlag = EnableFlag.Y;

    @Column(name = "CREATE_BY", updatable = false)
    private String createBy;

    @CreatedDate
    @Temporal(TemporalType.TIMESTAMP)
    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss", timezone = "GMT+8")
    @Column(name = "CREATE_DATE", updatable = false)
    private Date createDate;

    @Column(name = "LAST_UPDATE_BY")
    private String lastUpdateBy;

    @Column(name = "LAST_UPDATE_DATE")
    @LastModifiedDate
    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss", timezone = "GMT+8")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastUpdateDate;


    public void setId(PK id)
    {
        this.id = id;
    }

    @Override
    public PK getId()
    {
        return id;
    }

    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public EnableFlag getEnableFlag()
    {
        return enableFlag;
    }

    public void setEnableFlag(EnableFlag enableFlag)
    {
        this.enableFlag = enableFlag;
    }

    /**
     * @return the createBy
     */
    public String getCreateBy()
    {
        return createBy;
    }

    /**
     * @param createBy
     *            the createBy to set
     */
    public void setCreateBy(String createBy)
    {
        this.createBy = createBy;
    }

    /**
     * @return the createDate
     */
    public Date getCreateDate()
    {
        return createDate;
    }

    /**
     * @param createDate
     *            the createDate to set
     */
    public void setCreateDate(Date createDate)
    {
        this.createDate = createDate;
    }

    /**
     * @return the lastUpdateBy
     */
    public String getLastUpdateBy()
    {
        return lastUpdateBy;
    }

    /**
     * @param lastUpdateBy
     *            the lastUpdateBy to set
     */
    public void setLastUpdateBy(String lastUpdateBy)
    {
        this.lastUpdateBy = lastUpdateBy;
    }

    /**
     * @return the lastUpdateDate
     */
    public Date getLastUpdateDate()
    {
        return lastUpdateDate;
    }

    /**
     * @param lastUpdateDate
     *            the lastUpdateDate to set
     */
    public void setLastUpdateDate(Date lastUpdateDate)
    {
        this.lastUpdateDate = lastUpdateDate;
    }

    @Override
    @JsonIgnore
    public boolean isNew()
    {
        return null == this.id;
    }

}
