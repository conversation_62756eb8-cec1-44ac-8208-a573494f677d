//package com.apec.framework.jpa.common;
//
//import java.util.Objects;
//
//import org.springframework.data.domain.AuditorAware;
//
//import com.apec.framework.common.dto.UserBasicInfo;
//import com.apec.framework.session.service.SessionService;
//
///**
// * 类 编 号：
// * 类 名 称：
// * 内容摘要：
// * 创建日期：2017/10/19
// * 编码作者：hcl
// */
//public class DbAuditorAware implements AuditorAware<String>
//{
//    private SessionService sessionService;
//
//    public DbAuditorAware(SessionService sessionService)
//    {
//        this.sessionService = sessionService;
//    }
//
//    @Override
//    public String getCurrentAuditor()
//    {
//        UserBasicInfo userBasicInfo = sessionService.getUserBasicInfo();
//        if(Objects.nonNull(userBasicInfo))
//        {
//            return sessionService.getUserBasicInfo().getId();
//        }
//        return "";
//    }
//}
