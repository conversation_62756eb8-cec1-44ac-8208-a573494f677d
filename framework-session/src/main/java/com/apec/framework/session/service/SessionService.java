package com.apec.framework.session.service;


import com.apec.framework.common.dto.UserBasicInfo;

/**
 * 类 编 号：
 * 类 名 称：SessionService
 * 内容摘要：
 * 创建日期：2017/4/15 19:01
 * 编码作者：zhaolei
 */
public interface SessionService {

    String userIdentity();

    /**
     * 获取用户基本信息
     * @return UserBasicInfo
     */
    UserBasicInfo getUserBasicInfo();

    /**
     * 暂时不开放,请通过调用客户服务获取User
     * @param userIdentity  userIdentity
     * @return String
     */
    String getUser(String userIdentity);
}
