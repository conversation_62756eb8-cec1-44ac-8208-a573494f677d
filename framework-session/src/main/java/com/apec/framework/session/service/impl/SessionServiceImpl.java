package com.apec.framework.session.service.impl;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import com.apec.framework.common.dto.UserBasicInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.apec.framework.cache.CacheService;
import com.apec.framework.common.util.JsonUtils;
import com.apec.framework.session.bean.SessionConstant;
import com.apec.framework.session.service.SessionService;
import com.google.common.base.Strings;

/**
 * 类 编 号：
 * 类 名 称：
 * 内容摘要：
 * 创建日期：2017/4/15 19:02
 * 编码作者：zhaolei
 */
@Service
public class SessionServiceImpl implements SessionService
{
    private static Logger logger = LoggerFactory.getLogger(SessionServiceImpl.class);

    @Autowired
    CacheService cacheService;

    @Override
    public String userIdentity()
    {
        return loginUser(SessionConstant.SESSION_LOGGED_IN_USER_ID);
    }

    @Override
    public UserBasicInfo getUserBasicInfo()
    {
        String user = loginUser(SessionConstant.SESSION_LOGGED_IN_USER_BASICINFO);
        UserBasicInfo userBasicInfo = null;
        try
        {
            userBasicInfo = JsonUtils.parseObject(user, UserBasicInfo.class);
        }
        catch (Exception e)
        {
            logger.error(" //// Can't parse User!\nlonged in user:" + user, e);
        }

        return userBasicInfo;
    }

    /**
     * 暂时不开放,请通过调用客户服务获取User
     * @param userIdentity
     * @return
     */
    @Override
    public String getUser(String userIdentity)
    {
        String key = SessionConstant.CACHE_LOGGED_IN_USER_PREFIX + userIdentity;
        return cacheService.get(key);
    }

    private String loginUser(String key)
    {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes)RequestContextHolder
            .getRequestAttributes();
        HttpServletRequest request = requestAttributes.getRequest();
        HttpSession session = request.getSession(false);
        String user = "";
        if(null != session)
        {
            logger.debug(" //// Get logged in user from session,session id : " + session.getId());
            if(null != session.getAttribute(key)){
                user = session.getAttribute(key).toString();
            }
        }

        if(Strings.isNullOrEmpty(user))
        {
            logger.debug(" //// http session is null, get user from HttpServletRequest Header.");
            user = request.getHeader(key);
        }

        if(Strings.isNullOrEmpty(user))
        {
            logger.debug(" //// Can't get user from request header, get user from HttpServletRequest Parameter.");
            user = request.getParameter(key);
        }

        return user;
    }
}
