package com.apec.datadict.vo;


import java.lang.*;
import java.util.*;
import com.apec.framework.common.dto.BaseDTO;
import com.apec.framework.common.enumtype.EnableFlag;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import com.apec.framework.common.util.JsonUtils;
import com.alibaba.fastjson.JSONObject;
import com.apec.magpie.cb.dto.SortAttrDTO;

/**
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BasicDataDictVo extends BaseDTO
{
    private static final long serialVersionUID = 1L;
    /**
     * 交易标志
     */
    private String exchangeId;
    /**
     * 子系统代码
     */
    private String subSystemCode;
    /**
     * 字典编码
     */
    private String dictCode;
    /**
     * 数据字典的值
     */
    private String dictValue;
    /**
     * 字典英文值
     */
    private String dictEnValue;
    /**
     * 字典类型  1-系统字典，2-业务字典
     */
    private String dictType;
    /**
     * 字典维护级别  0-运维，1-业务
     */
    private String dictLevel;
    /**
     * 字典启用状态 1-启用 0-禁用
     */
    private String dictStatus;
    /**
     * 图片
     */
    private String img;
    /**
     * 图片id
     */
    private String imgId;
    /**
     * 子系统业务类型
     */
    private String subSystemBizType;
    /**
     * 主键id
     */
    private String id;
    /**
     * 状态
     */
    private String status;
    /**
     * 逻辑删除
     */
    private EnableFlag enableFlag;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 最后修改人
     */
    private String lastUpdateBy;
    /**
     * 最后修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdateDate;
    /**
     * 平台编号
     */
    private String plantformId;
    /**
     * 组织编码
     */
    private String oecdNo;
    /**
     * 城市id
     */
    private Integer cityId;
    /**
     * 排序
     */
    private Integer orderNumber;
    /**
     * 备注
     */
    private String remarks;

    /**
     * 创建时间 (开始时间)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginCreateDate;

    /**
     * 创建时间 (结束时间)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endCreateDate;
    /**
     * 最后修改时间 (开始时间)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginLastUpdateDate;

    /**
     * 最后修改时间 (结束时间)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endLastUpdateDate;

    /**
     * 使用id集合查询
     */
    private List<String> ids;

    /**
     * 确定字段是全匹配还是模糊匹配，likeJson里面的值 key=属性名称，v=boolean(是like)
     */
    private JSONObject likeJson;

    /**
     * 排序集合
     */
    private List<SortAttrDTO> sortList;

    @Override
    public String toString() {
        return JsonUtils.toJSONString(this);
    }
}
