package com.apec.datadict.model;


import java.lang.*;
import com.apec.framework.common.constant.FrameConsts;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Column;
import com.apec.framework.common.util.JsonUtils;
import org.hibernate.annotations.DynamicUpdate;

import com.apec.framework.jpa.model.MySQLBaseModel;

@Data
@Entity
@DynamicUpdate
@GenericGenerator(name = FrameConsts.SYSTEM_GENERATOR, strategy = FrameConsts.ASSIGNED)
@Table(name = "basic_data_dict")

/**
 */
public class BasicDataDict extends MySQLBaseModel<String>
{
    private static final long serialVersionUID = 1L;


    /**
     * 交易标志
     */
    @Column(name = "`exchange_id`", columnDefinition = "varchar(64)  COMMENT '交易标志'")
    private String exchangeId;

    /**
     * 子系统代码
     */
    @Column(name = "`sub_system_code`", columnDefinition = "varchar(64)  COMMENT '子系统代码'")
    private String subSystemCode;

    /**
     * 字典编码
     */
    @Column(name = "`dict_code`", columnDefinition = "varchar(64)  COMMENT '字典编码'")
    private String dictCode;

    /**
     * 数据字典的值
     */
    @Column(name = "`dict_value`", columnDefinition = "varchar(64)  COMMENT '数据字典的值'")
    private String dictValue;

    /**
     * 字典英文值
     */
    @Column(name = "`dict_en_value`", columnDefinition = "varchar(64)  COMMENT '字典英文值'")
    private String dictEnValue;

    /**
     * 字典类型  1-系统字典，2-业务字典
     */
    @Column(name = "`dict_type`", columnDefinition = "varchar(64)  COMMENT '字典类型  1-系统字典，2-业务字典'")
    private String dictType;

    /**
     * 字典维护级别  0-运维，1-业务
     */
    @Column(name = "`dict_level`", columnDefinition = "varchar(64)  COMMENT '字典维护级别  0-运维，1-业务'")
    private String dictLevel;

    /**
     * 字典启用状态 1-启用 0-禁用
     */
    @Column(name = "`dict_status`", columnDefinition = "varchar(64)  COMMENT '字典启用状态 1-启用 0-禁用'")
    private String dictStatus;

    /**
     * 图片
     */
    @Column(name = "`img`", columnDefinition = "varchar(1024)  COMMENT '图片'")
    private String img;

    /**
     * 图片id
     */
    @Column(name = "`img_id`", columnDefinition = "varchar(64)  COMMENT '图片id'")
    private String imgId;

    /**
     * 子系统业务类型
     */
    @Column(name = "`sub_system_biz_type`", columnDefinition = "varchar(64)  COMMENT '子系统业务类型'")
    private String subSystemBizType;



    @Override
    public String toString() {
        return JsonUtils.toJSONString(this);
    }
}
