package com.apec.datadict.constants;

import java.lang.*;
import java.util.*;

import com.apec.magpie.cb.constant.IConts;

/**
 */
public interface BasicDataDictContant extends IConts {

    /** 交易标志 */
    String  DEFAULT_EXCHANGEID_VAL = "1";
    /** 子系统代码 */
    String  DEFAULT_SUBSYSTEMCODE_VAL = "0";
    /** 字典编码 */
    String  DEFAULT_DICTCODE_VAL = "";
    /** 数据字典的值 */
    String  DEFAULT_DICTVALUE_VAL = "";
    /** 字典英文值 */
    String  DEFAULT_DICTENVALUE_VAL = "";
    /** 字典类型  1-系统字典，2-业务字典 */
    String  DEFAULT_DICTTYPE_VAL = "";
    /** 字典维护级别  0-运维，1-业务 */
    String  DEFAULT_DICTLEVEL_VAL = "0";
    /** 字典启用状态 1-启用 0-禁用 */
    String  DEFAULT_DICTSTATUS_VAL = "1";
    /** 图片 */
    String  DEFAULT_IMG_VAL = "";
    /** 图片id */
    String  DEFAULT_IMGID_VAL = "";
    /** 子系统业务类型 */
    String  DEFAULT_SUBSYSTEMBIZTYPE_VAL = "";


    /**
     * 常量属性,模型名称(BasicDataDict) .属性 exchangeId
     */
    String C_BASICDATADICT_ATTRNAME_EXCHANGEID = "exchangeId";
    /**
     * 常量属性,模型名称(BasicDataDict) .属性 subSystemCode
     */
    String C_BASICDATADICT_ATTRNAME_SUBSYSTEMCODE = "subSystemCode";
    /**
     * 常量属性,模型名称(BasicDataDict) .属性 dictCode
     */
    String C_BASICDATADICT_ATTRNAME_DICTCODE = "dictCode";
    /**
     * 常量属性,模型名称(BasicDataDict) .属性 dictValue
     */
    String C_BASICDATADICT_ATTRNAME_DICTVALUE = "dictValue";
    /**
     * 常量属性,模型名称(BasicDataDict) .属性 dictEnValue
     */
    String C_BASICDATADICT_ATTRNAME_DICTENVALUE = "dictEnValue";
    /**
     * 常量属性,模型名称(BasicDataDict) .属性 dictType
     */
    String C_BASICDATADICT_ATTRNAME_DICTTYPE = "dictType";
    /**
     * 常量属性,模型名称(BasicDataDict) .属性 dictLevel
     */
    String C_BASICDATADICT_ATTRNAME_DICTLEVEL = "dictLevel";
    /**
     * 常量属性,模型名称(BasicDataDict) .属性 dictStatus
     */
    String C_BASICDATADICT_ATTRNAME_DICTSTATUS = "dictStatus";
    /**
     * 常量属性,模型名称(BasicDataDict) .属性 img
     */
    String C_BASICDATADICT_ATTRNAME_IMG = "img";
    /**
     * 常量属性,模型名称(BasicDataDict) .属性 imgId
     */
    String C_BASICDATADICT_ATTRNAME_IMGID = "imgId";
    /**
     * 常量属性,模型名称(BasicDataDict) .属性 subSystemBizType
     */
    String C_BASICDATADICT_ATTRNAME_SUBSYSTEMBIZTYPE = "subSystemBizType";

}
