package com.apec.datadict.web;

import java.util.*;

import com.apec.datadict.model.BasicDataDict;
import com.apec.datadict.dto.BasicDataDictDTO;
import com.apec.datadict.vo.BasicDataDictVo;
import com.apec.datadict.constants.BasicDataDictContant;
import com.apec.datadict.dto.BasicDataDictQueryDTO;
import com.apec.datadict.service.BasicDataDictService;
import java.lang.*;
import com.apec.framework.common.enumtype.EnableFlag;
import com.apec.framework.common.dto.PageDTO;
import com.apec.framework.common.exception.ApecRuntimeException;
import com.apec.framework.common.model.PageJSON;
import com.apec.framework.common.model.ResultData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import java.util.*;
import java.util.stream.*;
import com.apec.framework.common.util.SpringBizUtils;
import com.apec.magpie.cb.common.utils.SortUtils;
import com.apec.aops.ThreadLocalUtil;

import com.apec.framework.common.util.BeanUtils;
import com.apec.magpie.cb.constant.IConts;
import com.apec.annotations.SystemControllerLog;

/**
 */

@RestController
@RequestMapping("/basicDataDict")
public class BasicDataDictController implements IConts {

	@Autowired
	BasicDataDictService service;

	/**
	 * 添加数据
	 * @param json JSON请求参数
	 * @return
	 */
	@SystemControllerLog(code = "basicdatadict.save",description = "添加数据",author = "rs",lastDate = "2020-11-20")
	@RequestMapping(value = "/save", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
		public ResultData save(@RequestBody String json) {
		BasicDataDictDTO dto = SpringBizUtils.getFormJSON(json, BasicDataDictDTO.class); 
		BasicDataDictDTO data = service.save(dto, ThreadLocalUtil.getUserId());
		return SpringBizUtils.getResultData(true, data, null);
	}


	/**
	 * 根据主键id逻辑删除
	 * @param json JSON请求参数
	 * @return
	 */
	@SystemControllerLog(code = "basicdatadict.delete",description = "根据主键id逻辑删除",author = "rs",lastDate = "2020-11-20")
	@RequestMapping(value = "/delete", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	public ResultData delete(@RequestBody String json) {
		BasicDataDictDTO obj = SpringBizUtils.getFormJSON(json, BasicDataDictDTO.class); 
		BasicDataDict data = service.delete(obj.getId(), ThreadLocalUtil.getUserId());
		return SpringBizUtils.getResultData(true, data, null);
	}


	/**
	 * 根据主键id物理删除
	 * @param json JSON请求参数
	 * @return
	 */
	@SystemControllerLog(code = "basicdatadict.deleteDB",description = "根据主键id物理删除",author = "rs",lastDate = "2020-11-20")
	@RequestMapping(value = "/deleteDB", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	public ResultData deleteDB(@RequestBody String json) {
		BasicDataDictDTO vo = SpringBizUtils.getFormJSON(json, BasicDataDictDTO.class); 
		Boolean data = service.deleteDB(vo.getId(), ThreadLocalUtil.getUserId());
		return SpringBizUtils.getResultData(true, data, null);
	}


	/**
	 * 根据主键id更新模型
	 * @param json JSON请求参数
	 * @return
	 */
	@SystemControllerLog(code = "basicdatadict.update",description = "根据主键id更新模型",author = "rs",lastDate = "2020-11-20")
	@RequestMapping(value = "/update", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	public ResultData update(@RequestBody String json) {
		BasicDataDict obj = SpringBizUtils.getFormJSON(json, BasicDataDict.class); 
		BasicDataDict data = service.update(obj, ThreadLocalUtil.getUserId());
		return SpringBizUtils.getResultData(true, data, null);
	}

	/**
	 * 根据主键id更新模型
	 * @param json JSON请求参数
	 * @return
	 */
	@SystemControllerLog(code = "basicdatadict.updateDTO",description = "根据主键id更新模型",author = "rs",lastDate = "2020-11-20")
	@RequestMapping(value = "/updateDTO", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	public ResultData updateDTO(@RequestBody String json) {
		BasicDataDictDTO obj = SpringBizUtils.getFormJSON(json, BasicDataDictDTO.class); 
		BasicDataDictDTO data = service.updateDTO(obj, ThreadLocalUtil.getUserId());
		return SpringBizUtils.getResultData(true, data, null);
	}


	/**
 	 * 根据主键id更新模型状态
	 * @param json JSON请求参数
	 * @return
	 */
	@SystemControllerLog(code = "basicdatadict.updateStatus",description = "根据主键id更新模型状态",author = "rs",lastDate = "2020-11-20")
	@RequestMapping(value = "/updateStatus", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	public ResultData updateStatus(@RequestBody String json) {
		BasicDataDict obj = SpringBizUtils.getFormJSON(json, BasicDataDict.class); 
		BasicDataDict data = service.updateStatus(obj.getId(),obj.getStatus(), ThreadLocalUtil.getUserId());
		return SpringBizUtils.getResultData(true, data, null);
	}

	/**
	 * 根据主键id更新模型排序值
	 * @param json JSON请求参数
	 * @return
	 */
	@SystemControllerLog(code = "basicdatadict.updateOrderNumber",description = "根据主键id更新模型排序值",author = "rs",lastDate = "2020-11-20")
	@RequestMapping(value = "/updateOrderNumber", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	public ResultData updateOrderNumber(@RequestBody String json) {
		BasicDataDict obj = SpringBizUtils.getFormJSON(json, BasicDataDict.class); 
		BasicDataDict data = service.updateOrderNumber(obj.getId(),obj.getOrderNumber(), ThreadLocalUtil.getUserId());
		return SpringBizUtils.getResultData(true, data, null);
	}


	/**
	 * 根据主键id查询模型
	 * @param json JSON请求参数
	 * @return
	 */
	@SystemControllerLog(code = "basicdatadict.find",description = "根据主键id查询模型",author = "rs",lastDate = "2020-11-20")
	@RequestMapping(value = "/find", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	public ResultData selectById(@RequestBody String json) {
		BasicDataDict obj = SpringBizUtils.getFormJSON(json, BasicDataDict.class); 
		BasicDataDict data = service.find(obj.getId(), ThreadLocalUtil.getUserId());
		return SpringBizUtils.getResultData(true, data, null);
	}



	/**
	 * 根据主键id查询模型
	 * @param json JSON请求参数
	 * @return
	 */
	@SystemControllerLog(code = "basicdatadict.findDTO",description = "根据主键id查询模型",author = "rs",lastDate = "2020-11-20")
	@RequestMapping(value = "/findDTO", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	public ResultData findDTO(@RequestBody String json) {
		BasicDataDictDTO dto = SpringBizUtils.getFormJSON(json, BasicDataDictDTO.class); 
		BasicDataDictDTO data = service.findDTO(dto.getId(), ThreadLocalUtil.getUserId());
		return SpringBizUtils.getResultData(true, data, null);
	}

	/**
	 * 查询集合(基础模型)
	 * @param json JSON请求参数
	 * @return
	 */
	@SystemControllerLog(code = "basicdatadict.findList",description = "查询集合(基础模型)",author = "rs",lastDate = "2020-11-20")
	@RequestMapping(value = "/findList", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	public ResultData findList(@RequestBody String json) {
		BasicDataDictDTO dto = SpringBizUtils.getFormJSON(json, BasicDataDictDTO.class); 
		List<BasicDataDict> data = service.findList(dto, ThreadLocalUtil.getUserId());
		return SpringBizUtils.getResultData(true, data, null);
	}




	/**
	 * 查询集合(扩展模型)
	 * @param json JSON请求参数
	 * @return
	 */
	@SystemControllerLog(code = "basicdatadict.findListExt",description = "查询集合(扩展模型)",author = "rs",lastDate = "2020-11-20")
	@RequestMapping(value = "/findListExt", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
	public ResultData findListExt(@RequestBody String json) {
	    BasicDataDictQueryDTO obj = SpringBizUtils.getFormJSON(json,BasicDataDictQueryDTO.class);
		List<BasicDataDictDTO>  data = service.findListExt(obj, ThreadLocalUtil.getUserId());
		return SpringBizUtils.getResultData(true, data, null);
	}

	/**
	 * 分页查询模型列表
	 * @param json JSON请求参数
	 * @return
	 */
	@SystemControllerLog(code = "basicdatadict.findByPage",description = "分页查询模型列表",author = "rs",lastDate = "2020-11-20")
	@RequestMapping(value = "/findByPage", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	public ResultData findByPage(@RequestBody String json) {
		BasicDataDictDTO dto = SpringBizUtils.getFormJSON(json, BasicDataDictDTO.class);
	    PageRequest pageRequest = SortUtils.genPageRequest(dto);
		PageDTO<BasicDataDictDTO> data = service.findByPageDTO(dto, pageRequest, ThreadLocalUtil.getUserId());
		return SpringBizUtils.getResultData(true, data, null);
	}


	/**
	 * 分页
	 * @param json JSON请求参数
	 * @return
	 */
	@SystemControllerLog(code = "basicdatadict.findByPageExt",description = "分页查询模型列表",author = "rs",lastDate = "2020-11-20")
	@RequestMapping(value = "/findByPageExt", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
	public ResultData findByPageExt(@RequestBody String json) {
		BasicDataDictQueryDTO dto =  SpringBizUtils.getFormJSON(json, BasicDataDictQueryDTO.class);
	    PageRequest request = SortUtils.genPageRequest(dto);
		PageDTO<BasicDataDictDTO> data = service.findByPageExt(dto, request, ThreadLocalUtil.getUserId());
		return SpringBizUtils.getResultData(true, data, null);
	}
}
