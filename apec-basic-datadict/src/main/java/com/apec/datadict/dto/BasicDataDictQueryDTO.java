package com.apec.datadict.dto;


import java.util.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import com.apec.framework.common.util.JsonUtils;

import lombok.EqualsAndHashCode;

/**
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class BasicDataDictQueryDTO extends BasicDataDictDTO
{
    /**
     * 创建 (开始时间)
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date beginCreateDate;
    /**
     * 创建 (结束时间)
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date endCreateDate;
    /**
     * 更新 (开始时间)
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date beginUpdateDate;
    /**
     * 更新 (结束时间)
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date endUpdateDate;


    private List<String> statusLS;
    private List<String> plantformIdLS;
    private List<String> oecdNoLS;


    /**
     * 交易标志 (集合查询)
     */
    private List<String> exchangeIdLS;
    /**
     * 子系统代码 (集合查询)
     */
    private List<String> subSystemCodeLS;
    /**
     * 字典编码 (集合查询)
     */
    private List<String> dictCodeLS;
    /**
     * 数据字典的值 (集合查询)
     */
    private List<String> dictValueLS;
    /**
     * 字典英文值 (集合查询)
     */
    private List<String> dictEnValueLS;
    /**
     * 字典类型  1-系统字典，2-业务字典 (集合查询)
     */
    private List<String> dictTypeLS;
    /**
     * 字典维护级别  0-运维，1-业务 (集合查询)
     */
    private List<String> dictLevelLS;
    /**
     * 字典启用状态 1-启用 0-禁用 (集合查询)
     */
    private List<String> dictStatusLS;
    /**
     * 图片 (集合查询)
     */
    private List<String> imgLS;
    /**
     * 图片id (集合查询)
     */
    private List<String> imgIdLS;
    /**
     * 子系统业务类型 (集合查询)
     */
    private List<String> subSystemBizTypeLS;

    @Override
    public String toString() {
        return JsonUtils.toJSONString(this);
    }
}
