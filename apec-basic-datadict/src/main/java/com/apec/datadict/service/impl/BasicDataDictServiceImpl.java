package com.apec.datadict.service.impl;

import com.apec.datadict.model.BasicDataDict;
import com.apec.datadict.dto.BasicDataDictDTO;
import com.apec.datadict.vo.BasicDataDictVo;
import com.apec.datadict.constants.BasicDataDictContant;
import com.apec.datadict.dto.BasicDataDictQueryDTO;
import java.lang.*;
import java.util.*;
import java.util.stream.*;
import com.apec.framework.common.dto.PageDTO;
import com.apec.framework.common.enumtype.EnableFlag;
import com.apec.framework.common.exception.ApecRuntimeException;
import com.apec.framework.common.tools.IDGenerator;
import com.apec.framework.common.util.BeanUtils;
import com.apec.framework.common.util.DateUtils;
import org.apache.commons.lang.*;
import com.querydsl.core.types.ExpressionUtils;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.Predicate;
import com.apec.framework.common.util.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Sort;
import com.apec.cache.base.CacheService;
import com.apec.datadict.service.BasicDataDictService;
import com.apec.datadict.dao.BasicDataDictDAO;
import com.apec.datadict.model.QBasicDataDict;
import com.alibaba.fastjson.JSONObject;
import com.apec.framework.common.constant.ErrorCodeConsts;
import org.apache.commons.lang3.StringUtils;
import com.apec.magpie.cb.dto.SortAttrDTO;
import com.apec.magpie.cb.enums.EnumDirection;
import org.apache.commons.collections.CollectionUtils;
import com.apec.magpie.cb.common.utils.SortUtils;
import javax.annotation.Resource;

/**
 */
@Service
public class BasicDataDictServiceImpl implements BasicDataDictService,BasicDataDictContant
{

    private final Logger LOG =  LoggerFactory.getLogger( getClass());

    @Autowired
    private BasicDataDictDAO dao;

    @Resource
    private CacheService cacheService;

	
    @Override
    @Transactional(rollbackFor=Exception.class)
    public BasicDataDictDTO save(BasicDataDictDTO dto,String userId)
        throws ApecRuntimeException
    {

        if(StringUtils.isBlank(userId)){
            throw new ApecRuntimeException(ErrorCodeConsts.COMMON_MISSING_PARAMS,Arrays.asList("userId"));
        }
        if (StringUtils.isBlank(dto.getExchangeId())) {
            dto.setExchangeId(DEFAULT_EXCHANGEID_VAL );
        }
        if (StringUtils.isBlank(dto.getSubSystemCode())) {
            dto.setSubSystemCode(DEFAULT_SUBSYSTEMCODE_VAL );
        }
        if (StringUtils.isBlank(dto.getDictCode())) {
            dto.setDictCode(DEFAULT_DICTCODE_VAL );
        }
        if (StringUtils.isBlank(dto.getDictValue())) {
            dto.setDictValue(DEFAULT_DICTVALUE_VAL );
        }
        if (StringUtils.isBlank(dto.getDictEnValue())) {
            dto.setDictEnValue(DEFAULT_DICTENVALUE_VAL );
        }
        if (StringUtils.isBlank(dto.getDictType())) {
            dto.setDictType(DEFAULT_DICTTYPE_VAL );
        }
        if (StringUtils.isBlank(dto.getDictLevel())) {
            dto.setDictLevel(DEFAULT_DICTLEVEL_VAL );
        }
        if (StringUtils.isBlank(dto.getDictStatus())) {
            dto.setDictStatus(DEFAULT_DICTSTATUS_VAL );
        }
        if (StringUtils.isBlank(dto.getImg())) {
            dto.setImg(DEFAULT_IMG_VAL );
        }
        if (StringUtils.isBlank(dto.getImgId())) {
            dto.setImgId(DEFAULT_IMGID_VAL );
        }
        if (StringUtils.isBlank(dto.getSubSystemBizType())) {
            dto.setSubSystemBizType(DEFAULT_SUBSYSTEMBIZTYPE_VAL );
        }

        //基础字段默认值
        if (null == dto.getCityId()) {
            dto.setCityId(DEFAULT_CITY_ID);
        }
        if (StringUtils.isBlank(dto.getStatus())) {
            dto.setStatus(STATUS_INVALID);
        }
        if (StringUtils.isBlank(dto.getPlantformId())) {
            dto.setPlantformId(DEFAULT_PLANTFORM_ID);
        }
        if (StringUtils.isBlank(dto.getOecdNo())) {
            dto.setOecdNo(DEFAULT_OECD_NO);
        }
        if (null == dto.getEnableFlag()) {
            dto.setEnableFlag(EnableFlag.Y);
        }
        if (null == dto.getCreateDate()) {
            dto.setCreateDate(new Date());
        }
        if (StringUtils.isBlank(dto.getCreateBy())) {
            dto.setCreateBy(userId);
        }
        if (null == dto.getOrderNumber()) {
            dto.setOrderNumber(1);
        }

        //属性拷贝保存到数据库刷新缓存
        BasicDataDict entity = new BasicDataDict();
        BeanUtils.copyPropertiesIgnoreNullFilds(dto, entity, new String[] {});

        if(StringUtils.isBlank(entity.getId())){
            entity.setId(IDGenerator.getNextId().toString());
        }

        dto.setId(entity.getId());
        dao.save(entity);
        return dto;
    }

    @Override
    public BasicDataDict delete(String id,String userId)
        throws ApecRuntimeException
    {
        if(StringUtils.isBlank(id) || StringUtils.isBlank(userId)){
            throw new ApecRuntimeException(ErrorCodeConsts.COMMON_MISSING_PARAMS,Arrays.asList("id userId"));
        }
        LOG.info("{},逻辑删除数据 id:{}",userId,id);
        BasicDataDict entity = dao.findOne(id);
        if(null == entity){
            throw new ApecRuntimeException(ErrorCodeConsts.ERROR_QUERY_DATA_NOT_EXIST_BY_ID);
        }
        entity.setEnableFlag(EnableFlag.N);

        if(StringUtils.isNotBlank(userId)){
            entity.setLastUpdateBy(userId);
        }
        entity.setLastUpdateDate(new Date());

        BasicDataDict rs = dao.saveAndFlush(entity);
        return rs;
    }

    @Override
    public boolean deleteDB(String id,String userId)
        throws ApecRuntimeException
    {
        if(StringUtils.isBlank(id) || StringUtils.isBlank(userId)){
            throw new ApecRuntimeException(ErrorCodeConsts.COMMON_MISSING_PARAMS,Arrays.asList("id userId"));
        }
        LOG.info("{},物理删除数据 id:{}",userId,id);
        BasicDataDict entity = dao.findOne(id);
        if(null == entity){
            throw new ApecRuntimeException(ErrorCodeConsts.ERROR_QUERY_DATA_NOT_EXIST_BY_ID);
        }
        LOG.info("删除的物理数据详情:{}",JsonUtils.toJSONString(entity));
        dao.delete(id);
        return true;
    }

    @Override
    public  BasicDataDict updateOrderNumber(String id, Integer orderNumber,String userId)
        throws ApecRuntimeException
    {
        if(StringUtils.isBlank(id) || null == orderNumber || StringUtils.isBlank(userId) ){
            throw new ApecRuntimeException(ErrorCodeConsts.COMMON_MISSING_PARAMS, Arrays.asList("id orderNumber userId"));
        }
        BasicDataDict entity = dao.findOne(id);
        entity.setOrderNumber(orderNumber);
        if(StringUtils.isNotBlank(userId)){
            entity.setLastUpdateBy(userId);
        }
        entity.setLastUpdateDate(new Date());
        dao.saveAndFlush(entity);
        return entity;
    }

    @Override
    public  BasicDataDict updateStatus(String id, String status,String userId)
        throws ApecRuntimeException
    {
        if(StringUtils.isBlank(id) || StringUtils.isBlank(status)
            || StringUtils.isBlank(userId)){
            throw new ApecRuntimeException(ErrorCodeConsts.COMMON_MISSING_PARAMS, Arrays.asList("id status userId"));
        }
        BasicDataDict entity = dao.findOne(id);
        entity.setStatus(status);
        if(StringUtils.isNotBlank(userId)){
            entity.setLastUpdateBy(userId);
        }
        entity.setLastUpdateDate(new Date());
        dao.saveAndFlush(entity);
        return entity;
    }

    @Override
    public BasicDataDict update(BasicDataDict obj,String userId)
        throws ApecRuntimeException
    {
        if(null == obj || StringUtils.isBlank(userId)){
            throw new ApecRuntimeException(ErrorCodeConsts.COMMON_MISSING_PARAMS, Arrays.asList("obj userId"));
        }
        if(StringUtils.isBlank(obj.getId())){
            throw new ApecRuntimeException(ErrorCodeConsts.ERROR_QUERY_DATA_NOT_EXIST_BY_ID);
        }

        BasicDataDict entity = dao.findOne(obj.getId());
        if(null == entity){
			LOG.error("查询数据为空,id={}",obj.getId());
			return null;
        }
		//模块属性设置
        if (StringUtils.isNotBlank(obj.getExchangeId())) {
			entity.setExchangeId(obj.getExchangeId());
        }
        if (StringUtils.isNotBlank(obj.getSubSystemCode())) {
			entity.setSubSystemCode(obj.getSubSystemCode());
        }
        if (StringUtils.isNotBlank(obj.getDictCode())) {
			entity.setDictCode(obj.getDictCode());
        }
        if (StringUtils.isNotBlank(obj.getDictValue())) {
			entity.setDictValue(obj.getDictValue());
        }
        if (StringUtils.isNotBlank(obj.getDictEnValue())) {
			entity.setDictEnValue(obj.getDictEnValue());
        }
        if (StringUtils.isNotBlank(obj.getDictType())) {
			entity.setDictType(obj.getDictType());
        }
        if (StringUtils.isNotBlank(obj.getDictLevel())) {
			entity.setDictLevel(obj.getDictLevel());
        }
        if (StringUtils.isNotBlank(obj.getDictStatus())) {
			entity.setDictStatus(obj.getDictStatus());
        }
        if (StringUtils.isNotBlank(obj.getImg())) {
			entity.setImg(obj.getImg());
        }
        if (StringUtils.isNotBlank(obj.getImgId())) {
			entity.setImgId(obj.getImgId());
        }
        if (StringUtils.isNotBlank(obj.getSubSystemBizType())) {
			entity.setSubSystemBizType(obj.getSubSystemBizType());
        }

        //基础属性设置
        if (StringUtils.isNotBlank(obj.getId())) {
            entity.setId(obj.getId());
        }
        if (StringUtils.isNotBlank(obj.getStatus())) {
            entity.setStatus(obj.getStatus());
        }
        if (null != obj.getEnableFlag()) {
            entity.setEnableFlag(obj.getEnableFlag());
        }
        if (null != obj.getCreateDate()) {
            entity.setCreateDate(obj.getCreateDate());
        }
        if (StringUtils.isNotBlank(obj.getCreateBy())) {
            entity.setCreateBy(obj.getCreateBy());
        }
        if (StringUtils.isNotBlank(obj.getLastUpdateBy())) {
            entity.setLastUpdateBy(obj.getLastUpdateBy());
        }
        if (null != obj.getLastUpdateDate()) {
            entity.setLastUpdateDate(obj.getLastUpdateDate());
        }
        if (StringUtils.isNotBlank(obj.getPlantformId())) {
            entity.setPlantformId(obj.getPlantformId());
        }
        if (StringUtils.isNotBlank(obj.getOecdNo())) {
            entity.setOecdNo(obj.getOecdNo());
        }
        if (null != obj.getCityId()) {
            entity.setCityId(obj.getCityId());
        }
        if (null != obj.getOrderNumber()) {
            entity.setOrderNumber(obj.getOrderNumber());
        }
        if (StringUtils.isNotBlank(obj.getRemarks())) {
            entity.setRemarks(obj.getRemarks());
        }

        if(StringUtils.isBlank(entity.getLastUpdateBy())) {
            entity.setLastUpdateBy(userId);
        }
        if(null == entity.getLastUpdateDate()) {
            entity.setLastUpdateDate(new Date());
        }

        dao.saveAndFlush(entity);
        return entity;
    }

    @Override
    public BasicDataDictDTO updateDTO(BasicDataDictDTO dto,String userId)
    throws ApecRuntimeException
    {
        BasicDataDict po = update(convertDtoToPo(dto),userId);
        return convertPoToDto(po);
    }

    @Override
    public BasicDataDict find(String id,String userId)
        throws ApecRuntimeException
    {
        BasicDataDict entity = dao.findOne(id);
        return entity;
    }

    @Override
    public BasicDataDictDTO findDTO(String id,String userId)
        throws ApecRuntimeException
    {
        BasicDataDict entity = dao.findOne(id);
        return convertPoToDto(entity);
    }


    @Override
    public PageDTO<BasicDataDictDTO> findByPageDTO(BasicDataDictDTO dto, PageRequest pageRequest,String userId)
        throws ApecRuntimeException
    {
        PageRequest sortPageRequest = new PageRequest(pageRequest.getPageNumber(),pageRequest.getPageSize(),
        SortUtils.getSortDirection(dto.getSortList()),SortUtils.getSortAttr(dto.getSortList()));
        Page<BasicDataDict> page = dao.findAll(getInputCondition(dto), sortPageRequest);
        return convertPoToDtoByPage(page);
    }

    @Override
    public PageDTO<BasicDataDict> findByPage(BasicDataDictDTO dto, PageRequest pageRequest,String userId)
        throws ApecRuntimeException
    {
        PageRequest sortPageRequest = new PageRequest(pageRequest.getPageNumber(),pageRequest.getPageSize(),
        SortUtils.getSortDirection(dto.getSortList()),SortUtils.getSortAttr(dto.getSortList()));
        Page<BasicDataDict> page = dao.findAll(getInputCondition(dto), sortPageRequest);
        return new PageDTO<>(page.getTotalElements(), page.getTotalPages(), page.getNumber(), page.getContent());
    }

    @Override
    public List<BasicDataDict> findList(BasicDataDictDTO dto,String userId)
        throws ApecRuntimeException
    {
        BasicDataDict entity = new BasicDataDict();
        BeanUtils.copyProperties(dto, entity);
        return queryAllModel(entity, dto.getLikeJson(), dto.getSortList());
    }

    public List<BasicDataDict> queryAllModel(BasicDataDict entity,JSONObject likeJson, List<SortAttrDTO> sortList) {
        //排序
        Sort sort = SortUtils.getSort(sortList);
        Iterable<BasicDataDict> iterable = dao.findAll(getInputConditionModel(entity, likeJson), sort);
        Iterator<BasicDataDict> iterator = iterable.iterator();
        List<BasicDataDict> list = new ArrayList<>();
        while (iterator.hasNext()) {
            BasicDataDict img = iterator.next();
            list.add(img);
        }
        return list;
    }

    @Override
    public PageDTO<BasicDataDictDTO> findByPageExt(BasicDataDictQueryDTO modelQueryDTO, PageRequest pageRequest,String userId)
        throws ApecRuntimeException
    {
        PageRequest sortPageRequest = new PageRequest(pageRequest.getPageNumber(),pageRequest.getPageSize(),
        SortUtils.getSortDirection(modelQueryDTO.getSortList()),SortUtils.getSortAttr(modelQueryDTO.getSortList()));
        Page<BasicDataDict> page = dao.findAll(getInputCondition(modelQueryDTO), sortPageRequest);
        return convertPoToDtoByPage(page);
    }

    @Override
    public List<BasicDataDictDTO> findListExt(BasicDataDictQueryDTO modelQueryDTO,String userId)
        throws ApecRuntimeException
    {
        Sort sort = SortUtils.getSort(modelQueryDTO.getSortList());
        Iterable<BasicDataDict> iterable =  dao.findAll(getInputCondition(modelQueryDTO), sort);
        List<BasicDataDictDTO> listModelDTO = new ArrayList<>();
        iterable.forEach(po->{
            listModelDTO.add(convertPoToDto(po));

        });
        return listModelDTO;
    }



    /**
     * 获取对象的缓存Key
     * @param entity 对象
     * @return
     */
    private String getCacheKey(BasicDataDict entity) {
        return CACHE_PREFIX + "BasicDataDict_List";
    }

    /**
     * 刷新类型的数据到数据库 （修改状态就需要刷新）
     * @param entity
     */
    private void flushCache(BasicDataDict entity) {
        List<BooleanExpression> predicates = new ArrayList<>();
        predicates.add(QBasicDataDict.basicDataDict.enableFlag.eq(EnableFlag.Y));
        predicates.add(QBasicDataDict.basicDataDict.status.eq(STATUS_VALID));

        Predicate predicate = ExpressionUtils.allOf(predicates.toArray(new BooleanExpression[predicates.size()]));
        Sort sort = SortUtils.getSort(new ArrayList());
        Iterable<BasicDataDict> page = dao.findAll(predicate, sort);
        List<BasicDataDict> list = new ArrayList<BasicDataDict>();
            page.forEach(one -> {
                list.add(one);
            });
        cacheService.add(getCacheKey(entity), JsonUtils.toJSONString(list));
        LOG.debug("刷新数据到缓存：" + getCacheKey(entity) + " \n" + JsonUtils.toJSONString(list));
    }

    /**
     * 获取基础模型的JPA查询列表表达式
     * @param entity 对象
     * @param likeJson 属性模糊查询标识
     * @return
     */
    private List<BooleanExpression> getBooleanExpressionList4Base(BasicDataDict entity, JSONObject likeJson) {
        List<BooleanExpression> list = new ArrayList<BooleanExpression>();
        if (StringUtils.isNotBlank(entity.getId())) {
            if(likeJson.containsKey("id") && likeJson.getBoolean("id")) {
                list.add(QBasicDataDict.basicDataDict.id.like(getQueryLikeParam(entity.getId())));
            }else{
                list.add(QBasicDataDict.basicDataDict.id.eq(entity.getId()));
            }
        }

        if (null == entity.getEnableFlag()) {
            list.add(QBasicDataDict.basicDataDict.enableFlag.eq(EnableFlag.Y));
        } else {
            list.add(QBasicDataDict.basicDataDict.enableFlag.eq(entity.getEnableFlag()));
        }

        if (null != entity.getCityId()) {
            list.add(QBasicDataDict.basicDataDict.cityId.eq(entity.getCityId()));
        }
        if (StringUtils.isNotBlank(entity.getStatus())) {
            list.add(QBasicDataDict.basicDataDict.status.eq(entity.getStatus()));
        }

        if (null != entity.getCreateDate()) {
            list.add(QBasicDataDict.basicDataDict.createDate.eq(entity.getCreateDate()));
        }
        if (null != entity.getLastUpdateDate()) {
            list.add(QBasicDataDict.basicDataDict.lastUpdateDate.eq(entity.getLastUpdateDate()));
        }
        if (StringUtils.isNotBlank(entity.getCreateBy())) {
            list.add(QBasicDataDict.basicDataDict.createBy.eq(entity.getCreateBy()));
        }
        if (StringUtils.isNotBlank(entity.getLastUpdateBy())) {
            list.add(QBasicDataDict.basicDataDict.lastUpdateBy.eq(entity.getLastUpdateBy()));
        }
        if (StringUtils.isNotBlank(entity.getPlantformId())) {
            list.add(QBasicDataDict.basicDataDict.plantformId.eq(entity.getPlantformId()));
        }
        if (StringUtils.isNotBlank(entity.getOecdNo())) {
            list.add(QBasicDataDict.basicDataDict.oecdNo.eq(entity.getOecdNo()));
        }
        if (null != entity.getOrderNumber()) {
            list.add(QBasicDataDict.basicDataDict.orderNumber.eq(entity.getOrderNumber()));
        }
        if (StringUtils.isNotBlank(entity.getRemarks())) {
            list.add(QBasicDataDict.basicDataDict.remarks.eq(entity.getRemarks()));
        }
        return list;
    }

    /**
     * 获取对象模型的JPA查询列表表达式
     * @param entity 对象
     * @param likeJson 属性模糊查询标识
     * @return
     */
    private List<BooleanExpression> getBooleanExpressionList4Model(BasicDataDict entity, JSONObject likeJson) {
        if(null == likeJson){
            likeJson = new JSONObject();
        }
        List<BooleanExpression> list = getBooleanExpressionList4Base(entity, likeJson);

        if (StringUtils.isNotBlank(entity.getExchangeId())) {
            if(likeJson.containsKey(C_BASICDATADICT_ATTRNAME_EXCHANGEID) && likeJson.getBoolean(C_BASICDATADICT_ATTRNAME_EXCHANGEID)) {
                list.add(QBasicDataDict.basicDataDict.exchangeId.like(getQueryLikeParam(entity.getExchangeId())));
            }else{
                list.add(QBasicDataDict.basicDataDict.exchangeId.eq(entity.getExchangeId()));
            }
        }


        if (StringUtils.isNotBlank(entity.getSubSystemCode())) {
            if(likeJson.containsKey(C_BASICDATADICT_ATTRNAME_SUBSYSTEMCODE) && likeJson.getBoolean(C_BASICDATADICT_ATTRNAME_SUBSYSTEMCODE)) {
                list.add(QBasicDataDict.basicDataDict.subSystemCode.like(getQueryLikeParam(entity.getSubSystemCode())));
            }else{
                list.add(QBasicDataDict.basicDataDict.subSystemCode.eq(entity.getSubSystemCode()));
            }
        }


        if (StringUtils.isNotBlank(entity.getDictCode())) {
            if(likeJson.containsKey(C_BASICDATADICT_ATTRNAME_DICTCODE) && likeJson.getBoolean(C_BASICDATADICT_ATTRNAME_DICTCODE)) {
                list.add(QBasicDataDict.basicDataDict.dictCode.like(getQueryLikeParam(entity.getDictCode())));
            }else{
                list.add(QBasicDataDict.basicDataDict.dictCode.eq(entity.getDictCode()));
            }
        }


        if (StringUtils.isNotBlank(entity.getDictValue())) {
            if(likeJson.containsKey(C_BASICDATADICT_ATTRNAME_DICTVALUE) && likeJson.getBoolean(C_BASICDATADICT_ATTRNAME_DICTVALUE)) {
                list.add(QBasicDataDict.basicDataDict.dictValue.like(getQueryLikeParam(entity.getDictValue())));
            }else{
                list.add(QBasicDataDict.basicDataDict.dictValue.eq(entity.getDictValue()));
            }
        }


        if (StringUtils.isNotBlank(entity.getDictEnValue())) {
            if(likeJson.containsKey(C_BASICDATADICT_ATTRNAME_DICTENVALUE) && likeJson.getBoolean(C_BASICDATADICT_ATTRNAME_DICTENVALUE)) {
                list.add(QBasicDataDict.basicDataDict.dictEnValue.like(getQueryLikeParam(entity.getDictEnValue())));
            }else{
                list.add(QBasicDataDict.basicDataDict.dictEnValue.eq(entity.getDictEnValue()));
            }
        }


        if (StringUtils.isNotBlank(entity.getDictType())) {
            if(likeJson.containsKey(C_BASICDATADICT_ATTRNAME_DICTTYPE) && likeJson.getBoolean(C_BASICDATADICT_ATTRNAME_DICTTYPE)) {
                list.add(QBasicDataDict.basicDataDict.dictType.like(getQueryLikeParam(entity.getDictType())));
            }else{
                list.add(QBasicDataDict.basicDataDict.dictType.eq(entity.getDictType()));
            }
        }


        if (StringUtils.isNotBlank(entity.getDictLevel())) {
            if(likeJson.containsKey(C_BASICDATADICT_ATTRNAME_DICTLEVEL) && likeJson.getBoolean(C_BASICDATADICT_ATTRNAME_DICTLEVEL)) {
                list.add(QBasicDataDict.basicDataDict.dictLevel.like(getQueryLikeParam(entity.getDictLevel())));
            }else{
                list.add(QBasicDataDict.basicDataDict.dictLevel.eq(entity.getDictLevel()));
            }
        }


        if (StringUtils.isNotBlank(entity.getDictStatus())) {
            if(likeJson.containsKey(C_BASICDATADICT_ATTRNAME_DICTSTATUS) && likeJson.getBoolean(C_BASICDATADICT_ATTRNAME_DICTSTATUS)) {
                list.add(QBasicDataDict.basicDataDict.dictStatus.like(getQueryLikeParam(entity.getDictStatus())));
            }else{
                list.add(QBasicDataDict.basicDataDict.dictStatus.eq(entity.getDictStatus()));
            }
        }


        if (StringUtils.isNotBlank(entity.getImg())) {
            if(likeJson.containsKey(C_BASICDATADICT_ATTRNAME_IMG) && likeJson.getBoolean(C_BASICDATADICT_ATTRNAME_IMG)) {
                list.add(QBasicDataDict.basicDataDict.img.like(getQueryLikeParam(entity.getImg())));
            }else{
                list.add(QBasicDataDict.basicDataDict.img.eq(entity.getImg()));
            }
        }


        if (StringUtils.isNotBlank(entity.getImgId())) {
            if(likeJson.containsKey(C_BASICDATADICT_ATTRNAME_IMGID) && likeJson.getBoolean(C_BASICDATADICT_ATTRNAME_IMGID)) {
                list.add(QBasicDataDict.basicDataDict.imgId.like(getQueryLikeParam(entity.getImgId())));
            }else{
                list.add(QBasicDataDict.basicDataDict.imgId.eq(entity.getImgId()));
            }
        }


        if (StringUtils.isNotBlank(entity.getSubSystemBizType())) {
            if(likeJson.containsKey(C_BASICDATADICT_ATTRNAME_SUBSYSTEMBIZTYPE) && likeJson.getBoolean(C_BASICDATADICT_ATTRNAME_SUBSYSTEMBIZTYPE)) {
                list.add(QBasicDataDict.basicDataDict.subSystemBizType.like(getQueryLikeParam(entity.getSubSystemBizType())));
            }else{
                list.add(QBasicDataDict.basicDataDict.subSystemBizType.eq(entity.getSubSystemBizType()));
            }
        }


        return list;
    }

    /**
     * 处理queryDTO参数
     * @param modelQueryDTO
     * @return
     * @throws ApecRuntimeException
     */
    private List<BooleanExpression> getBooleanExpressionList4ModelQueryDTO(BasicDataDictQueryDTO modelQueryDTO)
        throws ApecRuntimeException
    {
        BasicDataDict model = new BasicDataDict();
        BeanUtils.copyPropertiesIgnoreNullFilds(modelQueryDTO,model);
        List<BooleanExpression> listBooleanExpression = getBooleanExpressionList4Model(model,modelQueryDTO.getLikeJson());
        /* 模型扩展 时间区间属性 处理开始... */
        if(null != modelQueryDTO.getBeginCreateDate() && null != modelQueryDTO.getEndCreateDate()){
            String startDate = DateUtils.formatDate(modelQueryDTO.getBeginCreateDate(), DateUtils.FORMAT_DATE);
            String endDate = DateUtils.formatDate(modelQueryDTO.getEndCreateDate(), DateUtils.FORMAT_DATE);
            String startDateTime = startDate + SERVICE_QUERY_DATE_TIME_START;
            String endDateTime = endDate + SERVICE_QUERY_DATE_TIME_END;
            listBooleanExpression.add(QBasicDataDict.basicDataDict.createDate.between(
            DateUtils.formatDates(startDateTime, DateUtils.FORMAT_DATE_TIME),
            DateUtils.formatDates(endDateTime, DateUtils.FORMAT_DATE_TIME)));
        }

        if(null != modelQueryDTO.getBeginUpdateDate() && null != modelQueryDTO.getEndUpdateDate()){
            String startDate = DateUtils.formatDate(modelQueryDTO.getBeginUpdateDate(), DateUtils.FORMAT_DATE);
            String endDate = DateUtils.formatDate(modelQueryDTO.getEndUpdateDate(), DateUtils.FORMAT_DATE);
            String startDateTime = startDate + SERVICE_QUERY_DATE_TIME_START;
            String endDateTime = endDate + SERVICE_QUERY_DATE_TIME_END;
            listBooleanExpression.add(QBasicDataDict.basicDataDict.lastUpdateDate.between(
            DateUtils.formatDates(startDateTime, DateUtils.FORMAT_DATE_TIME),
            DateUtils.formatDates(endDateTime, DateUtils.FORMAT_DATE_TIME)));
        }
        /* 模型扩展 时间区间属性 处理完成. */


        /* 模型扩展 集合属性 处理开始... */
        if(CollectionUtils.isNotEmpty(modelQueryDTO.getIds())) {
            listBooleanExpression.add(QBasicDataDict.basicDataDict.id.in(modelQueryDTO.getIds()));
        }
        if(CollectionUtils.isNotEmpty(modelQueryDTO.getStatusLS())) {
            listBooleanExpression.add(QBasicDataDict.basicDataDict.status.in(modelQueryDTO.getStatusLS()));
        }
        if(CollectionUtils.isNotEmpty(modelQueryDTO.getPlantformIdLS())) {
            listBooleanExpression.add(QBasicDataDict.basicDataDict.plantformId.in(modelQueryDTO.getPlantformIdLS()));
        }
        if(CollectionUtils.isNotEmpty(modelQueryDTO.getOecdNoLS())) {
            listBooleanExpression.add(QBasicDataDict.basicDataDict.oecdNo.in(modelQueryDTO.getOecdNoLS()));
        }

        if(CollectionUtils.isNotEmpty(modelQueryDTO.getExchangeIdLS())) {
            listBooleanExpression.add(QBasicDataDict.basicDataDict.exchangeId.in(modelQueryDTO.getExchangeIdLS()));
        }
        if(CollectionUtils.isNotEmpty(modelQueryDTO.getSubSystemCodeLS())) {
            listBooleanExpression.add(QBasicDataDict.basicDataDict.subSystemCode.in(modelQueryDTO.getSubSystemCodeLS()));
        }
        if(CollectionUtils.isNotEmpty(modelQueryDTO.getDictCodeLS())) {
            listBooleanExpression.add(QBasicDataDict.basicDataDict.dictCode.in(modelQueryDTO.getDictCodeLS()));
        }
        if(CollectionUtils.isNotEmpty(modelQueryDTO.getDictValueLS())) {
            listBooleanExpression.add(QBasicDataDict.basicDataDict.dictValue.in(modelQueryDTO.getDictValueLS()));
        }
        if(CollectionUtils.isNotEmpty(modelQueryDTO.getDictEnValueLS())) {
            listBooleanExpression.add(QBasicDataDict.basicDataDict.dictEnValue.in(modelQueryDTO.getDictEnValueLS()));
        }
        if(CollectionUtils.isNotEmpty(modelQueryDTO.getDictTypeLS())) {
            listBooleanExpression.add(QBasicDataDict.basicDataDict.dictType.in(modelQueryDTO.getDictTypeLS()));
        }
        if(CollectionUtils.isNotEmpty(modelQueryDTO.getDictLevelLS())) {
            listBooleanExpression.add(QBasicDataDict.basicDataDict.dictLevel.in(modelQueryDTO.getDictLevelLS()));
        }
        if(CollectionUtils.isNotEmpty(modelQueryDTO.getDictStatusLS())) {
            listBooleanExpression.add(QBasicDataDict.basicDataDict.dictStatus.in(modelQueryDTO.getDictStatusLS()));
        }
        if(CollectionUtils.isNotEmpty(modelQueryDTO.getImgLS())) {
            listBooleanExpression.add(QBasicDataDict.basicDataDict.img.in(modelQueryDTO.getImgLS()));
        }
        if(CollectionUtils.isNotEmpty(modelQueryDTO.getImgIdLS())) {
            listBooleanExpression.add(QBasicDataDict.basicDataDict.imgId.in(modelQueryDTO.getImgIdLS()));
        }
        if(CollectionUtils.isNotEmpty(modelQueryDTO.getSubSystemBizTypeLS())) {
            listBooleanExpression.add(QBasicDataDict.basicDataDict.subSystemBizType.in(modelQueryDTO.getSubSystemBizTypeLS()));
        }
        /* 模型扩展 集合属性 处理结束... */
        return listBooleanExpression;
    }


    /**
     * 获取对象模型的JPA查询断言
     * @param entity 模型对象
     * @return
     */
    private Predicate getInputConditionModel(BasicDataDict entity, JSONObject likeJson) {
        List<BooleanExpression> predicates = getBooleanExpressionList4Model(entity,likeJson);
        return ExpressionUtils.allOf(predicates.toArray(new BooleanExpression[predicates.size()]));
    }

    /**
     * 获取Vo对象的JPA查询断言
     * @param vo VO对象
     * @return
     */
	private Predicate getInputCondition(BasicDataDictVo vo) {
		BasicDataDict entity = new BasicDataDict();
        BeanUtils.copyProperties(vo, entity);
        List<BooleanExpression> predicates = getBooleanExpressionList4Model(entity,vo.getLikeJson());
        return ExpressionUtils.allOf(predicates.toArray(new BooleanExpression[predicates.size()]));
	}
	
    /**
     * 获取Dto对象的JPA查询断言
     * @param dto DTO对象
     * @return
     */
	private Predicate getInputCondition(BasicDataDictDTO dto) {
        BasicDataDict entity = new BasicDataDict();
        BeanUtils.copyProperties(dto, entity);
        List<BooleanExpression> predicates = getBooleanExpressionList4Model(entity,dto.getLikeJson());
        return ExpressionUtils.allOf(predicates.toArray(new BooleanExpression[predicates.size()]));
	}

    /**
     * 处理模型扩展DTO
     * @param modelQueryDTO
     * @return
     * @throws ApecRuntimeException
     */
    private Predicate getInputCondition(BasicDataDictQueryDTO modelQueryDTO)
        throws ApecRuntimeException
    {
        List<BooleanExpression> listBooleanExpression = getBooleanExpressionList4ModelQueryDTO(modelQueryDTO);
        return ExpressionUtils.allOf(listBooleanExpression.toArray(new BooleanExpression[listBooleanExpression.size()]));
    }

    @Override
    public PageDTO<BasicDataDictDTO> convertPoToDtoByPage(Page<BasicDataDict> page)
    {
        List<BasicDataDictDTO> dtoList = page.getContent().stream().map(po -> {
            return convertPoToDto(po);
        }).collect(Collectors.toList());
        return new PageDTO<>(page.getTotalElements(), page.getTotalPages(), page.getNumber(), dtoList);
    }

    @Override
    public BasicDataDictDTO convertPoToDto(BasicDataDict po)
    {

        if(null == po) {
            return null;
        }
        BasicDataDictDTO dto = new BasicDataDictDTO();
        BeanUtils.copyPropertiesIgnoreNullFilds(po, dto);
        return dto;
    }

    @Override
    public BasicDataDict convertDtoToPo(BasicDataDictDTO dto)
    {

        if(null == dto) {
            return null;
        }
        BasicDataDict po = new BasicDataDict();
        BeanUtils.copyPropertiesIgnoreNullFilds(dto, po);
        return po;
    }

    @Override
    public List<BasicDataDictDTO> convertList(List<BasicDataDict> list){
        return list.stream().map(po -> { return convertPoToDto(po); }).collect(Collectors.toList());
    }
    /**
     * like查询的值
     * @param attrVal
     * @return
     */
    private String getQueryLikeParam(String attrVal){
        return SERVICE_QUERY_CHAR_PERCENT + attrVal + SERVICE_QUERY_CHAR_PERCENT;
    }
}
