package com.apec.datadict.service;

import com.apec.datadict.model.BasicDataDict;
import com.apec.datadict.dto.BasicDataDictDTO;
import com.apec.datadict.vo.BasicDataDictVo;
import com.apec.datadict.constants.BasicDataDictContant;
import com.apec.datadict.dto.BasicDataDictQueryDTO;
import java.lang.*;
import java.util.*;
import com.apec.framework.common.dto.PageDTO;
import com.apec.framework.common.exception.ApecRuntimeException;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Page;

/**
 */
public interface BasicDataDictService {
    /**
     * 创建一个对象
     * @param dto 对象
     * @param userId 用户编号
     * @return
	 * @throws ApecRuntimeException
     */
    BasicDataDictDTO save(BasicDataDictDTO dto,String userId)
        throws ApecRuntimeException;
	
    /**
     * 逻辑删除一个对象
     * @param id 主键id
     * @param userId 用户编号
     * @return
     * @throws ApecRuntimeException
     */
    BasicDataDict delete(String id,String userId)
        throws ApecRuntimeException;

    /**
     * 物理删除一个对象数据
     * @param id 主键id
     * @param userId 用户编号
     * @return
     * @throws ApecRuntimeException
     */
    boolean deleteDB(String id,String userId)
        throws ApecRuntimeException;

    /**
     * 更新排序
     * @param id 主键id
     * @param orderNumber
     * @param userId 用户编号
     * @return
     * @throws ApecRuntimeException
     */
    BasicDataDict updateOrderNumber(String id, Integer orderNumber,String userId)
        throws ApecRuntimeException;
	
    /**
     * 更新状态
     * @param id 主键id
     * @param status 状态
     * @param userId 用户编号
     * @return
     * @throws ApecRuntimeException
     */
    BasicDataDict updateStatus(String id, String status,String userId)
        throws ApecRuntimeException;
	
    /**
     * 更新对象模型
     * @param vo 对象
     * @param userId 用户编号
     * @return
     * @throws ApecRuntimeException
     */
    BasicDataDict update(BasicDataDict vo,String userId)
        throws ApecRuntimeException;

    /**
     * 更新对象模型
     * @param vo 对象
     * @param userId 用户编号
     * @return
     * @throws ApecRuntimeException
     */
    BasicDataDictDTO updateDTO(BasicDataDictDTO vo,String userId)
        throws ApecRuntimeException;

    /**
     * 分页查询数据
     * @param dto 查询模型
     * @param pageRequest 分页请求
     * @param userId 用户编号
     * @return
     * @throws ApecRuntimeException
     */
    PageDTO<BasicDataDictDTO> findByPageDTO(BasicDataDictDTO dto, PageRequest pageRequest,String userId)
        throws ApecRuntimeException;
	
	/**
     * 分页查询数据
     * @param dto 查询模型
     * @param pageRequest 分页请求
     * @param userId 用户编号
     * @return
     * @throws ApecRuntimeException
     */
    PageDTO<BasicDataDict> findByPage(BasicDataDictDTO dto, PageRequest pageRequest,String userId)
        throws ApecRuntimeException;
	
    /**
     * 根据主键查询对象
     * @param id 主键id
     * @param userId 用户编号
     * @return
     * @throws ApecRuntimeException
     */
    BasicDataDict find(String id,String userId)
        throws ApecRuntimeException;

    /**
     * 根据主键查询对象
     * @param id 主键id
     * @param userId 用户编号
     * @return
     * @throws ApecRuntimeException
     */
    BasicDataDictDTO findDTO(String id,String userId)
        throws ApecRuntimeException;

    /**
     * 查询数据列表
     * @param dto
     * @param userId 用户编号
     * @return
     * @throws ApecRuntimeException
     */
    List<BasicDataDict> findList(BasicDataDictDTO dto,String userId)
        throws ApecRuntimeException;

   /**
     *  分页查询(扩展查询条件)
     * @param modelQueryDTO
     * @param request
     * @return
     * @throws ApecRuntimeException
     */
    PageDTO<BasicDataDictDTO> findByPageExt(BasicDataDictQueryDTO modelQueryDTO, PageRequest request,String userId)
        throws ApecRuntimeException;

    /**
     * 查询所有(扩展查询条件)
     * @param modelQueryDTO
     * @return
     * @throws ApecRuntimeException
     */
    List<BasicDataDictDTO> findListExt(BasicDataDictQueryDTO modelQueryDTO,String userId)
        throws ApecRuntimeException;

    /**
     * 转换 page的PO转换为PageDTO的DTO
     * @param  page page的PO
     * @return
     */
    PageDTO<BasicDataDictDTO> convertPoToDtoByPage(Page<BasicDataDict> page);
    /**
     * 转换 PO 2 DTO
     * @param po
     * @return
     */
    BasicDataDictDTO convertPoToDto(BasicDataDict po);

    /**
     * 转换 DTO 2 PO
     * @param dto
     * @return
     */
    BasicDataDict convertDtoToPo(BasicDataDictDTO dto);

    /**
     * 转换集合对象
     * @param list
     * @return
     */
    List<BasicDataDictDTO> convertList(List<BasicDataDict> list);
}
