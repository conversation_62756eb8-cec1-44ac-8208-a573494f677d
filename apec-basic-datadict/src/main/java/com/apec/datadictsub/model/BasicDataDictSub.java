package com.apec.datadictsub.model;


import java.lang.*;
import com.apec.framework.common.constant.FrameConsts;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Column;
import com.apec.framework.common.util.JsonUtils;
import org.hibernate.annotations.DynamicUpdate;

import com.apec.framework.jpa.model.MySQLBaseModel;

@Data
@Entity
@DynamicUpdate
@GenericGenerator(name = FrameConsts.SYSTEM_GENERATOR, strategy = FrameConsts.ASSIGNED)
@Table(name = "basic_data_dict_sub")

/**
 */
public class BasicDataDictSub extends MySQLBaseModel<String>
{
    private static final long serialVersionUID = 1L;


    /**
     * 字典编码
     */
    @Column(name = "`dict_code`", columnDefinition = "varchar(64)  COMMENT '字典编码'")
    private String dictCode;

    /**
     * 字典子项编码
     */
    @Column(name = "`dict_sub_code`", columnDefinition = "varchar(64)  COMMENT '字典子项编码'")
    private String dictSubCode;

    /**
     * 字典子项值
     */
    @Column(name = "`dict_sub_value`", columnDefinition = "varchar(64)  COMMENT '字典子项值'")
    private String dictSubValue;

    /**
     * 字典子项英文值
     */
    @Column(name = "`dict_sub_en_value`", columnDefinition = "varchar(64)  COMMENT '字典子项英文值'")
    private String dictSubEnValue;

    /**
     * 字典启用状态 1-启用 0-禁用
     */
    @Column(name = "`dict_status`", columnDefinition = "varchar(64)  COMMENT '字典启用状态 1-启用 0-禁用'")
    private String dictStatus;

    /**
     * 图片id
     */
    @Column(name = "`img_id`", columnDefinition = "varchar(64)  COMMENT '图片id'")
    private String imgId;

    /**
     * 图片地址
     */
    @Column(name = "`img`", columnDefinition = "varchar(1024)  COMMENT '图片地址'")
    private String img;



    @Override
    public String toString() {
        return JsonUtils.toJSONString(this);
    }
}
