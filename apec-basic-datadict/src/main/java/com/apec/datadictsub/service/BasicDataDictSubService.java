package com.apec.datadictsub.service;

import com.apec.datadictsub.model.BasicDataDictSub;
import com.apec.datadictsub.dto.BasicDataDictSubDTO;
import com.apec.datadictsub.vo.BasicDataDictSubVo;
import com.apec.datadictsub.constants.BasicDataDictSubContant;
import com.apec.datadictsub.dto.BasicDataDictSubQueryDTO;
import java.lang.*;
import java.util.*;
import com.apec.framework.common.dto.PageDTO;
import com.apec.framework.common.exception.ApecRuntimeException;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Page;

/**
 */
public interface BasicDataDictSubService {
    /**
     * 创建一个对象
     * @param dto 对象
     * @param userId 用户编号
     * @return
	 * @throws ApecRuntimeException
     */
    BasicDataDictSubDTO save(BasicDataDictSubDTO dto,String userId)
        throws ApecRuntimeException;
	
    /**
     * 逻辑删除一个对象
     * @param id 主键id
     * @param userId 用户编号
     * @return
     * @throws ApecRuntimeException
     */
    BasicDataDictSub delete(String id,String userId)
        throws ApecRuntimeException;

    /**
     * 物理删除一个对象数据
     * @param id 主键id
     * @param userId 用户编号
     * @return
     * @throws ApecRuntimeException
     */
    boolean deleteDB(String id,String userId)
        throws ApecRuntimeException;

    /**
     * 更新排序
     * @param id 主键id
     * @param orderNumber
     * @param userId 用户编号
     * @return
     * @throws ApecRuntimeException
     */
    BasicDataDictSub updateOrderNumber(String id, Integer orderNumber,String userId)
        throws ApecRuntimeException;
	
    /**
     * 更新状态
     * @param id 主键id
     * @param status 状态
     * @param userId 用户编号
     * @return
     * @throws ApecRuntimeException
     */
    BasicDataDictSub updateStatus(String id, String status,String userId)
        throws ApecRuntimeException;
	
    /**
     * 更新对象模型
     * @param vo 对象
     * @param userId 用户编号
     * @return
     * @throws ApecRuntimeException
     */
    BasicDataDictSub update(BasicDataDictSub vo,String userId)
        throws ApecRuntimeException;

    /**
     * 更新对象模型
     * @param vo 对象
     * @param userId 用户编号
     * @return
     * @throws ApecRuntimeException
     */
    BasicDataDictSubDTO updateDTO(BasicDataDictSubDTO vo,String userId)
        throws ApecRuntimeException;

    /**
     * 分页查询数据
     * @param dto 查询模型
     * @param pageRequest 分页请求
     * @param userId 用户编号
     * @return
     * @throws ApecRuntimeException
     */
    PageDTO<BasicDataDictSubDTO> findByPageDTO(BasicDataDictSubDTO dto, PageRequest pageRequest,String userId)
        throws ApecRuntimeException;
	
	/**
     * 分页查询数据
     * @param dto 查询模型
     * @param pageRequest 分页请求
     * @param userId 用户编号
     * @return
     * @throws ApecRuntimeException
     */
    PageDTO<BasicDataDictSub> findByPage(BasicDataDictSubDTO dto, PageRequest pageRequest,String userId)
        throws ApecRuntimeException;
	
    /**
     * 根据主键查询对象
     * @param id 主键id
     * @param userId 用户编号
     * @return
     * @throws ApecRuntimeException
     */
    BasicDataDictSub find(String id,String userId)
        throws ApecRuntimeException;

    /**
     * 根据主键查询对象
     * @param id 主键id
     * @param userId 用户编号
     * @return
     * @throws ApecRuntimeException
     */
    BasicDataDictSubDTO findDTO(String id,String userId)
        throws ApecRuntimeException;

    /**
     * 查询数据列表
     * @param dto
     * @param userId 用户编号
     * @return
     * @throws ApecRuntimeException
     */
    List<BasicDataDictSub> findList(BasicDataDictSubDTO dto,String userId)
        throws ApecRuntimeException;

   /**
     *  分页查询(扩展查询条件)
     * @param modelQueryDTO
     * @param request
     * @return
     * @throws ApecRuntimeException
     */
    PageDTO<BasicDataDictSubDTO> findByPageExt(BasicDataDictSubQueryDTO modelQueryDTO, PageRequest request,String userId)
        throws ApecRuntimeException;

    /**
     * 查询所有(扩展查询条件)
     * @param modelQueryDTO
     * @return
     * @throws ApecRuntimeException
     */
    List<BasicDataDictSubDTO> findListExt(BasicDataDictSubQueryDTO modelQueryDTO,String userId)
        throws ApecRuntimeException;

    /**
     * 转换 page的PO转换为PageDTO的DTO
     * @param  page page的PO
     * @return
     */
    PageDTO<BasicDataDictSubDTO> convertPoToDtoByPage(Page<BasicDataDictSub> page);
    /**
     * 转换 PO 2 DTO
     * @param po
     * @return
     */
    BasicDataDictSubDTO convertPoToDto(BasicDataDictSub po);

    /**
     * 转换 DTO 2 PO
     * @param dto
     * @return
     */
    BasicDataDictSub convertDtoToPo(BasicDataDictSubDTO dto);

    /**
     * 转换集合对象
     * @param list
     * @return
     */
    List<BasicDataDictSubDTO> convertList(List<BasicDataDictSub> list);
}
