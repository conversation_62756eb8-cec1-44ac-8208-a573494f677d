package com.apec.datadictsub.service.impl;

import com.apec.datadictsub.model.BasicDataDictSub;
import com.apec.datadictsub.dto.BasicDataDictSubDTO;
import com.apec.datadictsub.vo.BasicDataDictSubVo;
import com.apec.datadictsub.constants.BasicDataDictSubContant;
import com.apec.datadictsub.dto.BasicDataDictSubQueryDTO;
import java.lang.*;
import java.util.*;
import java.util.stream.*;
import com.apec.framework.common.dto.PageDTO;
import com.apec.framework.common.enumtype.EnableFlag;
import com.apec.framework.common.exception.ApecRuntimeException;
import com.apec.framework.common.tools.IDGenerator;
import com.apec.framework.common.util.BeanUtils;
import com.apec.framework.common.util.DateUtils;
import org.apache.commons.lang.*;
import com.querydsl.core.types.ExpressionUtils;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.Predicate;
import com.apec.framework.common.util.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Sort;
import com.apec.cache.base.CacheService;
import com.apec.datadictsub.service.BasicDataDictSubService;
import com.apec.datadictsub.dao.BasicDataDictSubDAO;
import com.apec.datadictsub.model.QBasicDataDictSub;
import com.alibaba.fastjson.JSONObject;
import com.apec.framework.common.constant.ErrorCodeConsts;
import org.apache.commons.lang3.StringUtils;
import com.apec.magpie.cb.dto.SortAttrDTO;
import com.apec.magpie.cb.enums.EnumDirection;
import org.apache.commons.collections.CollectionUtils;
import com.apec.magpie.cb.common.utils.SortUtils;
import javax.annotation.Resource;

/**
 */
@Service
public class BasicDataDictSubServiceImpl implements BasicDataDictSubService,BasicDataDictSubContant
{

    private final Logger LOG =  LoggerFactory.getLogger( getClass());

    @Autowired
    private BasicDataDictSubDAO dao;

    @Resource
    private CacheService cacheService;

	
    @Override
    @Transactional(rollbackFor=Exception.class)
    public BasicDataDictSubDTO save(BasicDataDictSubDTO dto,String userId)
        throws ApecRuntimeException
    {

        if(StringUtils.isBlank(userId)){
            throw new ApecRuntimeException(ErrorCodeConsts.COMMON_MISSING_PARAMS,Arrays.asList("userId"));
        }
        if (StringUtils.isBlank(dto.getDictCode())) {
            dto.setDictCode(DEFAULT_DICTCODE_VAL );
        }
        if (StringUtils.isBlank(dto.getDictSubCode())) {
            dto.setDictSubCode(DEFAULT_DICTSUBCODE_VAL );
        }
        if (StringUtils.isBlank(dto.getDictSubValue())) {
            dto.setDictSubValue(DEFAULT_DICTSUBVALUE_VAL );
        }
        if (StringUtils.isBlank(dto.getDictSubEnValue())) {
            dto.setDictSubEnValue(DEFAULT_DICTSUBENVALUE_VAL );
        }
        if (StringUtils.isBlank(dto.getDictStatus())) {
            dto.setDictStatus(DEFAULT_DICTSTATUS_VAL );
        }
        if (StringUtils.isBlank(dto.getImgId())) {
            dto.setImgId(DEFAULT_IMGID_VAL );
        }
        if (StringUtils.isBlank(dto.getImg())) {
            dto.setImg(DEFAULT_IMG_VAL );
        }

        //基础字段默认值
        if (null == dto.getCityId()) {
            dto.setCityId(DEFAULT_CITY_ID);
        }
        if (StringUtils.isBlank(dto.getStatus())) {
            dto.setStatus(STATUS_INVALID);
        }
        if (StringUtils.isBlank(dto.getPlantformId())) {
            dto.setPlantformId(DEFAULT_PLANTFORM_ID);
        }
        if (StringUtils.isBlank(dto.getOecdNo())) {
            dto.setOecdNo(DEFAULT_OECD_NO);
        }
        if (null == dto.getEnableFlag()) {
            dto.setEnableFlag(EnableFlag.Y);
        }
        if (null == dto.getCreateDate()) {
            dto.setCreateDate(new Date());
        }
        if (StringUtils.isBlank(dto.getCreateBy())) {
            dto.setCreateBy(userId);
        }
        if (null == dto.getOrderNumber()) {
            dto.setOrderNumber(1);
        }

        //属性拷贝保存到数据库刷新缓存
        BasicDataDictSub entity = new BasicDataDictSub();
        BeanUtils.copyPropertiesIgnoreNullFilds(dto, entity, new String[] {});

        if(StringUtils.isBlank(entity.getId())){
            entity.setId(IDGenerator.getNextId().toString());
        }

        dto.setId(entity.getId());
        dao.save(entity);
        return dto;
    }

    @Override
    public BasicDataDictSub delete(String id,String userId)
        throws ApecRuntimeException
    {
        if(StringUtils.isBlank(id) || StringUtils.isBlank(userId)){
            throw new ApecRuntimeException(ErrorCodeConsts.COMMON_MISSING_PARAMS,Arrays.asList("id userId"));
        }
        LOG.info("{},逻辑删除数据 id:{}",userId,id);
        BasicDataDictSub entity = dao.findOne(id);
        if(null == entity){
            throw new ApecRuntimeException(ErrorCodeConsts.ERROR_QUERY_DATA_NOT_EXIST_BY_ID);
        }
        entity.setEnableFlag(EnableFlag.N);

        if(StringUtils.isNotBlank(userId)){
            entity.setLastUpdateBy(userId);
        }
        entity.setLastUpdateDate(new Date());

        BasicDataDictSub rs = dao.saveAndFlush(entity);
        return rs;
    }

    @Override
    public boolean deleteDB(String id,String userId)
        throws ApecRuntimeException
    {
        if(StringUtils.isBlank(id) || StringUtils.isBlank(userId)){
            throw new ApecRuntimeException(ErrorCodeConsts.COMMON_MISSING_PARAMS,Arrays.asList("id userId"));
        }
        LOG.info("{},物理删除数据 id:{}",userId,id);
        BasicDataDictSub entity = dao.findOne(id);
        if(null == entity){
            throw new ApecRuntimeException(ErrorCodeConsts.ERROR_QUERY_DATA_NOT_EXIST_BY_ID);
        }
        LOG.info("删除的物理数据详情:{}",JsonUtils.toJSONString(entity));
        dao.delete(id);
        return true;
    }

    @Override
    public  BasicDataDictSub updateOrderNumber(String id, Integer orderNumber,String userId)
        throws ApecRuntimeException
    {
        if(StringUtils.isBlank(id) || null == orderNumber || StringUtils.isBlank(userId) ){
            throw new ApecRuntimeException(ErrorCodeConsts.COMMON_MISSING_PARAMS, Arrays.asList("id orderNumber userId"));
        }
        BasicDataDictSub entity = dao.findOne(id);
        entity.setOrderNumber(orderNumber);
        if(StringUtils.isNotBlank(userId)){
            entity.setLastUpdateBy(userId);
        }
        entity.setLastUpdateDate(new Date());
        dao.saveAndFlush(entity);
        return entity;
    }

    @Override
    public  BasicDataDictSub updateStatus(String id, String status,String userId)
        throws ApecRuntimeException
    {
        if(StringUtils.isBlank(id) || StringUtils.isBlank(status)
            || StringUtils.isBlank(userId)){
            throw new ApecRuntimeException(ErrorCodeConsts.COMMON_MISSING_PARAMS, Arrays.asList("id status userId"));
        }
        BasicDataDictSub entity = dao.findOne(id);
        entity.setStatus(status);
        if(StringUtils.isNotBlank(userId)){
            entity.setLastUpdateBy(userId);
        }
        entity.setLastUpdateDate(new Date());
        dao.saveAndFlush(entity);
        return entity;
    }

    @Override
    public BasicDataDictSub update(BasicDataDictSub obj,String userId)
        throws ApecRuntimeException
    {
        if(null == obj || StringUtils.isBlank(userId)){
            throw new ApecRuntimeException(ErrorCodeConsts.COMMON_MISSING_PARAMS, Arrays.asList("obj userId"));
        }
        if(StringUtils.isBlank(obj.getId())){
            throw new ApecRuntimeException(ErrorCodeConsts.ERROR_QUERY_DATA_NOT_EXIST_BY_ID);
        }

        BasicDataDictSub entity = dao.findOne(obj.getId());
        if(null == entity){
			LOG.error("查询数据为空,id={}",obj.getId());
			return null;
        }
		//模块属性设置
        if (StringUtils.isNotBlank(obj.getDictCode())) {
			entity.setDictCode(obj.getDictCode());
        }
        if (StringUtils.isNotBlank(obj.getDictSubCode())) {
			entity.setDictSubCode(obj.getDictSubCode());
        }
        if (StringUtils.isNotBlank(obj.getDictSubValue())) {
			entity.setDictSubValue(obj.getDictSubValue());
        }
        if (StringUtils.isNotBlank(obj.getDictSubEnValue())) {
			entity.setDictSubEnValue(obj.getDictSubEnValue());
        }
        if (StringUtils.isNotBlank(obj.getDictStatus())) {
			entity.setDictStatus(obj.getDictStatus());
        }
        if (StringUtils.isNotBlank(obj.getImgId())) {
			entity.setImgId(obj.getImgId());
        }
        if (StringUtils.isNotBlank(obj.getImg())) {
			entity.setImg(obj.getImg());
        }

        //基础属性设置
        if (StringUtils.isNotBlank(obj.getId())) {
            entity.setId(obj.getId());
        }
        if (StringUtils.isNotBlank(obj.getStatus())) {
            entity.setStatus(obj.getStatus());
        }
        if (null != obj.getEnableFlag()) {
            entity.setEnableFlag(obj.getEnableFlag());
        }
        if (null != obj.getCreateDate()) {
            entity.setCreateDate(obj.getCreateDate());
        }
        if (StringUtils.isNotBlank(obj.getCreateBy())) {
            entity.setCreateBy(obj.getCreateBy());
        }
        if (StringUtils.isNotBlank(obj.getLastUpdateBy())) {
            entity.setLastUpdateBy(obj.getLastUpdateBy());
        }
        if (null != obj.getLastUpdateDate()) {
            entity.setLastUpdateDate(obj.getLastUpdateDate());
        }
        if (StringUtils.isNotBlank(obj.getPlantformId())) {
            entity.setPlantformId(obj.getPlantformId());
        }
        if (StringUtils.isNotBlank(obj.getOecdNo())) {
            entity.setOecdNo(obj.getOecdNo());
        }
        if (null != obj.getCityId()) {
            entity.setCityId(obj.getCityId());
        }
        if (null != obj.getOrderNumber()) {
            entity.setOrderNumber(obj.getOrderNumber());
        }
        if (StringUtils.isNotBlank(obj.getRemarks())) {
            entity.setRemarks(obj.getRemarks());
        }

        if(StringUtils.isBlank(entity.getLastUpdateBy())) {
            entity.setLastUpdateBy(userId);
        }
        if(null == entity.getLastUpdateDate()) {
            entity.setLastUpdateDate(new Date());
        }

        dao.saveAndFlush(entity);
        return entity;
    }

    @Override
    public BasicDataDictSubDTO updateDTO(BasicDataDictSubDTO dto,String userId)
    throws ApecRuntimeException
    {
        BasicDataDictSub po = update(convertDtoToPo(dto),userId);
        return convertPoToDto(po);
    }

    @Override
    public BasicDataDictSub find(String id,String userId)
        throws ApecRuntimeException
    {
        BasicDataDictSub entity = dao.findOne(id);
        return entity;
    }

    @Override
    public BasicDataDictSubDTO findDTO(String id,String userId)
        throws ApecRuntimeException
    {
        BasicDataDictSub entity = dao.findOne(id);
        return convertPoToDto(entity);
    }


    @Override
    public PageDTO<BasicDataDictSubDTO> findByPageDTO(BasicDataDictSubDTO dto, PageRequest pageRequest,String userId)
        throws ApecRuntimeException
    {
        PageRequest sortPageRequest = new PageRequest(pageRequest.getPageNumber(),pageRequest.getPageSize(),
        SortUtils.getSortDirection(dto.getSortList()),SortUtils.getSortAttr(dto.getSortList()));
        Page<BasicDataDictSub> page = dao.findAll(getInputCondition(dto), sortPageRequest);
        return convertPoToDtoByPage(page);
    }

    @Override
    public PageDTO<BasicDataDictSub> findByPage(BasicDataDictSubDTO dto, PageRequest pageRequest,String userId)
        throws ApecRuntimeException
    {
        PageRequest sortPageRequest = new PageRequest(pageRequest.getPageNumber(),pageRequest.getPageSize(),
        SortUtils.getSortDirection(dto.getSortList()),SortUtils.getSortAttr(dto.getSortList()));
        Page<BasicDataDictSub> page = dao.findAll(getInputCondition(dto), sortPageRequest);
        return new PageDTO<>(page.getTotalElements(), page.getTotalPages(), page.getNumber(), page.getContent());
    }

    @Override
    public List<BasicDataDictSub> findList(BasicDataDictSubDTO dto,String userId)
        throws ApecRuntimeException
    {
        BasicDataDictSub entity = new BasicDataDictSub();
        BeanUtils.copyProperties(dto, entity);
        return queryAllModel(entity, dto.getLikeJson(), dto.getSortList());
    }

    public List<BasicDataDictSub> queryAllModel(BasicDataDictSub entity,JSONObject likeJson, List<SortAttrDTO> sortList) {
        //排序
        Sort sort = SortUtils.getSort(sortList);
        Iterable<BasicDataDictSub> iterable = dao.findAll(getInputConditionModel(entity, likeJson), sort);
        Iterator<BasicDataDictSub> iterator = iterable.iterator();
        List<BasicDataDictSub> list = new ArrayList<>();
        while (iterator.hasNext()) {
            BasicDataDictSub img = iterator.next();
            list.add(img);
        }
        return list;
    }

    @Override
    public PageDTO<BasicDataDictSubDTO> findByPageExt(BasicDataDictSubQueryDTO modelQueryDTO, PageRequest pageRequest,String userId)
        throws ApecRuntimeException
    {
        PageRequest sortPageRequest = new PageRequest(pageRequest.getPageNumber(),pageRequest.getPageSize(),
        SortUtils.getSortDirection(modelQueryDTO.getSortList()),SortUtils.getSortAttr(modelQueryDTO.getSortList()));
        Page<BasicDataDictSub> page = dao.findAll(getInputCondition(modelQueryDTO), sortPageRequest);
        return convertPoToDtoByPage(page);
    }

    @Override
    public List<BasicDataDictSubDTO> findListExt(BasicDataDictSubQueryDTO modelQueryDTO,String userId)
        throws ApecRuntimeException
    {
        Sort sort = SortUtils.getSort(modelQueryDTO.getSortList());
        Iterable<BasicDataDictSub> iterable =  dao.findAll(getInputCondition(modelQueryDTO), sort);
        List<BasicDataDictSubDTO> listModelDTO = new ArrayList<>();
        iterable.forEach(po->{
            listModelDTO.add(convertPoToDto(po));

        });
        return listModelDTO;
    }



    /**
     * 获取对象的缓存Key
     * @param entity 对象
     * @return
     */
    private String getCacheKey(BasicDataDictSub entity) {
        return CACHE_PREFIX + "BasicDataDictSub_List";
    }

    /**
     * 刷新类型的数据到数据库 （修改状态就需要刷新）
     * @param entity
     */
    private void flushCache(BasicDataDictSub entity) {
        List<BooleanExpression> predicates = new ArrayList<>();
        predicates.add(QBasicDataDictSub.basicDataDictSub.enableFlag.eq(EnableFlag.Y));
        predicates.add(QBasicDataDictSub.basicDataDictSub.status.eq(STATUS_VALID));

        Predicate predicate = ExpressionUtils.allOf(predicates.toArray(new BooleanExpression[predicates.size()]));
        Sort sort = SortUtils.getSort(new ArrayList());
        Iterable<BasicDataDictSub> page = dao.findAll(predicate, sort);
        List<BasicDataDictSub> list = new ArrayList<BasicDataDictSub>();
            page.forEach(one -> {
                list.add(one);
            });
        cacheService.add(getCacheKey(entity), JsonUtils.toJSONString(list));
        LOG.debug("刷新数据到缓存：" + getCacheKey(entity) + " \n" + JsonUtils.toJSONString(list));
    }

    /**
     * 获取基础模型的JPA查询列表表达式
     * @param entity 对象
     * @param likeJson 属性模糊查询标识
     * @return
     */
    private List<BooleanExpression> getBooleanExpressionList4Base(BasicDataDictSub entity, JSONObject likeJson) {
        List<BooleanExpression> list = new ArrayList<BooleanExpression>();
        if (StringUtils.isNotBlank(entity.getId())) {
            if(likeJson.containsKey("id") && likeJson.getBoolean("id")) {
                list.add(QBasicDataDictSub.basicDataDictSub.id.like(getQueryLikeParam(entity.getId())));
            }else{
                list.add(QBasicDataDictSub.basicDataDictSub.id.eq(entity.getId()));
            }
        }

        if (null == entity.getEnableFlag()) {
            list.add(QBasicDataDictSub.basicDataDictSub.enableFlag.eq(EnableFlag.Y));
        } else {
            list.add(QBasicDataDictSub.basicDataDictSub.enableFlag.eq(entity.getEnableFlag()));
        }

        if (null != entity.getCityId()) {
            list.add(QBasicDataDictSub.basicDataDictSub.cityId.eq(entity.getCityId()));
        }
        if (StringUtils.isNotBlank(entity.getStatus())) {
            list.add(QBasicDataDictSub.basicDataDictSub.status.eq(entity.getStatus()));
        }

        if (null != entity.getCreateDate()) {
            list.add(QBasicDataDictSub.basicDataDictSub.createDate.eq(entity.getCreateDate()));
        }
        if (null != entity.getLastUpdateDate()) {
            list.add(QBasicDataDictSub.basicDataDictSub.lastUpdateDate.eq(entity.getLastUpdateDate()));
        }
        if (StringUtils.isNotBlank(entity.getCreateBy())) {
            list.add(QBasicDataDictSub.basicDataDictSub.createBy.eq(entity.getCreateBy()));
        }
        if (StringUtils.isNotBlank(entity.getLastUpdateBy())) {
            list.add(QBasicDataDictSub.basicDataDictSub.lastUpdateBy.eq(entity.getLastUpdateBy()));
        }
        if (StringUtils.isNotBlank(entity.getPlantformId())) {
            list.add(QBasicDataDictSub.basicDataDictSub.plantformId.eq(entity.getPlantformId()));
        }
        if (StringUtils.isNotBlank(entity.getOecdNo())) {
            list.add(QBasicDataDictSub.basicDataDictSub.oecdNo.eq(entity.getOecdNo()));
        }
        if (null != entity.getOrderNumber()) {
            list.add(QBasicDataDictSub.basicDataDictSub.orderNumber.eq(entity.getOrderNumber()));
        }
        if (StringUtils.isNotBlank(entity.getRemarks())) {
            list.add(QBasicDataDictSub.basicDataDictSub.remarks.eq(entity.getRemarks()));
        }
        return list;
    }

    /**
     * 获取对象模型的JPA查询列表表达式
     * @param entity 对象
     * @param likeJson 属性模糊查询标识
     * @return
     */
    private List<BooleanExpression> getBooleanExpressionList4Model(BasicDataDictSub entity, JSONObject likeJson) {
        if(null == likeJson){
            likeJson = new JSONObject();
        }
        List<BooleanExpression> list = getBooleanExpressionList4Base(entity, likeJson);

        if (StringUtils.isNotBlank(entity.getDictCode())) {
            if(likeJson.containsKey(C_BASICDATADICTSUB_ATTRNAME_DICTCODE) && likeJson.getBoolean(C_BASICDATADICTSUB_ATTRNAME_DICTCODE)) {
                list.add(QBasicDataDictSub.basicDataDictSub.dictCode.like(getQueryLikeParam(entity.getDictCode())));
            }else{
                list.add(QBasicDataDictSub.basicDataDictSub.dictCode.eq(entity.getDictCode()));
            }
        }


        if (StringUtils.isNotBlank(entity.getDictSubCode())) {
            if(likeJson.containsKey(C_BASICDATADICTSUB_ATTRNAME_DICTSUBCODE) && likeJson.getBoolean(C_BASICDATADICTSUB_ATTRNAME_DICTSUBCODE)) {
                list.add(QBasicDataDictSub.basicDataDictSub.dictSubCode.like(getQueryLikeParam(entity.getDictSubCode())));
            }else{
                list.add(QBasicDataDictSub.basicDataDictSub.dictSubCode.eq(entity.getDictSubCode()));
            }
        }


        if (StringUtils.isNotBlank(entity.getDictSubValue())) {
            if(likeJson.containsKey(C_BASICDATADICTSUB_ATTRNAME_DICTSUBVALUE) && likeJson.getBoolean(C_BASICDATADICTSUB_ATTRNAME_DICTSUBVALUE)) {
                list.add(QBasicDataDictSub.basicDataDictSub.dictSubValue.like(getQueryLikeParam(entity.getDictSubValue())));
            }else{
                list.add(QBasicDataDictSub.basicDataDictSub.dictSubValue.eq(entity.getDictSubValue()));
            }
        }


        if (StringUtils.isNotBlank(entity.getDictSubEnValue())) {
            if(likeJson.containsKey(C_BASICDATADICTSUB_ATTRNAME_DICTSUBENVALUE) && likeJson.getBoolean(C_BASICDATADICTSUB_ATTRNAME_DICTSUBENVALUE)) {
                list.add(QBasicDataDictSub.basicDataDictSub.dictSubEnValue.like(getQueryLikeParam(entity.getDictSubEnValue())));
            }else{
                list.add(QBasicDataDictSub.basicDataDictSub.dictSubEnValue.eq(entity.getDictSubEnValue()));
            }
        }


        if (StringUtils.isNotBlank(entity.getDictStatus())) {
            if(likeJson.containsKey(C_BASICDATADICTSUB_ATTRNAME_DICTSTATUS) && likeJson.getBoolean(C_BASICDATADICTSUB_ATTRNAME_DICTSTATUS)) {
                list.add(QBasicDataDictSub.basicDataDictSub.dictStatus.like(getQueryLikeParam(entity.getDictStatus())));
            }else{
                list.add(QBasicDataDictSub.basicDataDictSub.dictStatus.eq(entity.getDictStatus()));
            }
        }


        if (StringUtils.isNotBlank(entity.getImgId())) {
            if(likeJson.containsKey(C_BASICDATADICTSUB_ATTRNAME_IMGID) && likeJson.getBoolean(C_BASICDATADICTSUB_ATTRNAME_IMGID)) {
                list.add(QBasicDataDictSub.basicDataDictSub.imgId.like(getQueryLikeParam(entity.getImgId())));
            }else{
                list.add(QBasicDataDictSub.basicDataDictSub.imgId.eq(entity.getImgId()));
            }
        }


        if (StringUtils.isNotBlank(entity.getImg())) {
            if(likeJson.containsKey(C_BASICDATADICTSUB_ATTRNAME_IMG) && likeJson.getBoolean(C_BASICDATADICTSUB_ATTRNAME_IMG)) {
                list.add(QBasicDataDictSub.basicDataDictSub.img.like(getQueryLikeParam(entity.getImg())));
            }else{
                list.add(QBasicDataDictSub.basicDataDictSub.img.eq(entity.getImg()));
            }
        }


        return list;
    }

    /**
     * 处理queryDTO参数
     * @param modelQueryDTO
     * @return
     * @throws ApecRuntimeException
     */
    private List<BooleanExpression> getBooleanExpressionList4ModelQueryDTO(BasicDataDictSubQueryDTO modelQueryDTO)
        throws ApecRuntimeException
    {
        BasicDataDictSub model = new BasicDataDictSub();
        BeanUtils.copyPropertiesIgnoreNullFilds(modelQueryDTO,model);
        List<BooleanExpression> listBooleanExpression = getBooleanExpressionList4Model(model,modelQueryDTO.getLikeJson());
        /* 模型扩展 时间区间属性 处理开始... */
        if(null != modelQueryDTO.getBeginCreateDate() && null != modelQueryDTO.getEndCreateDate()){
            String startDate = DateUtils.formatDate(modelQueryDTO.getBeginCreateDate(), DateUtils.FORMAT_DATE);
            String endDate = DateUtils.formatDate(modelQueryDTO.getEndCreateDate(), DateUtils.FORMAT_DATE);
            String startDateTime = startDate + SERVICE_QUERY_DATE_TIME_START;
            String endDateTime = endDate + SERVICE_QUERY_DATE_TIME_END;
            listBooleanExpression.add(QBasicDataDictSub.basicDataDictSub.createDate.between(
            DateUtils.formatDates(startDateTime, DateUtils.FORMAT_DATE_TIME),
            DateUtils.formatDates(endDateTime, DateUtils.FORMAT_DATE_TIME)));
        }

        if(null != modelQueryDTO.getBeginUpdateDate() && null != modelQueryDTO.getEndUpdateDate()){
            String startDate = DateUtils.formatDate(modelQueryDTO.getBeginUpdateDate(), DateUtils.FORMAT_DATE);
            String endDate = DateUtils.formatDate(modelQueryDTO.getEndUpdateDate(), DateUtils.FORMAT_DATE);
            String startDateTime = startDate + SERVICE_QUERY_DATE_TIME_START;
            String endDateTime = endDate + SERVICE_QUERY_DATE_TIME_END;
            listBooleanExpression.add(QBasicDataDictSub.basicDataDictSub.lastUpdateDate.between(
            DateUtils.formatDates(startDateTime, DateUtils.FORMAT_DATE_TIME),
            DateUtils.formatDates(endDateTime, DateUtils.FORMAT_DATE_TIME)));
        }
        /* 模型扩展 时间区间属性 处理完成. */


        /* 模型扩展 集合属性 处理开始... */
        if(CollectionUtils.isNotEmpty(modelQueryDTO.getIds())) {
            listBooleanExpression.add(QBasicDataDictSub.basicDataDictSub.id.in(modelQueryDTO.getIds()));
        }
        if(CollectionUtils.isNotEmpty(modelQueryDTO.getStatusLS())) {
            listBooleanExpression.add(QBasicDataDictSub.basicDataDictSub.status.in(modelQueryDTO.getStatusLS()));
        }
        if(CollectionUtils.isNotEmpty(modelQueryDTO.getPlantformIdLS())) {
            listBooleanExpression.add(QBasicDataDictSub.basicDataDictSub.plantformId.in(modelQueryDTO.getPlantformIdLS()));
        }
        if(CollectionUtils.isNotEmpty(modelQueryDTO.getOecdNoLS())) {
            listBooleanExpression.add(QBasicDataDictSub.basicDataDictSub.oecdNo.in(modelQueryDTO.getOecdNoLS()));
        }

        if(CollectionUtils.isNotEmpty(modelQueryDTO.getDictCodeLS())) {
            listBooleanExpression.add(QBasicDataDictSub.basicDataDictSub.dictCode.in(modelQueryDTO.getDictCodeLS()));
        }
        if(CollectionUtils.isNotEmpty(modelQueryDTO.getDictSubCodeLS())) {
            listBooleanExpression.add(QBasicDataDictSub.basicDataDictSub.dictSubCode.in(modelQueryDTO.getDictSubCodeLS()));
        }
        if(CollectionUtils.isNotEmpty(modelQueryDTO.getDictSubValueLS())) {
            listBooleanExpression.add(QBasicDataDictSub.basicDataDictSub.dictSubValue.in(modelQueryDTO.getDictSubValueLS()));
        }
        if(CollectionUtils.isNotEmpty(modelQueryDTO.getDictSubEnValueLS())) {
            listBooleanExpression.add(QBasicDataDictSub.basicDataDictSub.dictSubEnValue.in(modelQueryDTO.getDictSubEnValueLS()));
        }
        if(CollectionUtils.isNotEmpty(modelQueryDTO.getDictStatusLS())) {
            listBooleanExpression.add(QBasicDataDictSub.basicDataDictSub.dictStatus.in(modelQueryDTO.getDictStatusLS()));
        }
        if(CollectionUtils.isNotEmpty(modelQueryDTO.getImgIdLS())) {
            listBooleanExpression.add(QBasicDataDictSub.basicDataDictSub.imgId.in(modelQueryDTO.getImgIdLS()));
        }
        if(CollectionUtils.isNotEmpty(modelQueryDTO.getImgLS())) {
            listBooleanExpression.add(QBasicDataDictSub.basicDataDictSub.img.in(modelQueryDTO.getImgLS()));
        }
        /* 模型扩展 集合属性 处理结束... */
        return listBooleanExpression;
    }


    /**
     * 获取对象模型的JPA查询断言
     * @param entity 模型对象
     * @return
     */
    private Predicate getInputConditionModel(BasicDataDictSub entity, JSONObject likeJson) {
        List<BooleanExpression> predicates = getBooleanExpressionList4Model(entity,likeJson);
        return ExpressionUtils.allOf(predicates.toArray(new BooleanExpression[predicates.size()]));
    }

    /**
     * 获取Vo对象的JPA查询断言
     * @param vo VO对象
     * @return
     */
	private Predicate getInputCondition(BasicDataDictSubVo vo) {
		BasicDataDictSub entity = new BasicDataDictSub();
        BeanUtils.copyProperties(vo, entity);
        List<BooleanExpression> predicates = getBooleanExpressionList4Model(entity,vo.getLikeJson());
        return ExpressionUtils.allOf(predicates.toArray(new BooleanExpression[predicates.size()]));
	}
	
    /**
     * 获取Dto对象的JPA查询断言
     * @param dto DTO对象
     * @return
     */
	private Predicate getInputCondition(BasicDataDictSubDTO dto) {
        BasicDataDictSub entity = new BasicDataDictSub();
        BeanUtils.copyProperties(dto, entity);
        List<BooleanExpression> predicates = getBooleanExpressionList4Model(entity,dto.getLikeJson());
        return ExpressionUtils.allOf(predicates.toArray(new BooleanExpression[predicates.size()]));
	}

    /**
     * 处理模型扩展DTO
     * @param modelQueryDTO
     * @return
     * @throws ApecRuntimeException
     */
    private Predicate getInputCondition(BasicDataDictSubQueryDTO modelQueryDTO)
        throws ApecRuntimeException
    {
        List<BooleanExpression> listBooleanExpression = getBooleanExpressionList4ModelQueryDTO(modelQueryDTO);
        return ExpressionUtils.allOf(listBooleanExpression.toArray(new BooleanExpression[listBooleanExpression.size()]));
    }

    @Override
    public PageDTO<BasicDataDictSubDTO> convertPoToDtoByPage(Page<BasicDataDictSub> page)
    {
        List<BasicDataDictSubDTO> dtoList = page.getContent().stream().map(po -> {
            return convertPoToDto(po);
        }).collect(Collectors.toList());
        return new PageDTO<>(page.getTotalElements(), page.getTotalPages(), page.getNumber(), dtoList);
    }

    @Override
    public BasicDataDictSubDTO convertPoToDto(BasicDataDictSub po)
    {

        if(null == po) {
            return null;
        }
        BasicDataDictSubDTO dto = new BasicDataDictSubDTO();
        BeanUtils.copyPropertiesIgnoreNullFilds(po, dto);
        return dto;
    }

    @Override
    public BasicDataDictSub convertDtoToPo(BasicDataDictSubDTO dto)
    {

        if(null == dto) {
            return null;
        }
        BasicDataDictSub po = new BasicDataDictSub();
        BeanUtils.copyPropertiesIgnoreNullFilds(dto, po);
        return po;
    }

    @Override
    public List<BasicDataDictSubDTO> convertList(List<BasicDataDictSub> list){
        return list.stream().map(po -> { return convertPoToDto(po); }).collect(Collectors.toList());
    }
    /**
     * like查询的值
     * @param attrVal
     * @return
     */
    private String getQueryLikeParam(String attrVal){
        return SERVICE_QUERY_CHAR_PERCENT + attrVal + SERVICE_QUERY_CHAR_PERCENT;
    }
}
