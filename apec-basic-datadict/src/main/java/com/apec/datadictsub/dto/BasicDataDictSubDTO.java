package com.apec.datadictsub.dto;


import java.lang.*;
import java.util.*;
import com.apec.framework.common.dto.BaseDTO;
import com.apec.framework.common.enumtype.EnableFlag;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import com.apec.framework.common.util.JsonUtils;
import com.alibaba.fastjson.JSONObject;
import com.apec.magpie.cb.dto.SortAttrDTO;

/**
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BasicDataDictSubDTO extends BaseDTO
{

    /**
     * 字典编码
     */
    private String dictCode;
    /**
     * 字典子项编码
     */
    private String dictSubCode;
    /**
     * 字典子项值
     */
    private String dictSubValue;
    /**
     * 字典子项英文值
     */
    private String dictSubEnValue;
    /**
     * 字典启用状态 1-启用 0-禁用
     */
    private String dictStatus;
    /**
     * 图片id
     */
    private String imgId;
    /**
     * 图片地址
     */
    private String img;

    /**
     * 使用id集合查询
     */
    private List<String> ids;
    /**
     * 确定字段是全匹配还是模糊匹配，likeJson里面的值 key=属性名称，v=boolean(是like)
     */
    private JSONObject likeJson ;
    /**
     * 排序集合
     */
    private List<SortAttrDTO> sortList;

    private String id;
    private String status;
    private EnableFlag enableFlag;
    private String createBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;
    private String lastUpdateBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdateDate;

    private String plantformId;
    private String oecdNo;
    private Integer cityId;
    private Integer orderNumber;
    private String remarks;

    @Override
    public String toString() {
    	return JsonUtils.toJSONString(this);
    }
}
