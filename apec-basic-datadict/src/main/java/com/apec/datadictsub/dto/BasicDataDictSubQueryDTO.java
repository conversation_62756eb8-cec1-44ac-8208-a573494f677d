package com.apec.datadictsub.dto;


import java.util.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import com.apec.framework.common.util.JsonUtils;

import lombok.EqualsAndHashCode;

/**
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class BasicDataDictSubQueryDTO extends BasicDataDictSubDTO
{
    /**
     * 创建 (开始时间)
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date beginCreateDate;
    /**
     * 创建 (结束时间)
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date endCreateDate;
    /**
     * 更新 (开始时间)
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date beginUpdateDate;
    /**
     * 更新 (结束时间)
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date endUpdateDate;


    private List<String> statusLS;
    private List<String> plantformIdLS;
    private List<String> oecdNoLS;


    /**
     * 字典编码 (集合查询)
     */
    private List<String> dictCodeLS;
    /**
     * 字典子项编码 (集合查询)
     */
    private List<String> dictSubCodeLS;
    /**
     * 字典子项值 (集合查询)
     */
    private List<String> dictSubValueLS;
    /**
     * 字典子项英文值 (集合查询)
     */
    private List<String> dictSubEnValueLS;
    /**
     * 字典启用状态 1-启用 0-禁用 (集合查询)
     */
    private List<String> dictStatusLS;
    /**
     * 图片id (集合查询)
     */
    private List<String> imgIdLS;
    /**
     * 图片地址 (集合查询)
     */
    private List<String> imgLS;

    @Override
    public String toString() {
        return JsonUtils.toJSONString(this);
    }
}
