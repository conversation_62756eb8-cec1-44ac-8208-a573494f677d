package com.apec.datadictsub.constants;

import java.lang.*;
import java.util.*;

import com.apec.magpie.cb.constant.IConts;

/**
 */
public interface BasicDataDictSubContant extends IConts {

    /** 字典编码 */
    String  DEFAULT_DICTCODE_VAL = "";
    /** 字典子项编码 */
    String  DEFAULT_DICTSUBCODE_VAL = "";
    /** 字典子项值 */
    String  DEFAULT_DICTSUBVALUE_VAL = "";
    /** 字典子项英文值 */
    String  DEFAULT_DICTSUBENVALUE_VAL = "";
    /** 字典启用状态 1-启用 0-禁用 */
    String  DEFAULT_DICTSTATUS_VAL = "0";
    /** 图片id */
    String  DEFAULT_IMGID_VAL = "";
    /** 图片地址 */
    String  DEFAULT_IMG_VAL = "";


    /**
     * 常量属性,模型名称(BasicDataDictSub) .属性 dictCode
     */
    String C_BASICDATADICTSUB_ATTRNAME_DICTCODE = "dictCode";
    /**
     * 常量属性,模型名称(BasicDataDictSub) .属性 dictSubCode
     */
    String C_BASICDATADICTSUB_ATTRNAME_DICTSUBCODE = "dictSubCode";
    /**
     * 常量属性,模型名称(BasicDataDictSub) .属性 dictSubValue
     */
    String C_BASICDATADICTSUB_ATTRNAME_DICTSUBVALUE = "dictSubValue";
    /**
     * 常量属性,模型名称(BasicDataDictSub) .属性 dictSubEnValue
     */
    String C_BASICDATADICTSUB_ATTRNAME_DICTSUBENVALUE = "dictSubEnValue";
    /**
     * 常量属性,模型名称(BasicDataDictSub) .属性 dictStatus
     */
    String C_BASICDATADICTSUB_ATTRNAME_DICTSTATUS = "dictStatus";
    /**
     * 常量属性,模型名称(BasicDataDictSub) .属性 imgId
     */
    String C_BASICDATADICTSUB_ATTRNAME_IMGID = "imgId";
    /**
     * 常量属性,模型名称(BasicDataDictSub) .属性 img
     */
    String C_BASICDATADICTSUB_ATTRNAME_IMG = "img";

}
