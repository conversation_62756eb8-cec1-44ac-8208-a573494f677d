package com.apec.selectlist.web;

import java.util.*;

import com.apec.selectlist.model.BasicSelectListCache;
import com.apec.selectlist.dto.BasicSelectListCacheDTO;
import com.apec.selectlist.vo.BasicSelectListCacheVo;
import com.apec.selectlist.constants.BasicSelectListCacheContant;
import com.apec.selectlist.dto.BasicSelectListCacheQueryDTO;
import com.apec.selectlist.service.BasicSelectListCacheService;
import java.lang.*;
import com.apec.framework.common.enumtype.EnableFlag;
import com.apec.framework.common.dto.PageDTO;
import com.apec.framework.common.exception.ApecRuntimeException;
import com.apec.framework.common.model.PageJSON;
import com.apec.framework.common.model.ResultData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import java.util.*;
import java.util.stream.*;
import com.apec.framework.common.util.SpringBizUtils;
import com.apec.magpie.cb.common.utils.SortUtils;
import com.apec.aops.ThreadLocalUtil;

import com.apec.framework.common.util.BeanUtils;
import com.apec.magpie.cb.constant.IConts;
import com.apec.annotations.SystemControllerLog;

/**
 */

@RestController
@RequestMapping("/basicSelectListCache")
public class BasicSelectListCacheController implements IConts {

	@Autowired
	BasicSelectListCacheService service;

	/**
	 * 添加数据
	 * @param json JSON请求参数
	 * @return
	 */
	@SystemControllerLog(code = "basicselectlistcache.save",description = "添加数据",author = "rs",lastDate = "2020-11-20")
	@RequestMapping(value = "/save", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
		public ResultData save(@RequestBody String json) {
		BasicSelectListCacheDTO dto = SpringBizUtils.getFormJSON(json, BasicSelectListCacheDTO.class); 
		BasicSelectListCacheDTO data = service.save(dto, ThreadLocalUtil.getUserId());
		return SpringBizUtils.getResultData(true, data, null);
	}


	/**
	 * 根据主键id逻辑删除
	 * @param json JSON请求参数
	 * @return
	 */
	@SystemControllerLog(code = "basicselectlistcache.delete",description = "根据主键id逻辑删除",author = "rs",lastDate = "2020-11-20")
	@RequestMapping(value = "/delete", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	public ResultData delete(@RequestBody String json) {
		BasicSelectListCacheDTO obj = SpringBizUtils.getFormJSON(json, BasicSelectListCacheDTO.class); 
		BasicSelectListCache data = service.delete(obj.getId(), ThreadLocalUtil.getUserId());
		return SpringBizUtils.getResultData(true, data, null);
	}


	/**
	 * 根据主键id物理删除
	 * @param json JSON请求参数
	 * @return
	 */
	@SystemControllerLog(code = "basicselectlistcache.deleteDB",description = "根据主键id物理删除",author = "rs",lastDate = "2020-11-20")
	@RequestMapping(value = "/deleteDB", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	public ResultData deleteDB(@RequestBody String json) {
		BasicSelectListCacheDTO vo = SpringBizUtils.getFormJSON(json, BasicSelectListCacheDTO.class); 
		Boolean data = service.deleteDB(vo.getId(), ThreadLocalUtil.getUserId());
		return SpringBizUtils.getResultData(true, data, null);
	}


	/**
	 * 根据主键id更新模型
	 * @param json JSON请求参数
	 * @return
	 */
	@SystemControllerLog(code = "basicselectlistcache.update",description = "根据主键id更新模型",author = "rs",lastDate = "2020-11-20")
	@RequestMapping(value = "/update", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	public ResultData update(@RequestBody String json) {
		BasicSelectListCache obj = SpringBizUtils.getFormJSON(json, BasicSelectListCache.class); 
		BasicSelectListCache data = service.update(obj, ThreadLocalUtil.getUserId());
		return SpringBizUtils.getResultData(true, data, null);
	}

	/**
	 * 根据主键id更新模型
	 * @param json JSON请求参数
	 * @return
	 */
	@SystemControllerLog(code = "basicselectlistcache.updateDTO",description = "根据主键id更新模型",author = "rs",lastDate = "2020-11-20")
	@RequestMapping(value = "/updateDTO", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	public ResultData updateDTO(@RequestBody String json) {
		BasicSelectListCacheDTO obj = SpringBizUtils.getFormJSON(json, BasicSelectListCacheDTO.class); 
		BasicSelectListCacheDTO data = service.updateDTO(obj, ThreadLocalUtil.getUserId());
		return SpringBizUtils.getResultData(true, data, null);
	}


	/**
 	 * 根据主键id更新模型状态
	 * @param json JSON请求参数
	 * @return
	 */
	@SystemControllerLog(code = "basicselectlistcache.updateStatus",description = "根据主键id更新模型状态",author = "rs",lastDate = "2020-11-20")
	@RequestMapping(value = "/updateStatus", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	public ResultData updateStatus(@RequestBody String json) {
		BasicSelectListCache obj = SpringBizUtils.getFormJSON(json, BasicSelectListCache.class); 
		BasicSelectListCache data = service.updateStatus(obj.getId(),obj.getStatus(), ThreadLocalUtil.getUserId());
		return SpringBizUtils.getResultData(true, data, null);
	}

	/**
	 * 根据主键id更新模型排序值
	 * @param json JSON请求参数
	 * @return
	 */
	@SystemControllerLog(code = "basicselectlistcache.updateOrderNumber",description = "根据主键id更新模型排序值",author = "rs",lastDate = "2020-11-20")
	@RequestMapping(value = "/updateOrderNumber", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	public ResultData updateOrderNumber(@RequestBody String json) {
		BasicSelectListCache obj = SpringBizUtils.getFormJSON(json, BasicSelectListCache.class); 
		BasicSelectListCache data = service.updateOrderNumber(obj.getId(),obj.getOrderNumber(), ThreadLocalUtil.getUserId());
		return SpringBizUtils.getResultData(true, data, null);
	}


	/**
	 * 根据主键id查询模型
	 * @param json JSON请求参数
	 * @return
	 */
	@SystemControllerLog(code = "basicselectlistcache.find",description = "根据主键id查询模型",author = "rs",lastDate = "2020-11-20")
	@RequestMapping(value = "/find", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	public ResultData selectById(@RequestBody String json) {
		BasicSelectListCache obj = SpringBizUtils.getFormJSON(json, BasicSelectListCache.class); 
		BasicSelectListCache data = service.find(obj.getId(), ThreadLocalUtil.getUserId());
		return SpringBizUtils.getResultData(true, data, null);
	}



	/**
	 * 根据主键id查询模型
	 * @param json JSON请求参数
	 * @return
	 */
	@SystemControllerLog(code = "basicselectlistcache.findDTO",description = "根据主键id查询模型",author = "rs",lastDate = "2020-11-20")
	@RequestMapping(value = "/findDTO", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	public ResultData findDTO(@RequestBody String json) {
		BasicSelectListCacheDTO dto = SpringBizUtils.getFormJSON(json, BasicSelectListCacheDTO.class); 
		BasicSelectListCacheDTO data = service.findDTO(dto.getId(), ThreadLocalUtil.getUserId());
		return SpringBizUtils.getResultData(true, data, null);
	}

	/**
	 * 查询集合(基础模型)
	 * @param json JSON请求参数
	 * @return
	 */
	@SystemControllerLog(code = "basicselectlistcache.findList",description = "查询集合(基础模型)",author = "rs",lastDate = "2020-11-20")
	@RequestMapping(value = "/findList", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	public ResultData findList(@RequestBody String json) {
		BasicSelectListCacheDTO dto = SpringBizUtils.getFormJSON(json, BasicSelectListCacheDTO.class); 
		List<BasicSelectListCache> data = service.findList(dto, ThreadLocalUtil.getUserId());
		return SpringBizUtils.getResultData(true, data, null);
	}




	/**
	 * 查询集合(扩展模型)
	 * @param json JSON请求参数
	 * @return
	 */
	@SystemControllerLog(code = "basicselectlistcache.findListExt",description = "查询集合(扩展模型)",author = "rs",lastDate = "2020-11-20")
	@RequestMapping(value = "/findListExt", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
	public ResultData findListExt(@RequestBody String json) {
	    BasicSelectListCacheQueryDTO obj = SpringBizUtils.getFormJSON(json,BasicSelectListCacheQueryDTO.class);
		List<BasicSelectListCacheDTO>  data = service.findListExt(obj, ThreadLocalUtil.getUserId());
		return SpringBizUtils.getResultData(true, data, null);
	}

	/**
	 * 分页查询模型列表
	 * @param json JSON请求参数
	 * @return
	 */
	@SystemControllerLog(code = "basicselectlistcache.findByPage",description = "分页查询模型列表",author = "rs",lastDate = "2020-11-20")
	@RequestMapping(value = "/findByPage", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	public ResultData findByPage(@RequestBody String json) {
		BasicSelectListCacheDTO dto = SpringBizUtils.getFormJSON(json, BasicSelectListCacheDTO.class);
	    PageRequest pageRequest = SortUtils.genPageRequest(dto);
		PageDTO<BasicSelectListCacheDTO> data = service.findByPageDTO(dto, pageRequest, ThreadLocalUtil.getUserId());
		return SpringBizUtils.getResultData(true, data, null);
	}


	/**
	 * 分页
	 * @param json JSON请求参数
	 * @return
	 */
	@SystemControllerLog(code = "basicselectlistcache.findByPageExt",description = "分页查询模型列表",author = "rs",lastDate = "2020-11-20")
	@RequestMapping(value = "/findByPageExt", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
	public ResultData findByPageExt(@RequestBody String json) {
		BasicSelectListCacheQueryDTO dto =  SpringBizUtils.getFormJSON(json, BasicSelectListCacheQueryDTO.class);
	    PageRequest request = SortUtils.genPageRequest(dto);
		PageDTO<BasicSelectListCacheDTO> data = service.findByPageExt(dto, request, ThreadLocalUtil.getUserId());
		return SpringBizUtils.getResultData(true, data, null);
	}
}
