package com.apec.selectlist.dto;


import java.lang.*;
import java.util.*;
import com.apec.framework.common.dto.BaseDTO;
import com.apec.framework.common.enumtype.EnableFlag;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import com.apec.framework.common.util.JsonUtils;
import com.alibaba.fastjson.JSONObject;
import com.apec.magpie.cb.dto.SortAttrDTO;

/**
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BasicSelectListCacheDTO extends BaseDTO
{

    /**
     * 分类类型编码(不能是全部数字)
     */
    private String selectTypeCode;
    /**
     * 分类类型名称
     */
    private String selectTypeName;
    /**
     * 调用的服务名称
     */
    private String serviceName;
    /**
     * 调用服务后面的扩展查询的路径
     */
    private String extQueryPath;
    /**
     * 刷新时间(分钟)
     */
    private Integer flushMinute;
    /**
     * 上次刷新成功时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastSuccessTime;
    /**
     * 最后失败时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastFailTime;
    /**
     * 失败信息
     */
    private String failMsg;

    /**
     * 使用id集合查询
     */
    private List<String> ids;
    /**
     * 确定字段是全匹配还是模糊匹配，likeJson里面的值 key=属性名称，v=boolean(是like)
     */
    private JSONObject likeJson ;
    /**
     * 排序集合
     */
    private List<SortAttrDTO> sortList;

    private String id;
    private String status;
    private EnableFlag enableFlag;
    private String createBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;
    private String lastUpdateBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdateDate;

    private String plantformId;
    private String oecdNo;
    private Integer cityId;
    private Integer orderNumber;
    private String remarks;

    @Override
    public String toString() {
    	return JsonUtils.toJSONString(this);
    }
}
