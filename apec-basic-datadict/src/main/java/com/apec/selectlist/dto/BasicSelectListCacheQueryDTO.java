package com.apec.selectlist.dto;


import java.util.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import com.apec.framework.common.util.JsonUtils;

import lombok.EqualsAndHashCode;

/**
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class BasicSelectListCacheQueryDTO extends BasicSelectListCacheDTO
{
    /**
     * 创建 (开始时间)
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date beginCreateDate;
    /**
     * 创建 (结束时间)
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date endCreateDate;
    /**
     * 更新 (开始时间)
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date beginUpdateDate;
    /**
     * 更新 (结束时间)
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date endUpdateDate;


    private List<String> statusLS;
    private List<String> plantformIdLS;
    private List<String> oecdNoLS;

    /**
     * 上次刷新成功时间 (开始时间)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginLastSuccessTime;

    /**
     * 上次刷新成功时间 (结束时间)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endLastSuccessTime;
    /**
     * 最后失败时间 (开始时间)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginLastFailTime;

    /**
     * 最后失败时间 (结束时间)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endLastFailTime;

    /**
     * 分类类型编码(不能是全部数字) (集合查询)
     */
    private List<String> selectTypeCodeLS;
    /**
     * 分类类型名称 (集合查询)
     */
    private List<String> selectTypeNameLS;
    /**
     * 调用的服务名称 (集合查询)
     */
    private List<String> serviceNameLS;
    /**
     * 调用服务后面的扩展查询的路径 (集合查询)
     */
    private List<String> extQueryPathLS;
    /**
     * 刷新时间(分钟) (集合查询)
     */
    private List<Integer> flushMinuteLS;
    /**
     * 上次刷新成功时间 (集合查询)
     */
    private List<Date> lastSuccessTimeLS;
    /**
     * 最后失败时间 (集合查询)
     */
    private List<Date> lastFailTimeLS;
    /**
     * 失败信息 (集合查询)
     */
    private List<String> failMsgLS;

    @Override
    public String toString() {
        return JsonUtils.toJSONString(this);
    }
}
