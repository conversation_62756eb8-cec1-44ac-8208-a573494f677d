package com.apec.selectlist.model;


import java.lang.*;
import java.util.Date;

import com.apec.framework.common.constant.FrameConsts;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Column;
import com.apec.framework.common.util.JsonUtils;
import org.hibernate.annotations.DynamicUpdate;

import com.apec.framework.jpa.model.MySQLBaseModel;

@Data
@Entity
@DynamicUpdate
@GenericGenerator(name = FrameConsts.SYSTEM_GENERATOR, strategy = FrameConsts.ASSIGNED)
@Table(name = "basic_select_list_cache")

/**
 */
public class BasicSelectListCache extends MySQLBaseModel<String>
{
    private static final long serialVersionUID = 1L;


    /**
     * 分类类型编码(不能是全部数字)
     */
    @Column(name = "`select_type_code`", columnDefinition = "varchar(64)  COMMENT '分类类型编码(不能是全部数字)'")
    private String selectTypeCode;

    /**
     * 分类类型名称
     */
    @Column(name = "`select_type_name`", columnDefinition = "varchar(64)  COMMENT '分类类型名称'")
    private String selectTypeName;

    /**
     * 调用的服务名称
     */
    @Column(name = "`service_Name`", columnDefinition = "varchar(64)  COMMENT '调用的服务名称'")
    private String serviceName;

    /**
     * 调用服务后面的扩展查询的路径
     */
    @Column(name = "`ext_query_path`", columnDefinition = "varchar(64)  COMMENT '调用服务后面的扩展查询的路径'")
    private String extQueryPath;

    /**
     * 刷新时间(分钟)
     */
    @Column(name = "`flush_minute`", columnDefinition = "int(32) DEFAULT '0' COMMENT '刷新时间(分钟)'")
    private Integer flushMinute;

    /**
     * 上次刷新成功时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "`last_success_time`", columnDefinition = "datetime  DEFAULT CURRENT_TIMESTAMP COMMENT '上次刷新成功时间'")
    private Date lastSuccessTime;

    /**
     * 最后失败时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "`last_fail_time`", columnDefinition = "datetime  DEFAULT CURRENT_TIMESTAMP COMMENT '最后失败时间'")
    private Date lastFailTime;

    /**
     * 失败信息
     */
    @Column(name = "`fail_msg`", columnDefinition = "varchar(64)  COMMENT '失败信息'")
    private String failMsg;



    @Override
    public String toString() {
        return JsonUtils.toJSONString(this);
    }
}
