package com.apec.selectlist.vo;


import java.lang.*;
import java.util.*;
import com.apec.framework.common.dto.BaseDTO;
import com.apec.framework.common.enumtype.EnableFlag;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import com.apec.framework.common.util.JsonUtils;
import com.alibaba.fastjson.JSONObject;
import com.apec.magpie.cb.dto.SortAttrDTO;

/**
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BasicSelectListCacheVo extends BaseDTO
{
    private static final long serialVersionUID = 1L;
    /**
     * 分类类型编码(不能是全部数字)
     */
    private String selectTypeCode;
    /**
     * 分类类型名称
     */
    private String selectTypeName;
    /**
     * 调用的服务名称
     */
    private String serviceName;
    /**
     * 调用服务后面的扩展查询的路径
     */
    private String extQueryPath;
    /**
     * 刷新时间(分钟)
     */
    private Integer flushMinute;
    /**
     * 上次刷新成功时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastSuccessTime;
    /**
     * 最后失败时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastFailTime;
    /**
     * 失败信息
     */
    private String failMsg;
    /**
     * 主键id
     */
    private String id;
    /**
     * 状态
     */
    private String status;
    /**
     * 逻辑删除
     */
    private EnableFlag enableFlag;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 最后修改人
     */
    private String lastUpdateBy;
    /**
     * 最后修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdateDate;
    /**
     * 平台编号
     */
    private String plantformId;
    /**
     * 组织编码
     */
    private String oecdNo;
    /**
     * 城市id
     */
    private Integer cityId;
    /**
     * 排序
     */
    private Integer orderNumber;
    /**
     * 备注
     */
    private String remarks;

    /**
     * 上次刷新成功时间 (开始时间)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginLastSuccessTime;

    /**
     * 上次刷新成功时间 (结束时间)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endLastSuccessTime;
    /**
     * 最后失败时间 (开始时间)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginLastFailTime;

    /**
     * 最后失败时间 (结束时间)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endLastFailTime;
    /**
     * 创建时间 (开始时间)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginCreateDate;

    /**
     * 创建时间 (结束时间)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endCreateDate;
    /**
     * 最后修改时间 (开始时间)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginLastUpdateDate;

    /**
     * 最后修改时间 (结束时间)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endLastUpdateDate;

    /**
     * 使用id集合查询
     */
    private List<String> ids;

    /**
     * 确定字段是全匹配还是模糊匹配，likeJson里面的值 key=属性名称，v=boolean(是like)
     */
    private JSONObject likeJson;

    /**
     * 排序集合
     */
    private List<SortAttrDTO> sortList;

    @Override
    public String toString() {
        return JsonUtils.toJSONString(this);
    }
}
