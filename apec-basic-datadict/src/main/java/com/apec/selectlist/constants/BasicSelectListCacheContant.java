package com.apec.selectlist.constants;

import java.lang.*;
import java.util.*;

import com.apec.magpie.cb.constant.IConts;

/**
 */
public interface BasicSelectListCacheContant extends IConts {

    /** 分类类型编码(不能是全部数字) */
    String  DEFAULT_SELECTTYPECODE_VAL = "";
    /** 分类类型名称 */
    String  DEFAULT_SELECTTYPENAME_VAL = "";
    /** 调用的服务名称 */
    String  DEFAULT_SERVICENAME_VAL = "";
    /** 调用服务后面的扩展查询的路径 */
    String  DEFAULT_EXTQUERYPATH_VAL = "";
    /** 刷新时间(分钟) */
    Integer  DEFAULT_FLUSHMINUTE_VAL = 30;
    /** 上次刷新成功时间 */
    Date  DEFAULT_LASTSUCCESSTIME_VAL = null;
    /** 最后失败时间 */
    Date  DEFAULT_LASTFAILTIME_VAL = null;
    /** 失败信息 */
    String  DEFAULT_FAILMSG_VAL = "";


    /**
     * 常量属性,模型名称(BasicSelectListCache) .属性 selectTypeCode
     */
    String C_BASICSELECTLISTCACHE_ATTRNAME_SELECTTYPECODE = "selectTypeCode";
    /**
     * 常量属性,模型名称(BasicSelectListCache) .属性 selectTypeName
     */
    String C_BASICSELECTLISTCACHE_ATTRNAME_SELECTTYPENAME = "selectTypeName";
    /**
     * 常量属性,模型名称(BasicSelectListCache) .属性 serviceName
     */
    String C_BASICSELECTLISTCACHE_ATTRNAME_SERVICENAME = "serviceName";
    /**
     * 常量属性,模型名称(BasicSelectListCache) .属性 extQueryPath
     */
    String C_BASICSELECTLISTCACHE_ATTRNAME_EXTQUERYPATH = "extQueryPath";
    /**
     * 常量属性,模型名称(BasicSelectListCache) .属性 flushMinute
     */
    String C_BASICSELECTLISTCACHE_ATTRNAME_FLUSHMINUTE = "flushMinute";
    /**
     * 常量属性,模型名称(BasicSelectListCache) .属性 lastSuccessTime
     */
    String C_BASICSELECTLISTCACHE_ATTRNAME_LASTSUCCESSTIME = "lastSuccessTime";
    /**
     * 常量属性,模型名称(BasicSelectListCache) .属性 lastFailTime
     */
    String C_BASICSELECTLISTCACHE_ATTRNAME_LASTFAILTIME = "lastFailTime";
    /**
     * 常量属性,模型名称(BasicSelectListCache) .属性 failMsg
     */
    String C_BASICSELECTLISTCACHE_ATTRNAME_FAILMSG = "failMsg";

}
