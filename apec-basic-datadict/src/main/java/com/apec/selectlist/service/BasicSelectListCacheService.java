package com.apec.selectlist.service;

import com.apec.selectlist.model.BasicSelectListCache;
import com.apec.selectlist.dto.BasicSelectListCacheDTO;
import com.apec.selectlist.vo.BasicSelectListCacheVo;
import com.apec.selectlist.constants.BasicSelectListCacheContant;
import com.apec.selectlist.dto.BasicSelectListCacheQueryDTO;
import java.lang.*;
import java.util.*;
import com.apec.framework.common.dto.PageDTO;
import com.apec.framework.common.exception.ApecRuntimeException;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Page;

/**
 */
public interface BasicSelectListCacheService {
    /**
     * 创建一个对象
     * @param dto 对象
     * @param userId 用户编号
     * @return
	 * @throws ApecRuntimeException
     */
    BasicSelectListCacheDTO save(BasicSelectListCacheDTO dto,String userId)
        throws ApecRuntimeException;
	
    /**
     * 逻辑删除一个对象
     * @param id 主键id
     * @param userId 用户编号
     * @return
     * @throws ApecRuntimeException
     */
    BasicSelectListCache delete(String id,String userId)
        throws ApecRuntimeException;

    /**
     * 物理删除一个对象数据
     * @param id 主键id
     * @param userId 用户编号
     * @return
     * @throws ApecRuntimeException
     */
    boolean deleteDB(String id,String userId)
        throws ApecRuntimeException;

    /**
     * 更新排序
     * @param id 主键id
     * @param orderNumber
     * @param userId 用户编号
     * @return
     * @throws ApecRuntimeException
     */
    BasicSelectListCache updateOrderNumber(String id, Integer orderNumber,String userId)
        throws ApecRuntimeException;
	
    /**
     * 更新状态
     * @param id 主键id
     * @param status 状态
     * @param userId 用户编号
     * @return
     * @throws ApecRuntimeException
     */
    BasicSelectListCache updateStatus(String id, String status,String userId)
        throws ApecRuntimeException;
	
    /**
     * 更新对象模型
     * @param vo 对象
     * @param userId 用户编号
     * @return
     * @throws ApecRuntimeException
     */
    BasicSelectListCache update(BasicSelectListCache vo,String userId)
        throws ApecRuntimeException;

    /**
     * 更新对象模型
     * @param vo 对象
     * @param userId 用户编号
     * @return
     * @throws ApecRuntimeException
     */
    BasicSelectListCacheDTO updateDTO(BasicSelectListCacheDTO vo,String userId)
        throws ApecRuntimeException;

    /**
     * 分页查询数据
     * @param dto 查询模型
     * @param pageRequest 分页请求
     * @param userId 用户编号
     * @return
     * @throws ApecRuntimeException
     */
    PageDTO<BasicSelectListCacheDTO> findByPageDTO(BasicSelectListCacheDTO dto, PageRequest pageRequest,String userId)
        throws ApecRuntimeException;
	
	/**
     * 分页查询数据
     * @param dto 查询模型
     * @param pageRequest 分页请求
     * @param userId 用户编号
     * @return
     * @throws ApecRuntimeException
     */
    PageDTO<BasicSelectListCache> findByPage(BasicSelectListCacheDTO dto, PageRequest pageRequest,String userId)
        throws ApecRuntimeException;
	
    /**
     * 根据主键查询对象
     * @param id 主键id
     * @param userId 用户编号
     * @return
     * @throws ApecRuntimeException
     */
    BasicSelectListCache find(String id,String userId)
        throws ApecRuntimeException;

    /**
     * 根据主键查询对象
     * @param id 主键id
     * @param userId 用户编号
     * @return
     * @throws ApecRuntimeException
     */
    BasicSelectListCacheDTO findDTO(String id,String userId)
        throws ApecRuntimeException;

    /**
     * 查询数据列表
     * @param dto
     * @param userId 用户编号
     * @return
     * @throws ApecRuntimeException
     */
    List<BasicSelectListCache> findList(BasicSelectListCacheDTO dto,String userId)
        throws ApecRuntimeException;

   /**
     *  分页查询(扩展查询条件)
     * @param modelQueryDTO
     * @param request
     * @return
     * @throws ApecRuntimeException
     */
    PageDTO<BasicSelectListCacheDTO> findByPageExt(BasicSelectListCacheQueryDTO modelQueryDTO, PageRequest request,String userId)
        throws ApecRuntimeException;

    /**
     * 查询所有(扩展查询条件)
     * @param modelQueryDTO
     * @return
     * @throws ApecRuntimeException
     */
    List<BasicSelectListCacheDTO> findListExt(BasicSelectListCacheQueryDTO modelQueryDTO,String userId)
        throws ApecRuntimeException;

    /**
     * 转换 page的PO转换为PageDTO的DTO
     * @param  page page的PO
     * @return
     */
    PageDTO<BasicSelectListCacheDTO> convertPoToDtoByPage(Page<BasicSelectListCache> page);
    /**
     * 转换 PO 2 DTO
     * @param po
     * @return
     */
    BasicSelectListCacheDTO convertPoToDto(BasicSelectListCache po);

    /**
     * 转换 DTO 2 PO
     * @param dto
     * @return
     */
    BasicSelectListCache convertDtoToPo(BasicSelectListCacheDTO dto);

    /**
     * 转换集合对象
     * @param list
     * @return
     */
    List<BasicSelectListCacheDTO> convertList(List<BasicSelectListCache> list);
}
