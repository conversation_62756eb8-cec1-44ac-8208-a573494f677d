package com.apec.selectlist.service.impl;

import com.apec.selectlist.model.BasicSelectListCache;
import com.apec.selectlist.dto.BasicSelectListCacheDTO;
import com.apec.selectlist.vo.BasicSelectListCacheVo;
import com.apec.selectlist.constants.BasicSelectListCacheContant;
import com.apec.selectlist.dto.BasicSelectListCacheQueryDTO;
import java.lang.*;
import java.util.*;
import java.util.stream.*;
import com.apec.framework.common.dto.PageDTO;
import com.apec.framework.common.enumtype.EnableFlag;
import com.apec.framework.common.exception.ApecRuntimeException;
import com.apec.framework.common.tools.IDGenerator;
import com.apec.framework.common.util.BeanUtils;
import com.apec.framework.common.util.DateUtils;
import org.apache.commons.lang.*;
import com.querydsl.core.types.ExpressionUtils;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.Predicate;
import com.apec.framework.common.util.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Sort;
import com.apec.cache.base.CacheService;
import com.apec.selectlist.service.BasicSelectListCacheService;
import com.apec.selectlist.dao.BasicSelectListCacheDAO;
import com.apec.selectlist.model.QBasicSelectListCache;
import com.alibaba.fastjson.JSONObject;
import com.apec.framework.common.constant.ErrorCodeConsts;
import org.apache.commons.lang3.StringUtils;
import com.apec.magpie.cb.dto.SortAttrDTO;
import com.apec.magpie.cb.enums.EnumDirection;
import org.apache.commons.collections.CollectionUtils;
import com.apec.magpie.cb.common.utils.SortUtils;
import javax.annotation.Resource;

/**
 */
@Service
public class BasicSelectListCacheServiceImpl implements BasicSelectListCacheService,BasicSelectListCacheContant
{

    private final Logger LOG =  LoggerFactory.getLogger( getClass());

    @Autowired
    private BasicSelectListCacheDAO dao;

    @Autowired
    private CacheService cacheService;

	
    @Override
    @Transactional(rollbackFor=Exception.class)
    public BasicSelectListCacheDTO save(BasicSelectListCacheDTO dto,String userId)
        throws ApecRuntimeException
    {

        if(StringUtils.isBlank(userId)){
            throw new ApecRuntimeException(ErrorCodeConsts.COMMON_MISSING_PARAMS,Arrays.asList("userId"));
        }
        if (StringUtils.isBlank(dto.getSelectTypeCode())) {
            dto.setSelectTypeCode(DEFAULT_SELECTTYPECODE_VAL );
        }
        if (StringUtils.isBlank(dto.getSelectTypeName())) {
            dto.setSelectTypeName(DEFAULT_SELECTTYPENAME_VAL );
        }
        if (StringUtils.isBlank(dto.getServiceName())) {
            dto.setServiceName(DEFAULT_SERVICENAME_VAL );
        }
        if (StringUtils.isBlank(dto.getExtQueryPath())) {
            dto.setExtQueryPath(DEFAULT_EXTQUERYPATH_VAL );
        }
        if (null == dto.getFlushMinute()) {
            dto.setFlushMinute(DEFAULT_FLUSHMINUTE_VAL );
        }
        if (null == dto.getLastSuccessTime()) {
            dto.setLastSuccessTime(DEFAULT_LASTSUCCESSTIME_VAL );
        }
        if (null == dto.getLastFailTime()) {
            dto.setLastFailTime(DEFAULT_LASTFAILTIME_VAL );
        }
        if (StringUtils.isBlank(dto.getFailMsg())) {
            dto.setFailMsg(DEFAULT_FAILMSG_VAL );
        }

        //基础字段默认值
        if (null == dto.getCityId()) {
            dto.setCityId(DEFAULT_CITY_ID);
        }
        if (StringUtils.isBlank(dto.getStatus())) {
            dto.setStatus(STATUS_INVALID);
        }
        if (StringUtils.isBlank(dto.getPlantformId())) {
            dto.setPlantformId(DEFAULT_PLANTFORM_ID);
        }
        if (StringUtils.isBlank(dto.getOecdNo())) {
            dto.setOecdNo(DEFAULT_OECD_NO);
        }
        if (null == dto.getEnableFlag()) {
            dto.setEnableFlag(EnableFlag.Y);
        }
        if (null == dto.getCreateDate()) {
            dto.setCreateDate(new Date());
        }
        if (StringUtils.isBlank(dto.getCreateBy())) {
            dto.setCreateBy(userId);
        }
        if (null == dto.getOrderNumber()) {
            dto.setOrderNumber(1);
        }

        //属性拷贝保存到数据库刷新缓存
        BasicSelectListCache entity = new BasicSelectListCache();
        BeanUtils.copyPropertiesIgnoreNullFilds(dto, entity, new String[] {});

        if(StringUtils.isBlank(entity.getId())){
            entity.setId(IDGenerator.getNextId().toString());
        }

        dto.setId(entity.getId());
        dao.save(entity);
        return dto;
    }

    @Override
    public BasicSelectListCache delete(String id,String userId)
        throws ApecRuntimeException
    {
        if(StringUtils.isBlank(id) || StringUtils.isBlank(userId)){
            throw new ApecRuntimeException(ErrorCodeConsts.COMMON_MISSING_PARAMS,Arrays.asList("id userId"));
        }
        LOG.info("{},逻辑删除数据 id:{}",userId,id);
        BasicSelectListCache entity = dao.findOne(id);
        if(null == entity){
            throw new ApecRuntimeException(ErrorCodeConsts.ERROR_QUERY_DATA_NOT_EXIST_BY_ID);
        }
        entity.setEnableFlag(EnableFlag.N);

        if(StringUtils.isNotBlank(userId)){
            entity.setLastUpdateBy(userId);
        }
        entity.setLastUpdateDate(new Date());

        BasicSelectListCache rs = dao.saveAndFlush(entity);
        return rs;
    }

    @Override
    public boolean deleteDB(String id,String userId)
        throws ApecRuntimeException
    {
        if(StringUtils.isBlank(id) || StringUtils.isBlank(userId)){
            throw new ApecRuntimeException(ErrorCodeConsts.COMMON_MISSING_PARAMS,Arrays.asList("id userId"));
        }
        LOG.info("{},物理删除数据 id:{}",userId,id);
        BasicSelectListCache entity = dao.findOne(id);
        if(null == entity){
            throw new ApecRuntimeException(ErrorCodeConsts.ERROR_QUERY_DATA_NOT_EXIST_BY_ID);
        }
        LOG.info("删除的物理数据详情:{}",JsonUtils.toJSONString(entity));
        dao.delete(id);
        return true;
    }

    @Override
    public  BasicSelectListCache updateOrderNumber(String id, Integer orderNumber,String userId)
        throws ApecRuntimeException
    {
        if(StringUtils.isBlank(id) || null == orderNumber || StringUtils.isBlank(userId) ){
            throw new ApecRuntimeException(ErrorCodeConsts.COMMON_MISSING_PARAMS, Arrays.asList("id orderNumber userId"));
        }
        BasicSelectListCache entity = dao.findOne(id);
        entity.setOrderNumber(orderNumber);
        if(StringUtils.isNotBlank(userId)){
            entity.setLastUpdateBy(userId);
        }
        entity.setLastUpdateDate(new Date());
        dao.saveAndFlush(entity);
        return entity;
    }

    @Override
    public  BasicSelectListCache updateStatus(String id, String status,String userId)
        throws ApecRuntimeException
    {
        if(StringUtils.isBlank(id) || StringUtils.isBlank(status)
            || StringUtils.isBlank(userId)){
            throw new ApecRuntimeException(ErrorCodeConsts.COMMON_MISSING_PARAMS, Arrays.asList("id status userId"));
        }
        BasicSelectListCache entity = dao.findOne(id);
        entity.setStatus(status);
        if(StringUtils.isNotBlank(userId)){
            entity.setLastUpdateBy(userId);
        }
        entity.setLastUpdateDate(new Date());
        dao.saveAndFlush(entity);
        return entity;
    }

    @Override
    public BasicSelectListCache update(BasicSelectListCache obj,String userId)
        throws ApecRuntimeException
    {
        if(null == obj || StringUtils.isBlank(userId)){
            throw new ApecRuntimeException(ErrorCodeConsts.COMMON_MISSING_PARAMS, Arrays.asList("obj userId"));
        }
        if(StringUtils.isBlank(obj.getId())){
            throw new ApecRuntimeException(ErrorCodeConsts.ERROR_QUERY_DATA_NOT_EXIST_BY_ID);
        }

        BasicSelectListCache entity = dao.findOne(obj.getId());
        if(null == entity){
			LOG.error("查询数据为空,id={}",obj.getId());
			return null;
        }
		//模块属性设置
        if (StringUtils.isNotBlank(obj.getSelectTypeCode())) {
			entity.setSelectTypeCode(obj.getSelectTypeCode());
        }
        if (StringUtils.isNotBlank(obj.getSelectTypeName())) {
			entity.setSelectTypeName(obj.getSelectTypeName());
        }
        if (StringUtils.isNotBlank(obj.getServiceName())) {
			entity.setServiceName(obj.getServiceName());
        }
        if (StringUtils.isNotBlank(obj.getExtQueryPath())) {
			entity.setExtQueryPath(obj.getExtQueryPath());
        }
        if (null != obj.getFlushMinute()) {
			entity.setFlushMinute(obj.getFlushMinute());
        }
        if (null != obj.getLastSuccessTime()) {
			entity.setLastSuccessTime(obj.getLastSuccessTime());
        }
        if (null != obj.getLastFailTime()) {
			entity.setLastFailTime(obj.getLastFailTime());
        }
        if (StringUtils.isNotBlank(obj.getFailMsg())) {
			entity.setFailMsg(obj.getFailMsg());
        }

        //基础属性设置
        if (StringUtils.isNotBlank(obj.getId())) {
            entity.setId(obj.getId());
        }
        if (StringUtils.isNotBlank(obj.getStatus())) {
            entity.setStatus(obj.getStatus());
        }
        if (null != obj.getEnableFlag()) {
            entity.setEnableFlag(obj.getEnableFlag());
        }
        if (null != obj.getCreateDate()) {
            entity.setCreateDate(obj.getCreateDate());
        }
        if (StringUtils.isNotBlank(obj.getCreateBy())) {
            entity.setCreateBy(obj.getCreateBy());
        }
        if (StringUtils.isNotBlank(obj.getLastUpdateBy())) {
            entity.setLastUpdateBy(obj.getLastUpdateBy());
        }
        if (null != obj.getLastUpdateDate()) {
            entity.setLastUpdateDate(obj.getLastUpdateDate());
        }
        if (StringUtils.isNotBlank(obj.getPlantformId())) {
            entity.setPlantformId(obj.getPlantformId());
        }
        if (StringUtils.isNotBlank(obj.getOecdNo())) {
            entity.setOecdNo(obj.getOecdNo());
        }
        if (null != obj.getCityId()) {
            entity.setCityId(obj.getCityId());
        }
        if (null != obj.getOrderNumber()) {
            entity.setOrderNumber(obj.getOrderNumber());
        }
        if (StringUtils.isNotBlank(obj.getRemarks())) {
            entity.setRemarks(obj.getRemarks());
        }

        if(StringUtils.isBlank(entity.getLastUpdateBy())) {
            entity.setLastUpdateBy(userId);
        }
        if(null == entity.getLastUpdateDate()) {
            entity.setLastUpdateDate(new Date());
        }

        dao.saveAndFlush(entity);
        return entity;
    }

    @Override
    public BasicSelectListCacheDTO updateDTO(BasicSelectListCacheDTO dto,String userId)
    throws ApecRuntimeException
    {
        BasicSelectListCache po = update(convertDtoToPo(dto),userId);
        return convertPoToDto(po);
    }

    @Override
    public BasicSelectListCache find(String id,String userId)
        throws ApecRuntimeException
    {
        BasicSelectListCache entity = dao.findOne(id);
        return entity;
    }

    @Override
    public BasicSelectListCacheDTO findDTO(String id,String userId)
        throws ApecRuntimeException
    {
        BasicSelectListCache entity = dao.findOne(id);
        return convertPoToDto(entity);
    }


    @Override
    public PageDTO<BasicSelectListCacheDTO> findByPageDTO(BasicSelectListCacheDTO dto, PageRequest pageRequest,String userId)
        throws ApecRuntimeException
    {
        PageRequest sortPageRequest = new PageRequest(pageRequest.getPageNumber(),pageRequest.getPageSize(),
        SortUtils.getSortDirection(dto.getSortList()),SortUtils.getSortAttr(dto.getSortList()));
        Page<BasicSelectListCache> page = dao.findAll(getInputCondition(dto), sortPageRequest);
        return convertPoToDtoByPage(page);
    }

    @Override
    public PageDTO<BasicSelectListCache> findByPage(BasicSelectListCacheDTO dto, PageRequest pageRequest,String userId)
        throws ApecRuntimeException
    {
        PageRequest sortPageRequest = new PageRequest(pageRequest.getPageNumber(),pageRequest.getPageSize(),
        SortUtils.getSortDirection(dto.getSortList()),SortUtils.getSortAttr(dto.getSortList()));
        Page<BasicSelectListCache> page = dao.findAll(getInputCondition(dto), sortPageRequest);
        return new PageDTO<>(page.getTotalElements(), page.getTotalPages(), page.getNumber(), page.getContent());
    }

    @Override
    public List<BasicSelectListCache> findList(BasicSelectListCacheDTO dto,String userId)
        throws ApecRuntimeException
    {
        BasicSelectListCache entity = new BasicSelectListCache();
        BeanUtils.copyProperties(dto, entity);
        return queryAllModel(entity, dto.getLikeJson(), dto.getSortList());
    }

    public List<BasicSelectListCache> queryAllModel(BasicSelectListCache entity,JSONObject likeJson, List<SortAttrDTO> sortList) {
        //排序
        Sort sort = SortUtils.getSort(sortList);
        Iterable<BasicSelectListCache> iterable = dao.findAll(getInputConditionModel(entity, likeJson), sort);
        Iterator<BasicSelectListCache> iterator = iterable.iterator();
        List<BasicSelectListCache> list = new ArrayList<>();
        while (iterator.hasNext()) {
            BasicSelectListCache img = iterator.next();
            list.add(img);
        }
        return list;
    }

    @Override
    public PageDTO<BasicSelectListCacheDTO> findByPageExt(BasicSelectListCacheQueryDTO modelQueryDTO, PageRequest pageRequest,String userId)
        throws ApecRuntimeException
    {
        PageRequest sortPageRequest = new PageRequest(pageRequest.getPageNumber(),pageRequest.getPageSize(),
        SortUtils.getSortDirection(modelQueryDTO.getSortList()),SortUtils.getSortAttr(modelQueryDTO.getSortList()));
        Page<BasicSelectListCache> page = dao.findAll(getInputCondition(modelQueryDTO), sortPageRequest);
        return convertPoToDtoByPage(page);
    }

    @Override
    public List<BasicSelectListCacheDTO> findListExt(BasicSelectListCacheQueryDTO modelQueryDTO,String userId)
        throws ApecRuntimeException
    {
        Sort sort = SortUtils.getSort(modelQueryDTO.getSortList());
        Iterable<BasicSelectListCache> iterable =  dao.findAll(getInputCondition(modelQueryDTO), sort);
        List<BasicSelectListCacheDTO> listModelDTO = new ArrayList<>();
        iterable.forEach(po->{
            listModelDTO.add(convertPoToDto(po));

        });
        return listModelDTO;
    }



    /**
     * 获取对象的缓存Key
     * @param entity 对象
     * @return
     */
    private String getCacheKey(BasicSelectListCache entity) {
        return CACHE_PREFIX + "BasicSelectListCache_List";
    }

    /**
     * 刷新类型的数据到数据库 （修改状态就需要刷新）
     * @param entity
     */
    private void flushCache(BasicSelectListCache entity) {
        List<BooleanExpression> predicates = new ArrayList<>();
        predicates.add(QBasicSelectListCache.basicSelectListCache.enableFlag.eq(EnableFlag.Y));
        predicates.add(QBasicSelectListCache.basicSelectListCache.status.eq(STATUS_VALID));

        Predicate predicate = ExpressionUtils.allOf(predicates.toArray(new BooleanExpression[predicates.size()]));
        Sort sort = SortUtils.getSort(new ArrayList());
        Iterable<BasicSelectListCache> page = dao.findAll(predicate, sort);
        List<BasicSelectListCache> list = new ArrayList<BasicSelectListCache>();
            page.forEach(one -> {
                list.add(one);
            });
        cacheService.add(getCacheKey(entity), JsonUtils.toJSONString(list));
        LOG.debug("刷新数据到缓存：" + getCacheKey(entity) + " \n" + JsonUtils.toJSONString(list));
    }

    /**
     * 获取基础模型的JPA查询列表表达式
     * @param entity 对象
     * @param likeJson 属性模糊查询标识
     * @return
     */
    private List<BooleanExpression> getBooleanExpressionList4Base(BasicSelectListCache entity, JSONObject likeJson) {
        List<BooleanExpression> list = new ArrayList<BooleanExpression>();
        if (StringUtils.isNotBlank(entity.getId())) {
            if(likeJson.containsKey("id") && likeJson.getBoolean("id")) {
                list.add(QBasicSelectListCache.basicSelectListCache.id.like(getQueryLikeParam(entity.getId())));
            }else{
                list.add(QBasicSelectListCache.basicSelectListCache.id.eq(entity.getId()));
            }
        }

        if (null == entity.getEnableFlag()) {
            list.add(QBasicSelectListCache.basicSelectListCache.enableFlag.eq(EnableFlag.Y));
        } else {
            list.add(QBasicSelectListCache.basicSelectListCache.enableFlag.eq(entity.getEnableFlag()));
        }

        if (null != entity.getCityId()) {
            list.add(QBasicSelectListCache.basicSelectListCache.cityId.eq(entity.getCityId()));
        }
        if (StringUtils.isNotBlank(entity.getStatus())) {
            list.add(QBasicSelectListCache.basicSelectListCache.status.eq(entity.getStatus()));
        }

        if (null != entity.getCreateDate()) {
            list.add(QBasicSelectListCache.basicSelectListCache.createDate.eq(entity.getCreateDate()));
        }
        if (null != entity.getLastUpdateDate()) {
            list.add(QBasicSelectListCache.basicSelectListCache.lastUpdateDate.eq(entity.getLastUpdateDate()));
        }
        if (StringUtils.isNotBlank(entity.getCreateBy())) {
            list.add(QBasicSelectListCache.basicSelectListCache.createBy.eq(entity.getCreateBy()));
        }
        if (StringUtils.isNotBlank(entity.getLastUpdateBy())) {
            list.add(QBasicSelectListCache.basicSelectListCache.lastUpdateBy.eq(entity.getLastUpdateBy()));
        }
        if (StringUtils.isNotBlank(entity.getPlantformId())) {
            list.add(QBasicSelectListCache.basicSelectListCache.plantformId.eq(entity.getPlantformId()));
        }
        if (StringUtils.isNotBlank(entity.getOecdNo())) {
            list.add(QBasicSelectListCache.basicSelectListCache.oecdNo.eq(entity.getOecdNo()));
        }
        if (null != entity.getOrderNumber()) {
            list.add(QBasicSelectListCache.basicSelectListCache.orderNumber.eq(entity.getOrderNumber()));
        }
        if (StringUtils.isNotBlank(entity.getRemarks())) {
            list.add(QBasicSelectListCache.basicSelectListCache.remarks.eq(entity.getRemarks()));
        }
        return list;
    }

    /**
     * 获取对象模型的JPA查询列表表达式
     * @param entity 对象
     * @param likeJson 属性模糊查询标识
     * @return
     */
    private List<BooleanExpression> getBooleanExpressionList4Model(BasicSelectListCache entity, JSONObject likeJson) {
        if(null == likeJson){
            likeJson = new JSONObject();
        }
        List<BooleanExpression> list = getBooleanExpressionList4Base(entity, likeJson);

        if (StringUtils.isNotBlank(entity.getSelectTypeCode())) {
            if(likeJson.containsKey(C_BASICSELECTLISTCACHE_ATTRNAME_SELECTTYPECODE) && likeJson.getBoolean(C_BASICSELECTLISTCACHE_ATTRNAME_SELECTTYPECODE)) {
                list.add(QBasicSelectListCache.basicSelectListCache.selectTypeCode.like(getQueryLikeParam(entity.getSelectTypeCode())));
            }else{
                list.add(QBasicSelectListCache.basicSelectListCache.selectTypeCode.eq(entity.getSelectTypeCode()));
            }
        }


        if (StringUtils.isNotBlank(entity.getSelectTypeName())) {
            if(likeJson.containsKey(C_BASICSELECTLISTCACHE_ATTRNAME_SELECTTYPENAME) && likeJson.getBoolean(C_BASICSELECTLISTCACHE_ATTRNAME_SELECTTYPENAME)) {
                list.add(QBasicSelectListCache.basicSelectListCache.selectTypeName.like(getQueryLikeParam(entity.getSelectTypeName())));
            }else{
                list.add(QBasicSelectListCache.basicSelectListCache.selectTypeName.eq(entity.getSelectTypeName()));
            }
        }


        if (StringUtils.isNotBlank(entity.getServiceName())) {
            if(likeJson.containsKey(C_BASICSELECTLISTCACHE_ATTRNAME_SERVICENAME) && likeJson.getBoolean(C_BASICSELECTLISTCACHE_ATTRNAME_SERVICENAME)) {
                list.add(QBasicSelectListCache.basicSelectListCache.serviceName.like(getQueryLikeParam(entity.getServiceName())));
            }else{
                list.add(QBasicSelectListCache.basicSelectListCache.serviceName.eq(entity.getServiceName()));
            }
        }


        if (StringUtils.isNotBlank(entity.getExtQueryPath())) {
            if(likeJson.containsKey(C_BASICSELECTLISTCACHE_ATTRNAME_EXTQUERYPATH) && likeJson.getBoolean(C_BASICSELECTLISTCACHE_ATTRNAME_EXTQUERYPATH)) {
                list.add(QBasicSelectListCache.basicSelectListCache.extQueryPath.like(getQueryLikeParam(entity.getExtQueryPath())));
            }else{
                list.add(QBasicSelectListCache.basicSelectListCache.extQueryPath.eq(entity.getExtQueryPath()));
            }
        }


        if (null != entity.getFlushMinute()) {
            list.add(QBasicSelectListCache.basicSelectListCache.flushMinute.eq(entity.getFlushMinute()));
        }


        if (null != entity.getLastSuccessTime()) {
            list.add(QBasicSelectListCache.basicSelectListCache.lastSuccessTime.eq(entity.getLastSuccessTime()));
        }


        if (null != entity.getLastFailTime()) {
            list.add(QBasicSelectListCache.basicSelectListCache.lastFailTime.eq(entity.getLastFailTime()));
        }


        if (StringUtils.isNotBlank(entity.getFailMsg())) {
            if(likeJson.containsKey(C_BASICSELECTLISTCACHE_ATTRNAME_FAILMSG) && likeJson.getBoolean(C_BASICSELECTLISTCACHE_ATTRNAME_FAILMSG)) {
                list.add(QBasicSelectListCache.basicSelectListCache.failMsg.like(getQueryLikeParam(entity.getFailMsg())));
            }else{
                list.add(QBasicSelectListCache.basicSelectListCache.failMsg.eq(entity.getFailMsg()));
            }
        }


        return list;
    }

    /**
     * 处理queryDTO参数
     * @param modelQueryDTO
     * @return
     * @throws ApecRuntimeException
     */
    private List<BooleanExpression> getBooleanExpressionList4ModelQueryDTO(BasicSelectListCacheQueryDTO modelQueryDTO)
        throws ApecRuntimeException
    {
        BasicSelectListCache model = new BasicSelectListCache();
        BeanUtils.copyPropertiesIgnoreNullFilds(modelQueryDTO,model);
        List<BooleanExpression> listBooleanExpression = getBooleanExpressionList4Model(model,modelQueryDTO.getLikeJson());
        /* 模型扩展 时间区间属性 处理开始... */
        if(null != modelQueryDTO.getBeginCreateDate() && null != modelQueryDTO.getEndCreateDate()){
            String startDate = DateUtils.formatDate(modelQueryDTO.getBeginCreateDate(), DateUtils.FORMAT_DATE);
            String endDate = DateUtils.formatDate(modelQueryDTO.getEndCreateDate(), DateUtils.FORMAT_DATE);
            String startDateTime = startDate + SERVICE_QUERY_DATE_TIME_START;
            String endDateTime = endDate + SERVICE_QUERY_DATE_TIME_END;
            listBooleanExpression.add(QBasicSelectListCache.basicSelectListCache.createDate.between(
            DateUtils.formatDates(startDateTime, DateUtils.FORMAT_DATE_TIME),
            DateUtils.formatDates(endDateTime, DateUtils.FORMAT_DATE_TIME)));
        }

        if(null != modelQueryDTO.getBeginUpdateDate() && null != modelQueryDTO.getEndUpdateDate()){
            String startDate = DateUtils.formatDate(modelQueryDTO.getBeginUpdateDate(), DateUtils.FORMAT_DATE);
            String endDate = DateUtils.formatDate(modelQueryDTO.getEndUpdateDate(), DateUtils.FORMAT_DATE);
            String startDateTime = startDate + SERVICE_QUERY_DATE_TIME_START;
            String endDateTime = endDate + SERVICE_QUERY_DATE_TIME_END;
            listBooleanExpression.add(QBasicSelectListCache.basicSelectListCache.lastUpdateDate.between(
            DateUtils.formatDates(startDateTime, DateUtils.FORMAT_DATE_TIME),
            DateUtils.formatDates(endDateTime, DateUtils.FORMAT_DATE_TIME)));
        }
        if(null != modelQueryDTO.getBeginLastSuccessTime() && null != modelQueryDTO.getEndLastSuccessTime()){
            String startDate = DateUtils.formatDate(modelQueryDTO.getBeginLastSuccessTime(), DateUtils.FORMAT_DATE);
            String endDate = DateUtils.formatDate(modelQueryDTO.getEndLastSuccessTime(), DateUtils.FORMAT_DATE);
            String startDateTime = startDate + SERVICE_QUERY_DATE_TIME_START;
            String endDateTime = endDate + SERVICE_QUERY_DATE_TIME_END;
            listBooleanExpression.add(QBasicSelectListCache.basicSelectListCache.lastSuccessTime.between(
            DateUtils.formatDates(startDateTime, DateUtils.FORMAT_DATE_TIME),
            DateUtils.formatDates(endDateTime, DateUtils.FORMAT_DATE_TIME)));
        }
        if(null != modelQueryDTO.getBeginLastFailTime() && null != modelQueryDTO.getEndLastFailTime()){
            String startDate = DateUtils.formatDate(modelQueryDTO.getBeginLastFailTime(), DateUtils.FORMAT_DATE);
            String endDate = DateUtils.formatDate(modelQueryDTO.getEndLastFailTime(), DateUtils.FORMAT_DATE);
            String startDateTime = startDate + SERVICE_QUERY_DATE_TIME_START;
            String endDateTime = endDate + SERVICE_QUERY_DATE_TIME_END;
            listBooleanExpression.add(QBasicSelectListCache.basicSelectListCache.lastFailTime.between(
            DateUtils.formatDates(startDateTime, DateUtils.FORMAT_DATE_TIME),
            DateUtils.formatDates(endDateTime, DateUtils.FORMAT_DATE_TIME)));
        }
        /* 模型扩展 时间区间属性 处理完成. */


        /* 模型扩展 集合属性 处理开始... */
        if(CollectionUtils.isNotEmpty(modelQueryDTO.getIds())) {
            listBooleanExpression.add(QBasicSelectListCache.basicSelectListCache.id.in(modelQueryDTO.getIds()));
        }
        if(CollectionUtils.isNotEmpty(modelQueryDTO.getStatusLS())) {
            listBooleanExpression.add(QBasicSelectListCache.basicSelectListCache.status.in(modelQueryDTO.getStatusLS()));
        }
        if(CollectionUtils.isNotEmpty(modelQueryDTO.getPlantformIdLS())) {
            listBooleanExpression.add(QBasicSelectListCache.basicSelectListCache.plantformId.in(modelQueryDTO.getPlantformIdLS()));
        }
        if(CollectionUtils.isNotEmpty(modelQueryDTO.getOecdNoLS())) {
            listBooleanExpression.add(QBasicSelectListCache.basicSelectListCache.oecdNo.in(modelQueryDTO.getOecdNoLS()));
        }

        if(CollectionUtils.isNotEmpty(modelQueryDTO.getSelectTypeCodeLS())) {
            listBooleanExpression.add(QBasicSelectListCache.basicSelectListCache.selectTypeCode.in(modelQueryDTO.getSelectTypeCodeLS()));
        }
        if(CollectionUtils.isNotEmpty(modelQueryDTO.getSelectTypeNameLS())) {
            listBooleanExpression.add(QBasicSelectListCache.basicSelectListCache.selectTypeName.in(modelQueryDTO.getSelectTypeNameLS()));
        }
        if(CollectionUtils.isNotEmpty(modelQueryDTO.getServiceNameLS())) {
            listBooleanExpression.add(QBasicSelectListCache.basicSelectListCache.serviceName.in(modelQueryDTO.getServiceNameLS()));
        }
        if(CollectionUtils.isNotEmpty(modelQueryDTO.getExtQueryPathLS())) {
            listBooleanExpression.add(QBasicSelectListCache.basicSelectListCache.extQueryPath.in(modelQueryDTO.getExtQueryPathLS()));
        }
        if(CollectionUtils.isNotEmpty(modelQueryDTO.getFlushMinuteLS())) {
            listBooleanExpression.add(QBasicSelectListCache.basicSelectListCache.flushMinute.in(modelQueryDTO.getFlushMinuteLS()));
        }
        if(CollectionUtils.isNotEmpty(modelQueryDTO.getLastSuccessTimeLS())) {
            listBooleanExpression.add(QBasicSelectListCache.basicSelectListCache.lastSuccessTime.in(modelQueryDTO.getLastSuccessTimeLS()));
        }
        if(CollectionUtils.isNotEmpty(modelQueryDTO.getLastFailTimeLS())) {
            listBooleanExpression.add(QBasicSelectListCache.basicSelectListCache.lastFailTime.in(modelQueryDTO.getLastFailTimeLS()));
        }
        if(CollectionUtils.isNotEmpty(modelQueryDTO.getFailMsgLS())) {
            listBooleanExpression.add(QBasicSelectListCache.basicSelectListCache.failMsg.in(modelQueryDTO.getFailMsgLS()));
        }
        /* 模型扩展 集合属性 处理结束... */
        return listBooleanExpression;
    }


    /**
     * 获取对象模型的JPA查询断言
     * @param entity 模型对象
     * @return
     */
    private Predicate getInputConditionModel(BasicSelectListCache entity, JSONObject likeJson) {
        List<BooleanExpression> predicates = getBooleanExpressionList4Model(entity,likeJson);
        return ExpressionUtils.allOf(predicates.toArray(new BooleanExpression[predicates.size()]));
    }

    /**
     * 获取Vo对象的JPA查询断言
     * @param vo VO对象
     * @return
     */
	private Predicate getInputCondition(BasicSelectListCacheVo vo) {
		BasicSelectListCache entity = new BasicSelectListCache();
        BeanUtils.copyProperties(vo, entity);
        List<BooleanExpression> predicates = getBooleanExpressionList4Model(entity,vo.getLikeJson());
        return ExpressionUtils.allOf(predicates.toArray(new BooleanExpression[predicates.size()]));
	}
	
    /**
     * 获取Dto对象的JPA查询断言
     * @param dto DTO对象
     * @return
     */
	private Predicate getInputCondition(BasicSelectListCacheDTO dto) {
        BasicSelectListCache entity = new BasicSelectListCache();
        BeanUtils.copyProperties(dto, entity);
        List<BooleanExpression> predicates = getBooleanExpressionList4Model(entity,dto.getLikeJson());
        return ExpressionUtils.allOf(predicates.toArray(new BooleanExpression[predicates.size()]));
	}

    /**
     * 处理模型扩展DTO
     * @param modelQueryDTO
     * @return
     * @throws ApecRuntimeException
     */
    private Predicate getInputCondition(BasicSelectListCacheQueryDTO modelQueryDTO)
        throws ApecRuntimeException
    {
        List<BooleanExpression> listBooleanExpression = getBooleanExpressionList4ModelQueryDTO(modelQueryDTO);
        return ExpressionUtils.allOf(listBooleanExpression.toArray(new BooleanExpression[listBooleanExpression.size()]));
    }

    @Override
    public PageDTO<BasicSelectListCacheDTO> convertPoToDtoByPage(Page<BasicSelectListCache> page)
    {
        List<BasicSelectListCacheDTO> dtoList = page.getContent().stream().map(po -> {
            return convertPoToDto(po);
        }).collect(Collectors.toList());
        return new PageDTO<>(page.getTotalElements(), page.getTotalPages(), page.getNumber(), dtoList);
    }

    @Override
    public BasicSelectListCacheDTO convertPoToDto(BasicSelectListCache po)
    {

        if(null == po) {
            return null;
        }
        BasicSelectListCacheDTO dto = new BasicSelectListCacheDTO();
        BeanUtils.copyPropertiesIgnoreNullFilds(po, dto);
        return dto;
    }

    @Override
    public BasicSelectListCache convertDtoToPo(BasicSelectListCacheDTO dto)
    {

        if(null == dto) {
            return null;
        }
        BasicSelectListCache po = new BasicSelectListCache();
        BeanUtils.copyPropertiesIgnoreNullFilds(dto, po);
        return po;
    }

    @Override
    public List<BasicSelectListCacheDTO> convertList(List<BasicSelectListCache> list){
        return list.stream().map(po -> { return convertPoToDto(po); }).collect(Collectors.toList());
    }
    /**
     * like查询的值
     * @param attrVal
     * @return
     */
    private String getQueryLikeParam(String attrVal){
        return SERVICE_QUERY_CHAR_PERCENT + attrVal + SERVICE_QUERY_CHAR_PERCENT;
    }
}
