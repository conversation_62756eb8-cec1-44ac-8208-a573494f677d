<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>apec-basic-datadict</artifactId>
    <version>1.6.1-RELEASE</version>
    <parent>
        <artifactId>APEC_CJ101_DataDict</artifactId>
        <groupId>com.apec</groupId>
        <version>1.5.0-RELEASE</version>
    </parent>

    <dependencies>
        <!--依赖项底层接口和模块 start -->
        <dependency>
            <groupId>com.apec</groupId>
            <artifactId>magpie-cb-spring-jpa</artifactId>
            <version>1.0.0-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.apec</groupId>
            <artifactId>magpie-cb-spring-util</artifactId>
            <version>1.0.0-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.apec</groupId>
            <artifactId>magpie-cb-springcloud-util</artifactId>
            <version>1.1.0-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.apec</groupId>
            <artifactId>magpie-cb-spring-cache-redis</artifactId>
            <version>1.1.0-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.apec</groupId>
            <artifactId>magpie-cb-base-formjson-log</artifactId>
            <version>1.1.0-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.apec</groupId>
            <artifactId>magpie-cb-base-thread-local</artifactId>
            <version>1.1.0-RELEASE</version>
        </dependency>

        <!--依赖项底层接口和模块 end -->

        <dependency>
            <groupId>com.apec</groupId>
            <artifactId>magpie-cb-util</artifactId>
            <version>1.0.0-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.apec</groupId>
            <artifactId>magpie-cb</artifactId>
            <version>1.6.0-RELEASE</version>
        </dependency>
    </dependencies>

    <!-- 打包方式 -->
    <build>
        <finalName>${project.artifactId}-${project.version}</finalName>
        <plugins>

            <!-- querydsl 插件 自动生成Qmodle，简化Criteria操作 -->
            <plugin>
                <groupId>com.mysema.maven</groupId>
                <artifactId>apt-maven-plugin</artifactId>
                <version>1.1.3</version>
                <executions>
                    <execution>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>process</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>target/generated-sources/java</outputDirectory>
                            <processor>com.querydsl.apt.jpa.JPAAnnotationProcessor</processor>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

        </plugins>

    </build>
</project>


<!--

DTO添加
private List<BasicDataDictSubDTO> dictSubList;

-->
