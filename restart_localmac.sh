#!/bin/bash
puser=apple
pname=datadict-server-1.0.0-RELEASE.jar
var=$(date +%Y%m%d%H%M%S)

#----------begin restart jar----------

pid=`ps aux | grep $pname | grep $puser | grep -v grep | awk '{print $2}'`

if [[ -z $pid ]]; then
echo "I can NOT find $pname running by $puser"
fi

kill -9 $pid >/dev/null 2>&1

#引入java环境变量
.  /etc/profile
exec nohup java -Xmx125m -Xss256k -jar $pname  --spring.profiles.active=localmac --logging.level.com.apec=DEBUG --cacheType=single 5 >/dev/null 2>&1 &
#---------- restart jar end!----------



