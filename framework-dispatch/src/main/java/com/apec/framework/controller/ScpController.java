package com.apec.framework.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.apec.framework.base.BaseController;
import com.apec.framework.base.IJSONService;
import com.apec.framework.common.constant.ConfigConsts;
import com.apec.framework.common.exception.DispatchException;
import com.apec.redis.common.util.StringUtil;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 供应链金融 匹配
 *
 * <AUTHOR>
 * @date 2020/2/15
 */
@RestController
public class ScpController extends BaseController {

    @NacosValue(value = "${scp.server.map:}", autoRefreshed = true)
    private String serverMap;

    @Autowired
    private IJSONService dispatchJSONService;

    /**
     * 请求分发
     *
     * @param serverName 服务名称
     * @param methodName 调用方法名
     * @param request    请求信息
     * @return 请求返回结果
     */
    @RequestMapping(value = "/{serverPath}/{methodName}.scp", produces = "application/json;charset=UTF-8")
    public String dispatchRequest(@PathVariable("serverPath") String serverPath,
                                  @PathVariable("methodName") String methodName, HttpServletRequest request, HttpServletResponse response) {
        return sendRequest(request, getServerName(serverPath), methodName, response);
    }

    /**
     * 请求分发
     *
     * @param serverName 服务名称
     * @param methodName 调用方法名
     * @param fileName   文件名称
     * @param request    请求信息
     * @return 请求返回结果
     */
    @RequestMapping(value = "/{serverPath}/{fileName}/{methodName}.scp", method = {RequestMethod.GET, RequestMethod.POST, RequestMethod.OPTIONS}, produces = "application/json;charset=UTF-8")
    public String dispatchRequest(@PathVariable("serverPath") String serverPath,
                                  @PathVariable("fileName") String fileName, @PathVariable("methodName") String methodName,
                                  HttpServletRequest request, HttpServletResponse response) {
        return sendRequest(request, getServerName(serverPath), fileName + ConfigConsts.SINGLE_SLASH + methodName, response);
    }

    /**
     * 发送请求
     *
     * @param request    请求
     * @param serverName 服务名称
     * @return 请求返回结果
     */
    private String sendRequest(HttpServletRequest request, String serverName, String methodName,
                               HttpServletResponse response) {
        try {
            return dispatchJSONService.service(serverName, methodName, request, response);
        } catch (DispatchException e) {
            return super.getResultJSONStr(false, "", e.getErrorCode());
        }
    }

    /**
     * 获取服务名
     *
     * @param serverPath 服务路径
     * @return 服务名
     */
    private String getServerName(String serverPath) {
        if (StringUtil.isEmpty(serverMap)) {
            return serverPath;
        }
        return Optional.ofNullable(JSONObject.parseObject(serverMap).getString(serverPath)).orElse(serverPath);
    }

}
