package com.apec.framework.controller;

import com.apec.framework.base.BaseController;
import com.apec.framework.base.IJSONService;
import com.apec.framework.common.constant.ConfigConsts;
import com.apec.framework.common.exception.DispatchException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Auther: liliwei
 * @Date: 2018/11/5 10:55
 * @Description:
 */
@RestController
public class DzController extends BaseController
{

    @Autowired
    private IJSONService dispatchJSONService;

    @RequestMapping(value = "/{serverName}/{methodName}.dz", produces = "application/json;charset=UTF-8")
    public String dispatchRequest(@PathVariable("serverName") String serverName,
        @PathVariable("methodName") String methodName, HttpServletRequest request, HttpServletResponse response) {
        return sendRequest(request, serverName, methodName, response);
    }

    @RequestMapping(value = "/{serverName}/{fileName}/{methodName}.dz", produces = "application/json;charset=UTF-8")
    public String dispatchRequest(@PathVariable("serverName") String serverName,
        @PathVariable("fileName") String fileName, @PathVariable("methodName") String methodName,
        HttpServletRequest request, HttpServletResponse response) {
        return sendRequest(request, serverName, fileName + ConfigConsts.SINGLE_SLASH + methodName, response);
    }


    private String sendRequest(HttpServletRequest request, String serverName, String methodName,
        HttpServletResponse response) {
        try {
            return dispatchJSONService.service(serverName, methodName, request, response);
        } catch (DispatchException e) {
            return super.getResultJSONStr(false, "", e.getErrorCode());
        }
    }

}
