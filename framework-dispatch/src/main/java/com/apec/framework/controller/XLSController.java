package com.apec.framework.controller;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;


import com.apec.framework.Constants;
import com.apec.framework.common.util.JsonUtils;


import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.apec.framework.base.BaseController;
import com.apec.framework.base.IJSONService;
import com.apec.framework.common.excel.CodeDealUtils;
import com.apec.framework.common.excel.XlsVO;
import com.apec.framework.common.exception.DispatchException;
import com.apec.framework.common.model.ResultData;
import com.apec.framework.common.util.FileUtils;

/**
 * 类 编 号：
 * 类 名 称：XLSController 
 * 内容摘要：PDF、excel 导出
 * 请求信息 
 * 创建日期：2017/2/6 
 * 编码作者：
 */
@RestController
public class XLSController extends BaseController
{

    @Autowired
    private IJSONService dispatchJSONService;

    @RequestMapping(value = "/{serverName}/{methodName}.xls")
    public void dispatchRequest(@PathVariable("serverName") String serverName,
        @PathVariable("methodName") String methodName, HttpServletRequest request, HttpServletResponse res)
        throws IOException
    {
        String ret = sendRequest(request, serverName, methodName, res);
        if(StringUtils.isEmpty(ret))
        {
            return;
        }
        ResultData resultData = JsonUtils.parseObject(ret, ResultData.class);
        if(!resultData.isSucceed() || null == resultData.getData())
        {
            return;
        }
        XlsVO object = JsonUtils.parseObject(resultData.getData().toString(), XlsVO.class);
        if(null == object)
        {
            return;
        }
        String url = object.getUrl();
        String fileName = CodeDealUtils.encodeFilename(object.getFileName(), request);
        FileUtils.downloadFile(res, fileName, url);
    }

    /**
    * 发送请求
    * @param request 请求
    * @param methodName 调用方法名
    * @param serverName 服务名称
    * @return 请求返回结果
    */
    private String sendRequest(HttpServletRequest request, String serverName, String methodName,
        HttpServletResponse response)
    {
        String ret;
        try
        {
            ret = dispatchJSONService.service(serverName, methodName, request, response);
        }
        catch (DispatchException e)
        {
            return super.getResultJSONStr(false, "", e.getErrorCode());
        }
        return ret;

    }
    
    
    /**
	 * 请求分发
	 * @param serverName 服务名称
	 * @param methodName 调用方法名
	 * @param fileName 文件名称
	 * @param request 请求信息
	 * @return 请求返回结果
	 */
	@RequestMapping(value = "/{serverName}/{fileName}/{methodName}.xls")
	public void dispatchRequest(@PathVariable("serverName") String serverName,
												  @PathVariable("fileName") String fileName, @PathVariable("methodName") String methodName,
												  HttpServletRequest request, HttpServletResponse res) throws IOException {

		String ret = sendRequest( request, serverName, fileName + Constants.SINGLE_SLASH + methodName, res);
		if(StringUtils.isEmpty(ret)) {
		    return;
// 			return   new ResponseEntity<>(null, new HttpHeaders(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
		ResultData resultData = JsonUtils.parseObject(ret, ResultData.class);
		if(!resultData.isSucceed() || null == resultData.getData()) {
		    return;
// 			return   new ResponseEntity<>(null, new HttpHeaders(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
		XlsVO object = JsonUtils.parseObject(resultData.getData().toString(), XlsVO.class);
		if(null == object) {
		    return;
// 			return   new ResponseEntity<>(null, new HttpHeaders(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
		String url = object.getUrl();
		String excelFileName = CodeDealUtils.encodeFilename(object.getFileName(), request);
        FileUtils.downloadFile(res, excelFileName, url);
// 		Path path = Paths.get(url);
// 		// 文件的存放路径
// 		byte[] contents  = Files.readAllBytes(path);

// 		HttpHeaders headers = new HttpHeaders();
// 		headers.setContentType(MediaType.parseMediaType("application/vnd.ms-excel"));
// 		headers.setContentDispositionFormData(excelFileName, excelFileName);
// 		headers.setCacheControl("must-revalidate, post-check=0, pre-check=0");

// 		return  new ResponseEntity<>(contents,headers, HttpStatus.OK);
	}

	@RequestMapping(value = "/down/pdf.pdf")
    public void ProcessRequest(HttpServletRequest request, HttpServletResponse res) throws IOException
    {
        String filePath = this.getClass().getClassLoader().getResource("Z-BaaS.pdf").getPath();
        File file=new File(filePath);
        if (file.exists())
        {
            res.setHeader("Content-Disposition", "attachment;filename=Z-BaaS.pdf" );
            res.setHeader("Content-Length", String.valueOf(file.length()));
            res.setHeader("Content-Transfer-Encoding", "binary");
            res.setHeader("Content-Type","Content-Type: application/json;charset=utf-8");
            // 将文件输入流写入response的输出流中
            StreamUtils.copy(new FileInputStream(filePath), res.getOutputStream());
        }
    }


}