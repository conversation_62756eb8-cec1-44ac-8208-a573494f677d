package com.apec.framework.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.apec.framework.base.BaseController;
import com.apec.framework.base.FileDownload;
import com.apec.framework.base.IJSONService;
import com.apec.framework.base.ResourceService;
import com.apec.framework.common.constant.ConfigConsts;
import com.apec.framework.common.constant.ErrorCodeConsts;
import com.apec.framework.common.constant.FrameConsts;
import com.apec.framework.common.dto.UploadCfgDTO;
import com.apec.framework.common.dto.UploadNFSDTO;
import com.apec.framework.common.dto.UploadNFSOneFileDTO;
import com.apec.framework.common.exception.ApecRuntimeException;
import com.apec.framework.common.exception.DispatchException;
import com.apec.framework.common.service.FileUploadService;
import com.apec.framework.common.service.NFSFileUploadService;
import com.apec.framework.common.util.HttpRequestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * @Auther: liliwei
 * @Date: 2018/7/8 10:58
 * @Description:magpie平台转发控制器
 */

@RestController
public class MagPieController extends BaseController
{
    @Autowired
    private IJSONService dispatchJSONService;

    @Autowired
    private FileUploadService fileUploadService;


    @Autowired
    private NFSFileUploadService nfsFileUploadService;

    @Autowired
    private ResourceService resourceService;

    @Value("${nfs.filemgt.servicename:BASE-FILEMGT}")
    private String filemgtServiceName;

    /**
     * 请求分发
     * @param serverName 服务名称
     * @param methodName 调用方法名
     * @param request 请求信息
     * @return 请求返回结果
     */
    @RequestMapping(value = "/{serverName}/{methodName}.magpie", produces = "application/json;charset=UTF-8")
    public String dispatchRequest(@PathVariable("serverName") String serverName,
        @PathVariable("methodName") String methodName, HttpServletRequest request, HttpServletResponse response)
    {
        return sendRequest(request, serverName, methodName, response);
    }

    /**
     * 请求分发
     * @param serverName 服务名称
     * @param methodName 调用方法名
     * @param fileName 文件名称
     * @param request 请求信息
     * @return 请求返回结果
     */
    @RequestMapping(value = "/{serverName}/{fileName}/{methodName}.magpie",method = {RequestMethod.GET,RequestMethod.POST,RequestMethod.OPTIONS},produces = "application/json;charset=UTF-8")
    public String dispatchRequest(@PathVariable("serverName") String serverName,
        @PathVariable("fileName") String fileName, @PathVariable("methodName") String methodName,
        HttpServletRequest request, HttpServletResponse response)
    {
        return sendRequest(request, serverName, fileName + ConfigConsts.SINGLE_SLASH + methodName, response);
    }
    /**
     * 请求分发
     * @param serverName 服务名称
     * @param moduleName 模块名
     * @param fileName 调用方法名
     * @param methodName 文件名称
     * @param request 请求信息
     * @return 请求返回结果
     */
    @RequestMapping(value = "/{serverName}/{moduleName}/{fileName}/{methodName}.magpie",method = {RequestMethod.GET,RequestMethod.POST,RequestMethod.OPTIONS},produces = "application/json;charset=UTF-8")
    public String dispatchRequest(@PathVariable("serverName") String serverName,@PathVariable("moduleName") String moduleName,
        @PathVariable("fileName") String fileName, @PathVariable("methodName") String methodName,
        HttpServletRequest request, HttpServletResponse response)
    {
        return sendRequest(request, serverName, moduleName + ConfigConsts.SINGLE_SLASH+fileName + ConfigConsts.SINGLE_SLASH + methodName, response);
    }

    /**
     * 发送请求
     * @param request 请求
     * @param methodName 调用方法名
     * @param serverName 服务名称
     * @return 请求返回结果
     */
    private String sendRequest(HttpServletRequest request, String serverName, String methodName,
        HttpServletResponse response)
    {
        try
        {
            return dispatchJSONService.service(serverName, methodName, request, response);
        }
        catch (DispatchException e)
        {
            return super.getResultJSONStr(false, "", e.getErrorCode());
        }
    }

    /**
     * 二级目录
     * 上传文件到FTP
     * @param serverName 服务名称
     * @param methodName 方法名称
     * @param request 请求信息
     * @return 上传结果
     */
    @RequestMapping(value = "/{serverName}/{methodName}/uploadFile.magpie", produces = "application/json;charset=UTF-8")
    public String uploadFile(@PathVariable("serverName") String serverName,
        @PathVariable("methodName") String methodName, HttpServletRequest request, HttpServletResponse response)
    {
        Map<String, MultipartFile> multipartFileMap = ((MultipartHttpServletRequest)request).getFileMap();

        try
        {
            fileUploadService.uploadFileByFTP(multipartFileMap, request);
        }
        catch (ApecRuntimeException e)
        {
            return super.getResultJSONStr(false, "", e.getErrorCode());
        }

        String ret;
        try
        {
            ret = dispatchJSONService.service(serverName, methodName, request, response);
        }
        catch (DispatchException e)
        {
            return super.getResultJSONStr(false, "", e.getErrorCode());
        }
        return ret;
    }

    /**
     * 三级目录
     * 上传文件到FTP
     * @param serverName 服务名称
     * @param fileName 文件名称
     * @param methodName 方法名称
     * @param request 请求信息
     * @return 上传结果
     */
    @RequestMapping(value = "/{serverName}/{fileName}/{methodName}/uploadFile.magpie", produces = "application/json;charset=UTF-8")
    public String uploadFile(@PathVariable("serverName") String serverName,
        @PathVariable("fileName") String fileName,
        @PathVariable("methodName") String methodName, HttpServletRequest request, HttpServletResponse response)
    {
        Map<String, MultipartFile> multipartFileMap = ((MultipartHttpServletRequest)request).getFileMap();

        try
        {
            fileUploadService.uploadFileByFTP(multipartFileMap, request);
        }
        catch (ApecRuntimeException e)
        {
            e.printStackTrace();
            return super.getResultJSONStr(false, "", e.getErrorCode());
        }

        String ret;
        try
        {
            ret = dispatchJSONService.service(serverName, fileName + ConfigConsts.SINGLE_SLASH + methodName, request, response);
        }
        catch (DispatchException e)
        {
            return super.getResultJSONStr(false, "", e.getErrorCode());
        }
        return ret;
    }

    /**
     * 二级目录
     * downloadFile文件下载
     * @param serverName 服务名称
     * @param methodName 方法名称
     * @param request 请求信息
     * @return
     */
    @RequestMapping(value = "/{serverName}/{methodName}/downloadFile.magpie", produces = "application/json;charset=UTF-8")
    public void downloadFile(@PathVariable("serverName") String serverName,
        @PathVariable("methodName") String methodName, HttpServletRequest request, HttpServletResponse response)
    {
        response.reset();
        try {
            FileDownload fd = resourceService.service(serverName, methodName, request, response);
            if(fd != null){
                if(!CollectionUtils.isEmpty(fd.getContentDispositions())){
                    for(String head : fd.getContentDispositions()){
                        response.addHeader("Content-disposition",head);
                    }
                }
                if(!CollectionUtils.isEmpty(fd.getContentTypes())){
                    for(String head : fd.getContentTypes()){
                        response.addHeader("Content-Type",head);
                    }
                }
                if(fd.getBytes() == null){
                    fd.setBytes(new byte[]{});
                }
                response.getOutputStream().write(fd.getBytes());
            }
        } catch (IOException e) {

        }
    }


    /**
     * 三级目录
     * downloadFile文件下载
     * @param serverName 服务名称
     * @param methodName 方法名称
     * @param request 请求信息
     * @return
     */
    @RequestMapping(value = "/{serverName}/{fileName}/{methodName}/downloadFile.magpie", produces = "application/json;charset=UTF-8")
    public void downloadFile(@PathVariable("serverName") String serverName,@PathVariable("fileName") String fileName,
                             @PathVariable("methodName") String methodName, HttpServletRequest request, HttpServletResponse response)
    {
        downloadFile(serverName,fileName + ConfigConsts.SINGLE_SLASH + methodName,request,response);
    }

    /**
     * NFS 共享文件(异步微服务)
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/uploadAsync/{uploadCode}.magpie", produces = "application/json;charset=UTF-8")
    public String uploadAsync(@PathVariable("uploadCode") String uploadCode,HttpServletRequest request, HttpServletResponse response)
    {
        try
        {
            UploadNFSDTO data = nfsFileUploadService.uploadFileNFS(request,uploadCode);
            return super.getResultJSONStr(true,data.getFileRsList(),null);
        }
        catch (ApecRuntimeException e)
        {
            return super.getResultJSONStr(false, "", e.getErrorCode());
        }
    }

    /**
     * NFS 共享文件(同步微服务)
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/uploadSync/{uploadCode}.magpie", produces = "application/json;charset=UTF-8")
    public String uploadSync(@PathVariable("uploadCode") String uploadCode,HttpServletRequest request, HttpServletResponse response)
    {
        UploadNFSDTO data = null;
        try
        {
            data = nfsFileUploadService.uploadFileNFS(request,uploadCode);
            if(null == data || !data.isFlag()){
                return super.getResultJSONStr(false, data, ErrorCodeConsts.ERROR_FTP_UPLOAD_FAILD);
            }
        }
        catch (ApecRuntimeException e)
        {
            return super.getResultJSONStr(false, "", e.getErrorCode());
        }

        String ret;
        try
        {
//            Map<String,String[]> paramMap = request.getParameterMap();
//            UploadCfgDTO cfg = nfsFileUploadService.getUploadServiceCfg(uploadCode);
            request.setAttribute("uploadData",data);
            ret = dispatchJSONService.service(filemgtServiceName.toUpperCase(), "/mgtfile/uploadFile", request, response);
        }
        catch (DispatchException e)
        {
            return super.getResultJSONStr(false, "", e.getErrorCode());
        }
        return ret;
    }

    /**
     * 检查业务执行
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/checkServiceExec/{uploadCode}.magpie", produces = "application/json;charset=UTF-8")
    public String checkServiceExec(@PathVariable("uploadCode") String uploadCode,HttpServletRequest request, HttpServletResponse response)
    {
        String ret;
        try
        {
            ret = dispatchJSONService.service(filemgtServiceName.toUpperCase(), "/mgtfile/checkUpload", request, response);
        }
        catch (DispatchException e)
        {
            return super.getResultJSONStr(false, "", e.getErrorCode());
        }
        return ret;
    }

}
