package com.apec.framework.controller;

import com.apec.framework.base.BaseController;
import com.apec.framework.base.IJSONService;
import com.apec.framework.common.constant.ConfigConsts;
import com.apec.framework.common.exception.DispatchException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Auther: liliwei
 * @Date: 2018/11/8 18:41
 * @Description:
 */

@RestController
public class DubboController extends BaseController
{

    @Value("${DUBBO_GATEWAY_URL_BEGIN}")
    private String dubboBeginUrl;

    @Value("${DUBBO_GATEWAY_URL_END}")
    private String dubboEndUrl;

    @Autowired
    private IJSONService dispatchJSONService;

    @RequestMapping(value = "/{fileName}/{methodName}.dubbo", produces = "application/json;charset=UTF-8")
    public String dispatchRequest(@PathVariable("fileName") String fileName, @PathVariable("methodName") String methodName,
                                  HttpServletRequest request, HttpServletResponse response) {
        return sendRequest(request, dubboBeginUrl,
                fileName
                        + ConfigConsts.SINGLE_SLASH + methodName
                        + dubboEndUrl, response);
    }

    @RequestMapping(value = "/{serverName}/{fileName}/{methodName}.dubbo", produces = "application/json;charset=UTF-8")
    public String dispatchRequest(@PathVariable("serverName") String serverName,
        @PathVariable("fileName") String fileName, @PathVariable("methodName") String methodName,
        HttpServletRequest request, HttpServletResponse response) {
        return sendRequest(request, dubboBeginUrl,
                serverName
                        + ConfigConsts.SINGLE_SLASH + fileName
                        + ConfigConsts.SINGLE_SLASH + methodName
                        + dubboEndUrl, response);
    }

    @RequestMapping(value = "/{serverName}/{modelName}/{fileName}/{methodName}.dubbo", produces = "application/json;charset=UTF-8")
    public String dispatchRequest(@PathVariable("serverName") String serverName,
        @PathVariable("modelName") String modelName,
        @PathVariable("fileName") String fileName, @PathVariable("methodName") String methodName,
        HttpServletRequest request, HttpServletResponse response) {
        return sendRequest(request, dubboBeginUrl,
                serverName
                        + ConfigConsts.SINGLE_SLASH + modelName
                        + ConfigConsts.SINGLE_SLASH + fileName
                        + ConfigConsts.SINGLE_SLASH + methodName
                        + dubboEndUrl, response);
    }

    private String sendRequest(HttpServletRequest request, String serverName, String methodName,
        HttpServletResponse response) {
        try {
            return dispatchJSONService.service(serverName, methodName, request, response);
        } catch (DispatchException e) {
            return super.getResultJSONStr(false, "", e.getErrorCode());
        }
    }
}
