package com.apec.framework.common.util;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

import javax.servlet.http.Cookie;

/**   
 * @ClassName:  CookieUtils   
 * @Description: 生成默认cookie
 * @author: goofly
 * @date:   2018年9月28日 上午9:05:26   
 *      
 */
public class CookieUtils {
	
	public static final String UTF8 = "UTF-8";
	public static final int default_exp_time = 3600*24;

	public static Cookie generateDefaultCookie(String name,String value) throws UnsupportedEncodingException {
		Cookie cookie = new Cookie("Authorization",URLEncoder.encode(value,UTF8));
		cookie.setPath("/");
		cookie.setMaxAge(default_exp_time);
		return cookie;
	}
	
	public static Cookie generateDefaultCookie(String name,StringBuffer value,String domain,Integer days) throws UnsupportedEncodingException {
		Cookie cookie = new Cookie("Authorization",URLEncoder.encode(value.toString(),UTF8));
		cookie.setPath("/");
		cookie.setDomain(domain);
		if(days != null) {
			cookie.setMaxAge(default_exp_time*days.intValue());
		}
		return cookie;
	}
}
