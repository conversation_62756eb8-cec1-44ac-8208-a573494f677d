package com.apec.framework.common.filter.checkpressiom;

import com.apec.framework.common.exception.ApecRuntimeException;
import com.apec.framework.common.model.ResultData;
import com.apec.framework.common.util.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * @Auther: liliwei
 * @Date: 2018/5/15 14:22
 * @Description:
 */
public abstract class AbsCkPressiom implements ICKPressiom
{

    public static final Logger log = LoggerFactory.getLogger(AbsCkPressiom.class);
    /**
     * 返回session超时信息
     * @param response 响应头
     */
    public void handlerError(HttpServletResponse response,ResultData<Object> resultData)
    {
        try
        {
            response.setCharacterEncoding("UTF-8");
            PrintWriter out = response.getWriter();
            response.setHeader("Content-Type ", "application/json;charset=UTF-8");
            out.print(JsonUtils.toJSONString(resultData));
            out.close();
        }
        catch (IOException e)
        {
            log.error("//// AbsFilter.handlerError exception case:" + e.getMessage());
            throw new ApecRuntimeException("AbsFilter.handlerError exception case:", e);
        }
    }
}
