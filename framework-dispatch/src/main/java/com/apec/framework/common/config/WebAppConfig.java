package com.apec.framework.common.config;

import com.apec.framework.common.filter.SessionTimeOutFilter;
import com.apec.framework.nosession.NoSessionFilter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.boot.web.servlet.MultipartConfigFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

import javax.servlet.MultipartConfigElement;

/**
 * 类 编 号：
 * 类 名 称：WebAppConfig
 * 内容摘要：
 * 完成日期：
 * 编码作者：
 */
@Configuration
@EnableWebMvc
public class WebAppConfig extends WebMvcConfigurerAdapter
{
    @Value("${notAllowUrls}")
    private String[] notAllowUrls;

    @Bean
    public SessionTimeOutFilter sessionTimeOutFilter()
    {
        return new SessionTimeOutFilter();
    }

   /* @Bean
    public NoSessionFilter noSessionFilter()
    {
        return new NoSessionFilter();
    }*/

    @Bean
    public FilterRegistrationBean filterRegistration()
    {

        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setFilter(sessionTimeOutFilter());
        registration.addUrlPatterns(notAllowUrls);
        registration.setOrder(1);
       /* registration.setFilter(noSessionFilter());
        registration.setOrder(-1);*/
        return registration;
    }

    @Bean
    public MultipartConfigElement multipartConfigElement() {
        MultipartConfigFactory factory = new MultipartConfigFactory();
        //// 设置文件大小限制 ,超了，页面会抛出异常信息，这时候就需要进行异常信息的处理了;
        factory.setMaxFileSize("10MB"); //KB,MB
        /// 设置总上传数据总大小
        factory.setMaxRequestSize("50MB");
        return factory.createMultipartConfig();
    }
}
