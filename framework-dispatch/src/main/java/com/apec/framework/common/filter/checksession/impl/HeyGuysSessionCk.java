package com.apec.framework.common.filter.checksession.impl;

import com.apec.cache.base.CacheService;
import com.apec.framework.common.constant.CacheConsts;
import com.apec.framework.common.constant.ErrorCodeConsts;
import com.apec.framework.common.constant.FrameConsts;
import com.apec.framework.common.exception.ApecRuntimeException;
import com.apec.framework.common.exception.DispatchException;
import com.apec.framework.common.filter.checksession.AbsCkSesssion;
import com.apec.framework.common.model.ResultData;
import com.apec.framework.common.tools.Sha1;
import com.apec.framework.common.util.JsonUtils;
import com.apec.framework.common.util.SpringUtils;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.security.DigestException;
import java.util.Base64;
import java.util.Map;

/**
 * @Auther: liliwei
 * @Date: 2018/5/12 14:30
 * @Description:
 */
@Component("heyGuysSessionCk")
public class HeyGuysSessionCk extends AbsCkSesssion
{

    @Value("${allowUrls}")
    private String allowUrls;

    @Value("${repeatSubmitUrl}")
    private String repeatSubmitUrl;

    @Value("${allowPrefix}")
    private String allowPrefix;

    @Resource(name = "redisService")
    private CacheService cacheService;

    @Override
    public boolean doSessionFilter(HttpServletRequest servletRequest, HttpServletResponse servletResponse)
    throws IOException,ServletException
    {
        HttpSession session = servletRequest.getSession();
        String type = servletRequest.getHeader(FrameConsts.CLIENT_TYPE_PARAM);
        String url = servletRequest.getRequestURI();
        String sessionId = session.getId();
        boolean isPass = true; // 如果session超时则不调用服务，默认为通过
        boolean isRepeat = true; // 是否为重复提交

        if(Strings.isNullOrEmpty(type))
        {
            type = FrameConsts.WEIXIN;
        }
        String roleType = getRoleType(servletRequest);

        String urlLower = allowUrls.toLowerCase();
        log.debug("//// URL白名单：{}", urlLower);
        boolean flag = false;
        if(StringUtils.isNotBlank(allowPrefix))
        {
            String[] prefix = allowPrefix.toLowerCase().split(",");
            for(String string : prefix)
            {
                if(url.toLowerCase().contains(string))
                {
                    flag = true;
                    break;
                }
            }
        }
        if(urlLower.contains(url.toLowerCase()) || url.toLowerCase().contains(allowPrefix.toLowerCase()) || flag)
        {
            session.setAttribute(FrameConsts.SESSION_ID, sessionId);
            initServiceUserInfo(servletRequest, servletResponse, roleType, type, sessionId);
        }
        else
        {
            if(FrameConsts.ANDROID.equals(type) || FrameConsts.IOS.equals(type) || FrameConsts.WEIXIN.equals(type))
            {
                servletRequest.setAttribute(FrameConsts.CLIENT_TYPE_PARAM, type);
                // 版本号：手机相关信息
                String ua = servletRequest.getHeader(FrameConsts.UA);
                // 设备号
                String imei = servletRequest.getHeader(FrameConsts.IMEI);

                servletRequest.setAttribute(FrameConsts.UA, ua);
                servletRequest.setAttribute(FrameConsts.IMEI, imei);

                try
                {
                    isPass = appLogTimeOut(servletRequest, servletResponse);
                }
                catch (DigestException e)
                {
                    log.error("///////DigestException{}", e);

                }
                isRepeat = appRepeatAction(servletRequest, servletResponse);
            }
            else
            {
                servletRequest.setAttribute(FrameConsts.CLIENT_TYPE_PARAM, FrameConsts.WEB);
                isPass = webLogTimeOut(servletResponse, sessionId, servletRequest);
                isRepeat = webRepeatAction(servletResponse, sessionId, servletRequest);
            }
        }
        return isPass && isRepeat;
    }

    /**
     *  初始化服务UserInfo信息，可以在配置白名单的后台服务里面获取用户数据。
     * @param request 请求
     * @param response 响应
     * @param roleType 用户类型
     * @param type 请求终端
     * @param sessionId sessionId
     * <AUTHOR>
     * @date 2017-12-13
     * @desc 微信商城详情页，活动在登录与未登录状态下获取信息。
     */
    private void initServiceUserInfo(HttpServletRequest request, HttpServletResponse response, String roleType,
        String type, String sessionId)
    {
        try
        {
            if(FrameConsts.ROLETYPE_CS.equals(roleType))
            {
                String token = request.getHeader(FrameConsts.TOKEN);
                log.info("//// sessionTimeOutFilter.initServiceUserInfo,token:{}", token);
                token = deToken(token);
                log.debug("//// 请求URL:{} || DEBUG_LOG ==> 解密后 token:{}", request.getRequestURI(), token);
                String tokenKey = FrameConsts.PREFIX_TOKEN + token;
                String userId = cacheService.get(tokenKey);
                // 将token设置到请求属性里面
                request.setAttribute(FrameConsts.TOKEN, token);
                log.info("//// sessionTimeOutFilter.initServiceUserInfo , userNo={}", userId);
                String userInfoJson = cacheService.get(CacheConsts.CACHE_USERINFO_PREFIX + userId);
                request.setAttribute(FrameConsts.USER_NO, userId);
                request.setAttribute(FrameConsts.USER_INFO, userInfoJson);
            }
            //            else {
            //                String sessionKey = FrameConsts.PREFIX_SESSIONID + sessionId;
            //                String userNo = cacheService.get(sessionKey);
            //                log.info("//// sessionTimeOutFilter.initServiceUserInfo userNo:" + userNo);
            //                String userInfoJson = cacheService.get(CacheConsts.CACHE_USERINFO_PREFIX + userNo);
            //                request.setAttribute(FrameConsts.USER_NO, userNo);
            //                request.setAttribute(FrameConsts.USER_INFO, userInfoJson);
            //                cacheService.expire(sessionKey, 120);
            //            }
        }
        catch (Exception e)
        {
            log.error("//// sessionTimeOutFilter.handleSessionInfo exception case:" + e.getMessage());
        }
    }


    /**
     * 处理app端session超时处理
     * @param request 请求request
     * @param response 响应数据
     */
    private boolean appLogTimeOut(HttpServletRequest request, HttpServletResponse response)
        throws UnsupportedEncodingException, DigestException
    {
        String token = request.getHeader(FrameConsts.TOKEN);
        log.info("//// sessionTimeOutFilter.webLogTimeOut appLogTimeOut,token:{}", token);
        if(StringUtils.isBlank(token))
        {
            handlerError(response,
                         new ResultData<Object>(false,ErrorCodeConsts.ERROR_600001,SpringUtils.getMessage(ErrorCodeConsts.ERROR_600001)));
            return false;
        }
        token = deToken(token);
        log.debug("//// 请求URL:{} || DEBUG_LOG ==> 解密后 token:{}", request.getRequestURI(), token);
        String tokenKey = FrameConsts.PREFIX_TOKEN + token;
        String userId = cacheService.get(tokenKey);
        // 将token设置到请求属性里面
        request.setAttribute(FrameConsts.TOKEN, token);
        log.info("//// sessionTimeOutFilter.webLogTimeOut appLogTimeOut , userNo={}", userId);
        return handleSessionInfo(response, userId, request);
    }

    /**
     * app 端重复提交处理
     * @param req 请求request
     * @param res 响应数据
     */
    private boolean appRepeatAction(HttpServletRequest req, HttpServletResponse res)
    {
        String token = req.getHeader(FrameConsts.TOKEN);
        String duplicateKey = FrameConsts.PREFIX_REPEAT + token;
        return isRepeatSubmit(duplicateKey, req, res);
    }

    /**
     * 处理H5端session超时
     * @param response 响应数据
     * @param sessionId 当前sessionId
     * @param request  当前请求信息
     */
    private boolean webLogTimeOut(HttpServletResponse response, String sessionId, HttpServletRequest request)
    {

        log.info("//// sessionTimeOutFilter.webLogTimeOut sessionId:" + sessionId);
        String sessionKey = FrameConsts.PREFIX_SESSIONID + sessionId;
        String userNo = cacheService.get(sessionKey);
        log.info("//// sessionTimeOutFilter.webLogTimeOut userNo:" + userNo);
        if(handleSessionInfo(response, userNo, request))
        {
            cacheService.expireSecond(sessionKey, 120 * 60);
            return true;
        }
        return false;
    }

    /**
     * web端重复提交处理
     * @param res 响应数据
     * @param sessionId 当前sessionId
     * @param req 当前请求信息
     */
    private boolean webRepeatAction(HttpServletResponse res, String sessionId, HttpServletRequest req)
    {
        String sessionKey = FrameConsts.PREFIX_SESSIONID + sessionId;
        log.debug("//// check_repeat_request:sessionKey is {}", sessionKey);
        String userNo = cacheService.get(sessionKey);
        String duplicateKey = FrameConsts.PREFIX_REPEAT + userNo;
        log.debug("//// check_repeat_request:duplicateKey is {}", duplicateKey);
        return isRepeatSubmit(duplicateKey, req, res);
    }

    /**
     * 设置session超时信息
     * @param response 响应信息
     * @param userId 用户编号
     * @param request request 当前请求信息
     */
    private boolean handleSessionInfo(HttpServletResponse response, String userId, HttpServletRequest request)
    {
        log.info("//// sessionTimeOutFilter.handleSessionInfo，url：{}，userNo：{}", request.getRequestURI(), userId);

        if(StringUtils.isBlank(userId))
        {
            handlerError(response,
                         new ResultData<Object>(false,ErrorCodeConsts.ERROR_600001,SpringUtils.getMessage(ErrorCodeConsts.ERROR_600001)));
            return false;
        }
        else
        {
            String userInfoJson = cacheService.get(CacheConsts.CACHE_USERINFO_PREFIX + userId);
            request.setAttribute(FrameConsts.USER_NO, userId);
            request.setAttribute(FrameConsts.USER_INFO, userInfoJson);
            return true;
        }
    }

    /**
     * 重复提交
     * @param repeatKey 重复提交建
     * @param request 请求信息
     * @return true/false
     */
    private boolean isRepeatSubmit(String repeatKey, HttpServletRequest request, HttpServletResponse response)
    {
        String url = request.getRequestURI();
        if(repeatSubmitUrl.contains(url))
        {
            String clientRepeat = request.getParameter(FrameConsts.CLIENT_DUPLICATE_ACT_PARAM);
            log.debug("//// check_repeat_request:_d value is {}", clientRepeat);
            String serverRepeat = cacheService.get(repeatKey);
            log.debug("//// check_repeat_request:server store repeat value is {}", serverRepeat);
            if(StringUtils.isBlank(clientRepeat))
            {
                return true;
            }
            if(StringUtils.isBlank(serverRepeat))
            {
                cacheService.add(repeatKey, clientRepeat);
                return true;
            }
            if(serverRepeat.equals(clientRepeat))
            {
                try
                {
                    response.setCharacterEncoding("UTF-8");
                    ResultData<Object> resultData = new ResultData<>();
                    PrintWriter out = response.getWriter();
                    resultData.setErrorCode(ErrorCodeConsts.ERROR_600004);
                    resultData.setErrorMsg(SpringUtils.getMessage(ErrorCodeConsts.ERROR_600004));
                    resultData.setSucceed(false);
                    resultData.setRepeatAct(serverRepeat);
                    out.print(JsonUtils.toJSONString(resultData));
                    out.close();
                }
                catch (IOException e)
                {
                    log.error("//// sessionTimeOutFilter.isRepeatSubmit case:" + e.getMessage());
                    throw new ApecRuntimeException("sessionTimeOutFilter.isRepeatSubmit case:", e);
                }
                return false;
            }
            cacheService.add(repeatKey, clientRepeat);
        }
        return true;

    }

    /**
     * 反解密spring session token
     * @param token  token
     * token有两端组成，前后端用"."（点号）分割
     * 前面一段暂定名为head，可解，由原始x-auth-token + unix时间戳组成，两部分也使用"."（点号）分割，客户端使用Base64加密。
     * 0d618af4-806c-48e6-94ed-0d6f0fadec3b.1493710175
     * 后面一段暂定名为body，使用x-auth-token=value + unix时间戳 + 密钥 经过SHA1加密，服务端用同样方式进行加密后校验
     * 格式：token=0d618af4-806c-48e6-94ed-0d6f0fadec3b&time=1493710175&secret=APEC2017
     *
     * <a name="参考">https://tower.im/projects/fbe414e4e0ce42d49deb1a33cf899d14/docs/cf395c52e8684a6e85c33fb350874ab7/</a>
     * @return String
     */
    private String deToken(String token) throws UnsupportedEncodingException, DigestException
    {
        String X_AUTH_TOKEN_SECRET = "APEC2017";
        String[] array = token.split("\\.");
        if(array.length != 2)
        {
            throw new DispatchException(ErrorCodeConsts.ERROR_X_AUTH_TOKEN, "x-auth-token格式错误");
        }

        // 处理head部分
        String deHead = array[0];
        byte[] asBytes = Base64.getDecoder().decode(deHead);
        String head = new String(asBytes, "utf-8");
        String[] headArray = head.split("\\.");
        if(headArray.length != 2)
        {
            throw new DispatchException(ErrorCodeConsts.ERROR_X_AUTH_TOKEN, "x-auth-token格式错误");
        }
        String xAuthToken = headArray[0];
        String unixTime = headArray[1];

        // 校验body
        Map<String, Object> map = Maps.newHashMap();
        map.put("token", xAuthToken);
        map.put("time", unixTime);
        map.put("secret", X_AUTH_TOKEN_SECRET);
        String mapSha1 = Sha1.SHA1(map);

        String deBody = array[1];

        if(!deBody.equals(mapSha1))
        {
            throw new DispatchException(ErrorCodeConsts.ERROR_X_AUTH_TOKEN, "x-auth-token错误");
        }
        return xAuthToken;
    }

    private String getRoleType(HttpServletRequest request)
    {
        String roleType = request.getHeader(FrameConsts.REQUEST_HEADER_PARAM_ROLETYPE);
        if(Strings.isNullOrEmpty(roleType))
        {
            log.error("# 请求{} 头不包含{}参数，设置默认值为{}", request.getRequestURI(), FrameConsts.REQUEST_HEADER_PARAM_ROLETYPE,
                      FrameConsts.ROLETYPE_CS);
            roleType = FrameConsts.ROLETYPE_CS;
        }
        return roleType;
    }
}
