package com.apec.framework.common.filter.checksession.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.apec.cache.base.CacheService;
import com.apec.framework.common.config.HsPessiomConfig;
import com.apec.framework.common.constant.ErrorCodeConsts;
import com.apec.framework.common.filter.checksession.AbsCkSesssion;
import com.apec.framework.common.model.ResultData;
import com.apec.framework.common.util.JsonUtils;
import com.apec.framework.common.util.SpringUtils;
import com.hundsun.exchange.uc.client.dto.UserAgent;
import org.apache.commons.lang.StringUtils;
import org.eclipse.jetty.http.HttpFields;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.nio.ByteBuffer;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @Auther: liliwei
 * @Date: 2018/5/10 19:09
 * @Description:hs app 端session超时校验
 */
@Component("hsAppSessionTimeOut")
public class HsAPPSessionCk extends AbsCkSesssion implements InitializingBean
{
    private static final int laytime = 120*60;

    private static final int notlaytime = 60*60;

    private static final int outtime = 3;

    private static final int sessionDefTimeOut=1800;

    private Map<String,Long> sessionIdMap = new HashMap<String,Long>();

    private TimeUnit timeUnit = TimeUnit.SECONDS;

    @Autowired
    private HsPessiomConfig hsPessiomConfig;


    public boolean doSessionFilter(HttpServletRequest servletRequest, HttpServletResponse response)
    {
        boolean flag=true;
        try
        {
            ByteBuffer buffer = scanBufferFromBaseRequest(servletRequest);
            String path = servletRequest.getServletPath();
            JSONObject result = doGetParameters(servletRequest, buffer == null ? null : buffer);
            String functionId=hsPessiomConfig.getFunctionTypeMap().get(path);
            String checkType=hsPessiomConfig.getSessionTypeMap().get(path);
            log.info("//// HsAPPSessionTimeOut.doSessionFilte,requestMap:{}", result.toJSONString());
            String cacheKey=null;
            if(!"noSession".equals(checkType)){
                cacheKey=result.get(checkType).toString();
                if("sessionId".equals(checkType)) flag=checkSession(result,cacheKey,functionId,response);
                if("token_id".equals(checkType)) flag=checkToken(result,cacheKey,servletRequest,response);
            }
            log.info("//// HsAPPSessionTimeOut.doSessionFilte,cacheKey:{}", cacheKey);
            //servletRequest.setAttribute("functionId",functionId);
            servletRequest.setAttribute("app_request",result);
        }catch (Exception e){
            log.error("HsAPPSessionTimeOut.doSessionFilte.DigestException:{}",e);
        }
        return flag;
    }

    public boolean checkSession(JSONObject result,String cacheKey,String functionId,HttpServletResponse response){
        /*if("305773".equals(functionId)){
            cacheKey=result.get("session_id").toString();
        }
        if(StringUtils.isNotBlank(cacheKey)){
            if(null==cacheService.get(cacheKey)) {
                handlerError(response,
                             new ResultData<Object>(false,ErrorCodeConsts.HS_SESSION_ABATE,SpringUtils.getMessage(ErrorCodeConsts.ERROR_600001)));
                return false;
            }
        }else {
            handlerError(response,
                         new ResultData<Object>(false,ErrorCodeConsts.HS_SESSION_ABATE,SpringUtils.getMessage(ErrorCodeConsts.ERROR_600001)));
            return false;
        }*/
        return checkAccount(result,cacheKey,functionId,response);
    }

    /**
     * 将请求体读取至buffer(针对POST|PUT类请求)
     *
     * @param baseRequest
     * @return
     * @throws IOException
     * @throws ServletException
     */
    private ByteBuffer scanBufferFromBaseRequest(HttpServletRequest baseRequest) throws IOException, ServletException
    {
        ByteBuffer buffer = null;
        if("POST".equals(baseRequest.getMethod()) || "PUT".equals(baseRequest.getMethod()))
        {
            int contentLength = baseRequest.getContentLength();
            if(contentLength != 0)
            {
                // 返回请求体的一个二进制形式的ServletInputStream
                InputStream in = baseRequest.getInputStream();
                // 分配内存
                buffer = ByteBuffer.allocate(contentLength);
                int length = 0;
                // 将请求体数据读入buffer
                do
                {
                    length = in.read(buffer.array());
                }
                while (length < 0);
            }
        }
        return buffer;
    }

    public JSONObject doGetParameters(HttpServletRequest request, ByteBuffer buffer) throws Exception
    {
        JSONObject jsonObj = new JSONObject();
        if(buffer != null)
        {
            String decodeData = "";
            try
            {
                decodeData = new String(buffer.array(), "UTF-8");
            }
            catch (UnsupportedEncodingException e)
            {
                log.warn(e.getMessage());
            }
            if(decodeData.length() > 0)
            {
                String content_type = HttpFields
                    .valueParameters(request.getContentType(), new HashMap<String, String>());
                // 处理json内容
                if(content_type.contains("json"))
                {
                    jsonObj = JSONObject.parseObject(decodeData);
                }
                else
                {
                    String[] splitList = decodeData.split("&");
                    /**
                     * json格式为{admin:123,pwd:333}或{ids:[1,2,3],userIds:[3,5,6]}
                     */
                    for(String item : splitList)
                    {
                        String[] obj = item.split("=");
                        if(obj[0].contains("[]"))
                        {// 格式为：{ids:[1,2,3]},
                            // 注！数组方式的多个值传到T2是以逗号隔开的
                            StringBuffer tmpVal = new StringBuffer();
                            String key = obj[0].replace("[]", "");
                            String val = (String)jsonObj.get(key);
                            if(StringUtils.isNotBlank(val))
                            {
                                tmpVal.append(val).append(",").append(obj[1]);
                            }
                            else
                            {
                                tmpVal.append(obj[1]);
                            }
                            jsonObj.put(key, tmpVal.toString());
                        }
                        else
                        {
                            jsonObj.put(obj[0], obj[1]);
                        }
                    }
                }
            }
        }
        else
        {
            //get请求体中的参数
            Map<String, String[]> requestMap = request.getParameterMap();
            for(Map.Entry<String, String[]> key : requestMap.entrySet())
            {
                if(key.getValue().length == 1)
                {
                    jsonObj.put(key.getKey(), (key.getValue()[0]));
                }
            }
        }
        return jsonObj;
    }

    public boolean checkAccount(JSONObject requestMap,String  session_id,String functionId,HttpServletResponse servletResponse){
        /*try {
            UserAgent user = (UserAgent)cacheService.get(session_id);
            JSONObject jsonObjResult = JSONObject.parseObject(JsonUtils.toJSONString(user, SerializerFeature.WriteMapNullValue));
           *//* request.setAttribute("cacheObj", jsonObjResult);*//*
            if(session_id.equals(jsonObjResult.getJSONArray("data").getJSONObject(0).get("session_id"))){
                touchCache(session_id);
                //检测账号
                if(StringUtils.isNotBlank(hsPessiomConfig.getCheckAccount())&&hsPessiomConfig.getCheckAccount().indexOf(functionId)>=0){
                    String  user_id = jsonObjResult.getJSONArray("data").getJSONObject(0).get("user_id").toString();
                    //String user_id_next = user_id;
                    if(user_id.length()>10){
                        user_id = user_id.substring(0, 10);
                    }
                    if(requestMap.containsKey("settlementAccount")){
                        String settlementAccount = requestMap.get("settlementAccount").toString();
                        if(StringUtils.isNotBlank(settlementAccount)&&settlementAccount.indexOf(user_id)==-1){
                            handlerError(servletResponse,
                                         new ResultData<Object>(false,ErrorCodeConsts.HS_NOT_PEREONAL_SETTLEMENT_ACCOUNT,SpringUtils.getMessage(ErrorCodeConsts.HS_NOT_PEREONAL_SETTLEMENT_ACCOUNT)));
                            return false;
                        }
                    }
                    if(requestMap.containsKey("fundAccount")){
                        String fundAccount = requestMap.get("fundAccount").toString();
                        if(StringUtils.isNotBlank(fundAccount)&&fundAccount.indexOf(user_id)==-1){
                            handlerError(servletResponse,
                                         new ResultData<Object>(false,ErrorCodeConsts.HS_NOT_PEREONAL_FUND_ACCOUNT,SpringUtils.getMessage(ErrorCodeConsts.HS_NOT_PEREONAL_FUND_ACCOUNT)));
                            return false;
                        }
                    }
                    if(requestMap.containsKey("fundAccountOut")){
                        String fundAccountOut = requestMap.get("fundAccountOut").toString();
                        if(StringUtils.isNotBlank(fundAccountOut)&&fundAccountOut.indexOf(user_id)==-1){
                            handlerError(servletResponse,
                                         new ResultData<Object>(false,ErrorCodeConsts.HS_NOT_PEREONAL_FUND_ACCOUNT,SpringUtils.getMessage(ErrorCodeConsts.HS_NOT_PEREONAL_FUND_ACCOUNT)));
                            return false;
                        }
                    }
                }
                //赋值userId
                if(hsPessiomConfig.getUserIdOpen()){
                    String param = hsPessiomConfig.getUserIdValue(functionId);
                    String jsonstr =  JSON.toJSONString(requestMap);
                    if(log.isDebugEnabled()){
                        log.debug(functionId + ":BeforeReplacement:[" + jsonstr +"]");
                    }
                    int i = jsonstr.indexOf("\"user_id\"");
                    if(jsonstr.length()>i+11&&i>0){
                        int start = jsonstr.indexOf("\"", i+10);
                        int end = jsonstr.indexOf("\"", i+11);
                        String str = jsonstr.substring(start+1, end);
                        String user_id = "";
                        if(StringUtils.isNotBlank(str)&&str.length()<20){
                            user_id = jsonObjResult.getJSONArray("data").getJSONObject(0).get("user_id").toString();
                            //判断是否是特殊的情况
                            String userIdSpecial = hsPessiomConfig.getUserIdSpecial();
                            if(StringUtils.isNotBlank(userIdSpecial)&&(userIdSpecial.indexOf(functionId)>=0)){
                                if(user_id.length()>10){
                                    user_id = user_id.substring(0,10);
                                }
                            }
                        }
                        //覆盖
                        param = "\"" + param + "\"";
                        String jsonstrNext = jsonstr.substring(i-1);
                        jsonstrNext = jsonstrNext.replace("\"user_id\"", param);
                        jsonstrNext = jsonstrNext.replace(str , user_id);
                        jsonstr = jsonstr.substring(0,i-1) + jsonstrNext;
                        if(log.isDebugEnabled()){
                            log.debug(functionId + ":AfterReplacement:[" + jsonstr +"]");
                        }
                        requestMap =JSONObject.parseObject(jsonstr);
                        //判断是否是需要发送客户user_id的情况
                        String userIdSupply = hsPessiomConfig.getUserIdSupply();
                        if(StringUtils.isNotBlank(userIdSupply)&&(userIdSupply.indexOf(functionId)>=0)){
                            requestMap.put("user_id", user_id);
                        }
                       // request.setAttribute("requestMap", requestMap);
                    }
                }
            }else{
                handlerError(servletResponse,
                             new ResultData<Object>(false,ErrorCodeConsts.HS_SESSION_ABATE,SpringUtils.getMessage(ErrorCodeConsts.HS_SESSION_ABATE)));
            }
        } catch (Exception e) {
           *//* ErrorLogPrint.print(requestMap, LOG, e);
            this.errorResponse(ErrorCode.FAIL, baseRequest, response);*//*
           log.error("HsAPPSessionTimeOut.checkAccount.error;{}",e);
        }*/
        return true;
    }

    public boolean checkToken(JSONObject result,String cacheKey,HttpServletRequest request,HttpServletResponse response){
        //JsonObject cachedate = cacheService.getClient().get(cacheKey).content();
        /*Bucket bucket=cacheService.getClient();
        JsonObject cachedate=bucket.get(cacheKey).content();
        if(null!=cachedate){
            String checkState = cachedate.containsKey("checkState")?cachedate.get("checkState").toString():"0";
            if("1".equals(checkState)){
                request.setAttribute("app_pressim",cachedate);
                long time = (Long) cachedate.get("time");
                long time2 = System.currentTimeMillis();
                if(time2-time>notlaytime){
                    cachedate.put("time", time2);
                    JsonDocument logininfo = JsonDocument.create(cacheKey, laytime, cachedate);
                    //延长cache使用时间(id,延期时间，更新超时时间，更新超时时间单位)
                    bucket.upsert(logininfo,outtime,timeUnit);
                }
            }else{
                handlerError(response,
                             new ResultData<Object>(false,ErrorCodeConsts.HS_SESSION_ABATE,SpringUtils.getMessage(ErrorCodeConsts.HS_SESSION_ABATE)));
                return false;
            }
        }else {
            handlerError(response,
                         new ResultData<Object>(false,ErrorCodeConsts.HS_SESSION_ABATE,SpringUtils.getMessage(ErrorCodeConsts.HS_SESSION_ABATE)));
            return false;
        }*/
        return false;
    }

    public void touchCache(String session_id){
        /*if(sessionIdMap.containsKey(session_id)){
            long time =  sessionIdMap.get(session_id);
            long time2 = System.currentTimeMillis();
            if((time2-time>=0)&&(time2-time<=600000)){
                return;
            }
        }
        //操作之前touch一下缓存
        Map<String, Object> requestMapTouchCb = new HashMap<String, Object>();
        requestMapTouchCb.put("sessionId", session_id);
       // hKT2Service.callService(requestMapTouchCb,"306154");
        Bucket bucket=cacheService.getClient();
        bucket.touch(session_id,sessionDefTimeOut);
        long time = System.currentTimeMillis();
        sessionIdMap.put(session_id, time);*/
    }

    @Override
    public void afterPropertiesSet() throws Exception
    {
        new Thread()
        {
            public void run()
            {
                sessionIdMap = new HashMap<String,Long>();
                try
                {
                    Thread.sleep(36000000L);
                }
                catch (InterruptedException e) {
                    sessionIdMap = new HashMap<String,Long>();
                    e.printStackTrace();
                }
            }
        }
            .start();
    }
}
