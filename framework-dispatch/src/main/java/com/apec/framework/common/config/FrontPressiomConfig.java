package com.apec.framework.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

import java.util.Map;

/**
 * @Auther: liliwei
 * @Date: 2018/5/14 19:19
 * @Description:
 */
@ConfigurationProperties(prefix = "hs")
@Configuration
@PropertySource(value = "classpath:hs-app-config.properties")
public class FrontPressiomConfig
{

    private Map<String,String> wsPathCode;


    private Map<String,String> msPathCode;

    public Map<String, String> getWsPathCode()
    {
        return wsPathCode;
    }

    public void setWsPathCode(Map<String, String> wsPathCode)
    {
        this.wsPathCode = wsPathCode;
    }

    public Map<String, String> getMsPathCode()
    {
        return msPathCode;
    }

    public void setMsPathCode(Map<String, String> msPathCode)
    {
        this.msPathCode = msPathCode;
    }
}
