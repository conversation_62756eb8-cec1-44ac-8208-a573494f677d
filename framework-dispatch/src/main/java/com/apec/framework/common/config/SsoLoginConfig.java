package com.apec.framework.common.config;

import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: liuc
 * @date: 2020/5/9
 * @desc:
 */
@Configuration
@ConfigurationProperties(prefix = "sso.login")
@Component(value = "ssoLoginConfig")
public class SsoLoginConfig {

    private List<String> urls;

    public List<String> getUrls() {
        return urls;
    }

    public void setUrls(List<String> urls) {
        this.urls = urls;
    }

    public boolean check(String url){
        if(StringUtils.isBlank(url)){
            return false;
        }
        if(urls.contains(url.toLowerCase())){
            return true;
        }
        return false;
    }
}
