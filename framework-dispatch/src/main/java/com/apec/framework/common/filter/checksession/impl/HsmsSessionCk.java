package com.apec.framework.common.filter.checksession.impl;

import com.apec.cache.base.CacheService;
import com.apec.framework.common.filter.checksession.AbsCkSesssion;
import com.apec.framework.nosession.common.util.CommonUtils;
import com.hundsun.eclp.biz.domain.user.UserAgent;
import com.hundsun.eclp.client.common.GenericUserAgent;
import com.hundsun.eclp.client.common.MemData;
import com.hundsun.network.cache.domain.CacheWrapper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.Map;

/**
 * 恒生ms Session 超时校验
 * Created by l<PERSON><PERSON> on 2018/4/25.
 */
@Component("hsmsSessionTimeOut")
public class HsmsSessionCk extends AbsCkSesssion
{
    /**
     * 是否限制多处登录（默认不限制）
     */
    @Value("${limit.multi.login:true}")
    private boolean limitMultiLogin;

    /*@Value("${app.domain:apec.develop}")
    private String appDomain;*/


    public boolean doSessionFilter(HttpServletRequest servletRequest,HttpServletResponse servletResponse){
        //added on 20170526 start post CSRF的控制
        String httpMethod = servletRequest.getMethod();
        log.info("====HsmsSessionCk.Method:"+httpMethod);
        /*if("POST".equals(httpMethod)) {
            String refererUrl = servletRequest.getHeader("Referer");
            if(StringUtils.isNotBlank(refererUrl)) {
                //如果请求域名和配置的域名 不同域，且是post请求，则CSRF，报无权限页面
                 String reqDomain = HsHandleUtils.parseDomainByUrl(refererUrl);
                if(StringUtils.isNotBlank(appDomain)) {
                    if(!appDomain.startsWith(".")) {
                        appDomain = "." + appDomain;
                    }
                    if(!(appDomain.contains(reqDomain) || reqDomain.contains(appDomain))) {
                        return false;
                    }
                }
            }
        }*/
        return checkSessionStatus(servletRequest);
    }

    /**
     * 校验session是否超时
     * @param request
     * @return
     */
    private boolean checkSessionStatus(HttpServletRequest request) {
       HttpSession session = request.getSession();
        GenericUserAgent genericUserAgent = (GenericUserAgent)session.getAttribute(GenericUserAgent.ECLP_USER_AGENT);
        log.info("///HSms.session.loginUser:{}",genericUserAgent);
        if(genericUserAgent == null) {
            log.error("cookie not exist!");
            return false;
        }
        UserAgent agent = new UserAgent(genericUserAgent);
        Map<String, Map<Integer, Integer>> subsystemPermissionMap
            = null;
        if(subsystemPermissionMap == null) {
            log.error("ErrorEnum.CACHE_FLUSHED:%s" ,"session time out");
            return false;
        }
        Map<Integer, Integer> codePressiom=subsystemPermissionMap.get(agent.ECLP_SUBSYSTEM_CODE);
        request.setAttribute(GenericUserAgent.USER_AGENT, agent);
        request.setAttribute("codePressiom",codePressiom);
        //如果限制多处登录，判断userAgent和服务器session中是否一致
        if(limitMultiLogin) {
            //客户端session
            UserAgent clientSession = (UserAgent) session.getAttribute(UserAgent.SESSION_AGENT);
            log.info("UserAgent.user.userAccount:{},UserAgent.user.name{}",clientSession.getUserAccount(),clientSession.getName());
            if(clientSession == null) {
                log.error("ErrorEnum.COOKIE_NOT_EXIST");
                return false;
            }
            //服务端session
            UserAgent valueWrapper = null;
            log.info("couchbase.session.valueWrapper:{}",valueWrapper);
            if(valueWrapper == null) {
                log.error("ErrorEnum.SESSION_NOT_EXIST");
                return false;
            }
            if(isAnotherClient(clientSession, valueWrapper)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 校验客户端token与服务端token是否相等
     * @param clientSession
     * @param serverSession
     * @return
     */
    private boolean isAnotherClient(UserAgent clientSession, UserAgent serverSession) {
        if(StringUtils.equals(clientSession.getSessionToken(), serverSession.getSessionToken())) {
            return false;
        }
        return !StringUtils.equals(clientSession.getLastLoginIp(), serverSession.getLastLoginIp());
    }
}
