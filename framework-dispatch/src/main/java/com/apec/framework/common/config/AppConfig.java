package com.apec.framework.common.config;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.Filter;

import com.hundsun.jresplus.beans.ObjectFactory;
import com.hundsun.jresplus.beans.ObjectFactoryImpl;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.filter.CharacterEncodingFilter;

import com.apec.framework.common.constant.ConfigConsts;

@Configuration
public class AppConfig
{

    @Bean
    public ObjectFactory objectFactory(){
        return new ObjectFactoryImpl();
    }

    @Bean
    public Filter characterEncodingFilter()
    {
        CharacterEncodingFilter characterEncodingFilter = new CharacterEncodingFilter();
        characterEncodingFilter.setEncoding(ConfigConsts.SYSTEM_ENCODING);
        characterEncodingFilter.setForceEncoding(true);
        return characterEncodingFilter;
    }

    @Bean("clientTemplate")
    @LoadBalanced
    RestTemplate restTemplate()
    {
        RestTemplate restTemplate = new RestTemplate();
        StringHttpMessageConverter converter = new StringHttpMessageConverter(ConfigConsts.SYSTEM_CHARSET);
        ByteArrayHttpMessageConverter byteArrayHttpMessageConverter = new ByteArrayHttpMessageConverter();
        FormHttpMessageConverter formHttpMessageConverter = new FormHttpMessageConverter();
        List<HttpMessageConverter<?>> messageConverters = new ArrayList<>();
        messageConverters.add(converter);
        messageConverters.add(byteArrayHttpMessageConverter);
        messageConverters.add(formHttpMessageConverter);
        restTemplate.setMessageConverters(messageConverters);
        return restTemplate;
    }
}
