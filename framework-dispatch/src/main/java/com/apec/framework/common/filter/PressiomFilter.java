package com.apec.framework.common.filter;

import com.apec.framework.common.config.FrontPressiomConfig;
import com.apec.framework.common.config.HsPessiomConfig;
import com.apec.framework.common.constant.FrameConsts;
import com.apec.framework.common.filter.checkpressiom.impl.HsAppPressiomCk;
import com.apec.framework.common.filter.checkpressiom.impl.HsPressiomCk;
import com.apec.framework.nosession.dto.UserWsAgent;
import com.hundsun.eclp.client.common.GenericUserAgent;
import com.hundsun.exchange.uc.client.dto.UserAgent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.ServletWebRequest;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.Map;

/**
 * 大宗权限过滤
 * Created by liliwei on 2018/4/25.
 */
/*@Component("pressiomFilter")
public class PressiomFilter implements Filter
{
    private static final Logger log = LoggerFactory.getLogger(PressiomFilter.class);

    @Autowired
    private HsAppPressiomCk hsAppPressiomCk;

    @Autowired
    private HsPressiomCk hsPressiomCk;

    //@Override
    public void init(FilterConfig filterConfig) throws ServletException
    {

    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain filterChain)
        throws IOException, ServletException
    {
        HttpServletRequest req=(HttpServletRequest)request;
        HttpServletResponse resp=(HttpServletResponse)request;

        String type=req.getHeader(FrameConsts.SESSION_VALIDATE_TYPE);
        boolean flag=false;
        if(FrameConsts.DZ_SESSION_VALIDATE_APP.equals(type)){
            flag=hsAppPressiomCk.doPressiomFilter(req,resp);
        }else {
            flag=hsPressiomCk.doPressiomFilter(req,resp);
        }
        if(flag){
            filterChain.doFilter(request,response);
        }

    }

    @Override
    public void destroy()
    {

    }

}*/
