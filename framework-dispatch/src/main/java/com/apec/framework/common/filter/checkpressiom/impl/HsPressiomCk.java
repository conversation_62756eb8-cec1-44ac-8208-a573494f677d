package com.apec.framework.common.filter.checkpressiom.impl;

import com.apec.framework.common.config.FrontPressiomConfig;
import com.apec.framework.common.constant.FrameConsts;
import com.apec.framework.common.filter.checkpressiom.AbsCkPressiom;
import com.hundsun.eclp.client.common.GenericUserAgent;
import com.hundsun.exchange.uc.client.dto.UserAgent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.ServletWebRequest;

import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.Map;

/**
 * @Auther: liliwei
 * @Date: 2018/5/14 19:11
 * @Description:
 */
@Component("hsPressiomCk")
public class HsPressiomCk extends AbsCkPressiom
{

    @Autowired
    private FrontPressiomConfig frontPressiomConfig;

    @Override
    public boolean doPressiomFilter(HttpServletRequest servletRequest, HttpServletResponse servletResponse)
        throws IOException, ServletException
    {
        String type = servletRequest.getHeader(FrameConsts.SESSION_VALIDATE_TYPE);
        HttpSession session = servletRequest.getSession();
        if(FrameConsts.DZ_SESSION_VALIDATE_MS.equals(type)){
            return checkMsPressiom(servletRequest);
        }else{
            return checkWsPressiom(servletRequest,session);
        }
    }

    public boolean checkWsPressiom(ServletRequest request,HttpSession session){
        ServletWebRequest servletWebRequest=(ServletWebRequest)request;
        UserAgent agent = (UserAgent)servletWebRequest.getAttribute(UserAgent.UC_SESSION_ID, RequestAttributes.SCOPE_REQUEST);
        if(!isCurrentUser(servletWebRequest, agent)) {
            return false;
        }
        /*if(!isBeyondAuth(servletWebRequest, agent, handlerMethod)) {
            return false;
        }
        if(!isProductBeyondAuth(servletWebRequest, agent, handlerMethod)) {
            return false;
        }*/
        if (!wspass(agent, request)) {
            return false;
            // 到异常控制类中去处理
        }
        return true;
    }

    /**
     * 是否跨用户的提交的校验
     * @param webRequest
     * @param userAgent
     * @return
     * <AUTHOR>
     */
    private boolean isCurrentUser(ServletWebRequest webRequest, UserAgent userAgent) {
        HttpServletRequest request = webRequest.getRequest();
        String sucsid = request.getParameter(FrameConsts.SUC_SID);
        //没有在request中传当前userId参数
        /*if(com.hundsun.jresplus.common.util.StringUtil.isBlank(sucsid)) {
            log.info("UserAuthorityHandlerInterceptor.isCurrentUser: sucsid param is null");
            return true;
        }
        //请求参数中的sucsid和当前登录用户id不一样，抛会员会话过期错误
        return com.hundsun.jresplus.common.util.StringUtil.equals(sucsid, userAgent.getUserId().toString());*/
        return false;
    }

    private boolean checkMsPressiom(ServletRequest request) {
        com.hundsun.eclp.biz.domain.user.UserAgent userAgent=
            (com.hundsun.eclp.biz.domain.user.UserAgent)request.getAttribute(GenericUserAgent.USER_AGENT);
        return msPass(userAgent,request);
    }

    private boolean msPass(com.hundsun.eclp.biz.domain.user.UserAgent agent, ServletRequest request){
        HttpServletRequest req=(HttpServletRequest)request;
        String servletPath = req.getServletPath();
        if(!frontPressiomConfig.getMsPathCode().containsKey(servletPath)) return true;
        String codes=frontPressiomConfig.getMsPathCode().get(servletPath);
        Map<Integer,Integer> codePressiom =
            ( Map<Integer,Integer>) request.getAttribute("codePressiom");
        boolean havePermission = false;

        for(String code:codes.split(",")){
            if (null!=codePressiom.get(code))
            {
                havePermission = true;
                break;
            }
        }
        return havePermission;
    }

    private  boolean wspass(UserAgent agent, ServletRequest request){

        //不在权限拦截名单的请求放行
        HttpServletRequest req=(HttpServletRequest)request;
        String servletPath = req.getServletPath();
        if(!frontPressiomConfig.getWsPathCode().containsKey(servletPath)) return true;
        String codes=frontPressiomConfig.getWsPathCode().get(servletPath);

        boolean havePermission = false;
        //配置文件中的system.code
        for(String code : codes.split(",")){
            String currCode = agent.getCurrentSubSystemCode();
            int splitIndex = currCode.indexOf(",");
            String subSystemCode = null;
            if(splitIndex != -1) {
                //start 港交所不支持多个子系统code解析，原因是枚举要更改，麻烦
                String firstSubCode = currCode.substring(0, splitIndex);
                //end
                subSystemCode = firstSubCode;
                havePermission = agent.havePermission(code, firstSubCode);
                if(!havePermission) {
                    return  havePermission = false;
                }
            } else {
                subSystemCode = currCode;
                havePermission = agent.havePermission(code,currCode);
                if(!havePermission) {
                    havePermission = false;
                }
            }
        }
        if (havePermission) {
            havePermission= true;
        }
        return havePermission;
    }
}
