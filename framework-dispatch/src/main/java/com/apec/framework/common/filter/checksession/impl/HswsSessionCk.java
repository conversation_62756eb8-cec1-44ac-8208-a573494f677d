package com.apec.framework.common.filter.checksession.impl;

import com.apec.framework.common.constant.FrameConsts;
import com.apec.framework.common.filter.checksession.AbsCkSesssion;
import com.apec.framework.common.util.IpUtils;
import com.apec.framework.nosession.dto.ClientUAInfo;
import com.hundsun.exchange.uc.client.dto.UserAgent;
import com.hundsun.network.cache.domain.CacheWrapper;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * 恒生WS session 超时校验
 * Created by lili<PERSON> on 2018/4/25.
 */
@Component("hswsSessionTimeOut")
public class HswsSessionCk extends AbsCkSesssion
{

    @Value("${response.out.charset:UTF-8}")
    private String characterEncoding;

    /**
     * 子系统code
     */
    private String systemCode;

    /**
     * 是否允许子系统间的链接访问
     */

    //private IClientSubsysService clientSubsysService;

    private boolean crossSubSystemEnable = true;

    public String getSystemCode()
    {
        return systemCode;
    }

    public void setSystemCode(String systemCode)
    {
        this.systemCode = systemCode;
    }

    public boolean doSessionFilter(HttpServletRequest servletRequest,HttpServletResponse servletResponse)
    {
        if(StringUtils.isNotBlank(this.characterEncoding)) {
            try
            {
                servletRequest.setCharacterEncoding(this.characterEncoding);
            }catch (IOException e){
                log.error("HswsSessionTimeOut.doSessionFilter.setCharacterEncoding:{}",e);
            }

        }
        return isSessionTimeOut(servletRequest);
    }

    /**
     * 校验session是否超时
     * @param servletRequest
     * @param servletRequest
     * @return
     */
    public boolean isSessionTimeOut(HttpServletRequest servletRequest) {
        HttpSession session = servletRequest.getSession();
        ClientUAInfo clientinfo = getClientInfo(servletRequest, session);
        Cookie[] cookies = servletRequest.getCookies();
        if(null==clientinfo){
            log.info("cookie is not existing ...");
            return false;
        }
        String sessionId = clientinfo.getSessionId();
        /*CacheWrapper valueWrapper = (CacheWrapper)cacheService.get(sessionId);*/
        UserAgent userAgent =null;
        if( null == userAgent ){
            log.info("session is not in the cache server ... maybe session is timeout");
            return false;
        }

        String clientToken = clientinfo.getUserToken();

        //判断是否允许多处登入
        if(userAgent.isForcedLogout(clientToken)) {
            log.info("userLogin by anther client...");
            String reqIp = IpUtils.getIpAddr(servletRequest);
            Object lastLoginIp = userAgent.getUserMap().get("currentLoginIp");
            if(lastLoginIp != null && !lastLoginIp.toString().equals(reqIp)) {
                log.info("session is forced logout by another client");
                return false;
            }
        }
        setUserAgent(servletRequest,userAgent);
        //added by tanhl 20150406 end
        if(!crossSubSystemEnable) {
            //added by tanhl 20131223 北金所不同子系统（客户端之间链接不能互访）
            String currSubCode = userAgent.getCurrentSubSystemCode();
            if(!FrameConsts.UCWEB_SUB_CODE.equals(systemCode) && //会员子系统的排除在外
               !systemCode.equals(currSubCode )) {
                return false;
            }
        }
        userAgent.setCurrentSubSystemCode(systemCode);
        return true;
    }

   private void setUserAgent(HttpServletRequest request, UserAgent userAgent) {
        request.setAttribute(UserAgent.UC_SESSION_ID, userAgent);
        request.setAttribute(UserAgent.UC_USR_AGT, userAgent);
        request.setAttribute(UserAgent.UC_USER, userAgent.getUserMap());
        request.setAttribute(UserAgent.UC_SUB_SYSTEM_CODE, systemCode);
    }


    /**
     * 获取客户端session
     * @param servletRequest
     * @param session
     * @return
     */
    public ClientUAInfo getClientInfo(HttpServletRequest servletRequest, HttpSession session){
        ClientUAInfo clientUAInfo = (ClientUAInfo)session.getAttribute(FrameConsts.CLIENT_UA_INFO);
        if(null!=clientUAInfo){
            return clientUAInfo;
        }

        clientUAInfo = new ClientUAInfo();
        Cookie[] cookies = servletRequest.getCookies();
        if(ArrayUtils.isEmpty(cookies)) {
            return null;
        }
        for(Cookie cookie : cookies){
            String cookieValue = cookie.getValue();
            String cookieName = cookie.getName();
            if(FrameConsts.UC_SESSION_ID.equals(cookieName)){
                clientUAInfo.setSessionId(cookieValue);
            } else if ( FrameConsts.UC_ID.equals(cookieName)) {
                clientUAInfo.setUserId(cookieValue);
            } else if (FrameConsts.COOKIE_VALID_TOKEN.equals(cookieName)) {
                clientUAInfo.setUserToken(cookieValue);
            }
        }
        return clientUAInfo;
    }
}