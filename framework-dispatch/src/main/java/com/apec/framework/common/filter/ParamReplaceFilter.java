/*
package com.apec.framework.common.filter;

import javax.servlet.*;
import java.io.IOException;

*/
/**
 * @Auther: liliwei
 * @Date: 2018/5/15 15:39
 * @Description:
 *//*

public class ParamReplaceFilter implements Filter
{
    @Override
    public void init(FilterConfig filterConfig) throws ServletException
    {

    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
        throws IOException, ServletException
    {

    }

    @Override
    public void destroy()
    {

    }
}
*/
