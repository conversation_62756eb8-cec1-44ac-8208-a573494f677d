package com.apec.framework.common.filter.checkpressiom;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @Auther: liliwei
 * @Date: 2018/5/15 14:21
 * @Description:
 */
public interface ICKPressiom
{
    boolean doPressiomFilter(HttpServletRequest servletRequest, HttpServletResponse servletResponse) throws IOException,
        ServletException;
}
