package com.apec.framework.common.util;

import java.util.Enumeration;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.HttpHeaders;

/**
 * 类 编 号：
 * 类 名 称：
 * 内容摘要：
 * 创建日期：2017/11/21 11:52
 * <AUTHOR>
 */
public class HttpServletUtils
{
    public static HttpHeaders getHeaders(HttpServletRequest req)
    {
        HttpHeaders headers = new HttpHeaders();
        Enumeration<String> headerNames = req.getHeaderNames();
        while (headerNames.hasMoreElements())
        {
            String key = headerNames.nextElement();
            String value = req.getHeader(key);
            headers.set(key, value);
        }
        return headers;
    }
}
