package com.apec.framework.common.filter;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.apec.cache.base.CacheService;
import com.apec.framework.auth.RedisConstants;
import com.apec.framework.common.config.SsoLoginConfig;
import com.apec.framework.common.constant.CacheConsts;
import com.apec.framework.common.constant.ErrorCodeConsts;
import com.apec.framework.common.constant.FrameConsts;
import com.apec.framework.common.enumtype.App;
import com.apec.framework.common.enumtype.Platform;
import com.apec.framework.common.exception.ApecRuntimeException;
import com.apec.framework.common.model.ResultData;
import com.apec.framework.common.util.IpUtils;
import com.apec.framework.common.util.JsonUtils;
import com.apec.framework.common.util.SpringUtils;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jwts;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.*;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.*;

/**
 * 类 编 号：
 * 类 名 称：SessionTimeOutFilter
 * 内容摘要：处理session超时
 * 完成日期：
 * 编码作者：
 */
@Component("sessionTimeOutFilter")
public class SessionTimeOutFilter implements Filter {

    private Logger logger = LoggerFactory.getLogger(getClass());


    @Resource(name = "redisService")
    private CacheService cacheService;

    @Value("${allowSuffix}")
    private String[] allowSuffix;

    @Value("${jwt.token.key}")
    private String jwtTokenKey;

    @Value("${jwt.token.header}")
    private String jwtTokenHeader;

    @Autowired
    @Qualifier("permissionTemplate")
    private RestTemplate permissionTemplate;

    @Value("${check_permission_url}")
    private String checkPermissionUrl;

    @Value("${serverurl.white.list}")
    private String[] whiteServers;

    private List<String> whiteList = new ArrayList<>();

    @Resource(name = "ssoLoginConfig")
    private SsoLoginConfig ssoLoginConfig;

    @Value("${spring.profiles.active}")
    private String active;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        whiteList = Arrays.asList(whiteServers);
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        HttpServletRequest servletRequest = (HttpServletRequest) request;
        HttpServletResponse servletResponse = (HttpServletResponse) response;
        logger.info("当期请求url：" + servletRequest.getRequestURI());

        servletResponse.setHeader("Access-Control-Allow-Origin", "*");
        servletResponse.setHeader("Access-Control-Allow-Methods", "POST, GET, OPTIONS, DELETE,PATCH");
        servletResponse.setHeader("Access-Control-Max-Age", "3600");
        servletResponse.setHeader("Access-Control-Allow-Headers",
                "x-requested-with,Content-Type,CMS-userId,test,CMS-userOrg,CMS-labelId,role-type,"
                        + "source-type,token,User-Agent");
        servletResponse.setHeader("Access-Control-Allow-Credentials", "ture");

        String str = getToken(servletRequest);
        logger.info("///////当期请求TOKEN：" + str);

        String requestURI = servletRequest.getRequestURI();
        logger.info("///////当期请求URL：" + requestURI);

        boolean flagURIWhileIp = checkUriLimitWhileIpLegal(servletRequest);
        if (!flagURIWhileIp) {
            ResultData resultData = new ResultData(false, ErrorCodeConsts.COMMON_ERROR_RUI_NOT_IN_WHITE_IP,
                    SpringUtils.getMessage(ErrorCodeConsts.COMMON_ERROR_RUI_NOT_IN_WHITE_IP, new String[]{requestURI}));
            handlerError(servletResponse, resultData, "checkUriLimitWhileIpLegal"
            );
        }

        String prefixUrl = requestURI.split("/")[1];
        String endUrl = null;
        if (requestURI.contains(".")) {
            endUrl = "." + requestURI.split("\\.")[1];
        }
        //缓存中获取白名单
        String oauthAllowUrlCache = cacheService.get(RedisConstants.DISPATCH_WHITE_LIST_URL);
        List<String> oauthAllowUrl = JSONObject.parseArray(oauthAllowUrlCache, String.class);

        String prefixUrlCache = cacheService.get(RedisConstants.DISPATCH_WHITE_LIST_URL_PREFIX);
        List<String> allowPrefix = JSONObject.parseArray(prefixUrlCache, String.class);
        //RequestDecorator decorator = new RequestDecorator(servletRequest);
        //todo chain.doFilter(request, response); -> chain.doFilter(decorator, response);
        if (ssoLoginConfig.check(requestURI)) {
            //统一登录入口直接放行
            chain.doFilter(request, response);
        }else if (checkAllowUrl(oauthAllowUrl, requestURI)) {
            if (StringUtils.isNotEmpty(str) && str.contains("bearer")) {
                extraHandle(servletRequest, servletResponse, str, false);
            }
            chain.doFilter(request, response);
        } else if (checkAllowUrl(allowPrefix, prefixUrl)) {
            if (StringUtils.isNotEmpty(str) && str.contains("bearer")) {
                extraHandle(servletRequest, servletResponse, str, false);
            }
            chain.doFilter(request, response);
        } else if (checkAllowUrl(whiteList, prefixUrl)) {
            if (StringUtils.isNotEmpty(str) && str.contains("bearer")) {
                extraHandle(servletRequest, servletResponse, str, false);
            }
            chain.doFilter(request, response);
        } else if (null != endUrl && Arrays.asList(allowSuffix).contains(endUrl.toLowerCase())) {
            if (StringUtils.isNotEmpty(str) && str.contains("bearer")) {
                extraHandle(servletRequest, servletResponse, str, false);
            }
            chain.doFilter(request, response);
        } else {
            //检测token是否是当前环境
            if(!checkActive(str,servletResponse)){
                return;
            }
            ResultData<String> resultData = checkPermission(servletRequest, servletResponse);
            if (!resultData.isSucceed()) {
                handlerError(servletResponse, resultData);
            } else {
                if(!extraHandle(servletRequest, servletResponse, str, false)){
                    return;
                };
                String uri = ((HttpServletRequest) request).getRequestURI();
                if (uri.endsWith(".dubbo")) {
                    HttpServletRequest req = (HttpServletRequest) request;
                    MutableHttpServletRequest mutableRequest = new MutableHttpServletRequest(req);
                    mutableRequest.putHeader("x-custom-header", ".dubbo");
                    chain.doFilter(mutableRequest, response);
                } else {
                    chain.doFilter(request, response);
                }
            }

        }
    }

    private boolean checkActive(String token, HttpServletResponse response) {
        String encode = token.split("bearer")[1].trim();
        ResultData<?> rd = new ResultData<String>();
        rd.setSucceed(false);
        rd.setErrorMsg("请求token无效,请重新登录!");
        try {
            JSONObject claimsJSON = parseToken(encode);
            String tokenActive = claimsJSON.getString("active");
            if(!active.equals(tokenActive)){
                rd.setErrorCode("token_active_not_match");
                handlerError(response,rd);
                return false;
            }
        }catch (Exception e){
            rd.setErrorCode("token_resolve_fail");
            rd.setErrorMsg("token已过期,请重新登录!");
            logger.error("token resolve fail",e);
            handlerError(response,rd);
            return false;
        }
        return true;
    }

    public boolean extraHandle(HttpServletRequest servletRequest, HttpServletResponse response, String token, boolean check) {
        CheckRs rs = setAttrUserInfo(servletRequest, token);
        if (check && !rs.isSuc()) {
            ResultData rd = new ResultData();
            rd.setSucceed(false);
            rd.setErrorCode("LOGIN_STATUS_DISABLE");
            rd.setErrorMsg(rs.getMsg());
            handlerError(response, rd);
            return false;
        }
        assembleRequest(servletRequest, rs.getMap());
        setSensorsInfo(servletRequest);
        return true;
    }

    public CheckRs setAttrUserInfo(HttpServletRequest servletRequest, String str) {
        String encode = str.split("bearer")[1].trim();
        CheckRs rs = new CheckRs();
        try {
            rs.setSuc(true);
            JSONObject claimsJSON = parseToken(encode);
            logger.info("token info : {}", claimsJSON.toJSONString());
            String userType = getLoginUserType(claimsJSON);
            String accountId = getAccountId(claimsJSON);
            String userId = getUserId(claimsJSON);
            String userInfo = "{}";
            String accountInfo = "{}";
            List<String> authorities = getAuthorities(claimsJSON);
            String systemType = servletRequest.getHeader(FrameConsts.SYSTEM_TYPE);
            rs.addParam(FrameConsts.ROLE_IDS, JSONObject.toJSONString(authorities));
            rs.addParam(FrameConsts.SYSTEM_TYPE, StringUtils.isBlank(systemType) ? "null" : systemType);
            String sourceType = getScope(claimsJSON);
            String clientId = getClientId(claimsJSON);
            if (FrameConsts.WEB.equals(clientId)) {
                if (StringUtils.isNotBlank(userId)) {
                    if (!checkUumsUser(userId, rs)) {
                        return rs;
                    }
                    userInfo = cacheService.get(RedisConstants.PC_MAGPIE_USER_KEY + userId, String.class);
                    Set<String> roles = cacheService.members(RedisConstants.UUMS_USER_ROLES + userId);
                    if (CollectionUtils.isNotEmpty(roles)) {
                        rs.addParam(FrameConsts.ROLE_IDS, JSONObject.toJSONString(roles));
                    }
                }
                if (!"{}".equals(userInfo)) {
                    JSONObject jo = JSONObject.parseObject(userInfo);
                    rs.addParam(FrameConsts.USER_MARKET, jo.getString("marketCodes"));
                }
            } else if (FrameConsts.APP.equals(clientId)) {
                if (StringUtils.isNotBlank(userId)) {
                    if (!checkUcUser(userId, rs)) {
                        return rs;
                    }
                    userInfo = cacheService.get(RedisConstants.APP_MAGPIE_USER_KEY + userId, String.class);
                    if (StringUtils.isNotBlank(accountId)) {
                        accountInfo = cacheService.get(RedisConstants.APP_MAGPIE_ACCOUNT_KEY + accountId, String.class);
                        if (!checkAccount(accountId, rs)) {
                            return rs;
                        }
                        Set<String> roles = cacheService.members(RedisConstants.ACCOUNT_ROLES + accountId);
                        if (CollectionUtils.isNotEmpty(roles)) {
                            rs.addParam(FrameConsts.ROLE_IDS, JSONObject.toJSONString(roles));
                        }
                    }
                }
                if (userInfo != null && !"{}".equals(userInfo)) {
                    JSONObject jo = JSONObject.parseObject(userInfo);
                    rs.addParam(FrameConsts.USER_MARKET, jo.getString("marketCodes"));
                }
            }
            rs.addParam(FrameConsts.USER_NO, userId);
            rs.addParam(FrameConsts.USER_INFO, userInfo);
            rs.addParam(FrameConsts.ACCOUNT_INFO, accountInfo);
            rs.addParam(FrameConsts.LOGIN_USER_TYPE, userType);

            rs.addParam(FrameConsts.LOGIN_CLIENT_ID, clientId);
        } catch (Exception e) {
            logger.error("////Login token解析失败：", e);
            rs.setSuc(false);
            rs.setMsg("登录信息无效");
            if (e instanceof ExpiredJwtException) {
                rs.setMsg("登录已过期");
            }
        }
        return rs;
    }

    private boolean checkUcUser(String userId, CheckRs rs) {
        String status = cacheService.get(RedisConstants.UC_USER_DISABLE_ + userId);
        if (StringUtils.isNotBlank(status)) {
            rs.setSuc(false);
            rs.setMsg("客户状态异常!");
        }
        return rs.isSuc();
    }

    private boolean checkUumsUser(String userId, CheckRs rs) {
        String status = cacheService.get(RedisConstants.UUMS_USER_DISABLE_ + userId);
        if (StringUtils.isNotBlank(status)) {
            rs.setSuc(false);
            rs.setMsg("人员状态异常!");
        }
        return rs.isSuc();
    }

    private boolean checkAccount(String accountId, CheckRs rs) {
        String status = cacheService.get(RedisConstants.ACCOUNT_DISABLE + accountId);
        if (StringUtils.isNotBlank(status)) {
            rs.setSuc(false);
            rs.setMsg("帐号状态异常!");
        }
        return rs.isSuc();
    }

    private boolean checkUumsAccount(String accountId, CheckRs rs) {
        String status = cacheService.get(RedisConstants.ACCOUNT_DISABLE + accountId);
        if (StringUtils.isNotBlank(status)) {
            rs.setSuc(false);
            rs.setMsg("帐号状态异常!");
        }
        return rs.isSuc();
    }

    private void assembleRequest(HttpServletRequest servletRequest, Map<String, Object> map) {
        map.forEach((k, v) -> {
            servletRequest.setAttribute(k, v);
        });
    }

    public void setSensorsInfo(HttpServletRequest servletRequest) {
        String appName = servletRequest.getHeader(FrameConsts.APP_NAME);
        String productLine = servletRequest.getHeader(FrameConsts.PRODUCT_LINE);
        String platformType = servletRequest.getHeader(FrameConsts.PLATFORM_TYPE);
        logger.info("sensors info, app_name: {}, product_line: {}, platform_type: {}", appName, productLine, platformType);
        servletRequest.setAttribute(FrameConsts.APP_NAME, StringUtils.isBlank(appName) ? App.ZNXY.name() : appName);
        servletRequest.setAttribute(FrameConsts.PRODUCT_LINE, StringUtils.isBlank(productLine) ? "NULL" : productLine);
        servletRequest.setAttribute(FrameConsts.PLATFORM_TYPE, StringUtils.isBlank(platformType) ? Platform.WEB.name() : platformType);
    }

    @Override
    public void destroy() {
        // TODO Auto-generated method stub

    }

    public ResultData<String> checkPermission(HttpServletRequest request, HttpServletResponse servletResponse) {
        String token = null;
        try {
            token = getToken(request);
        } catch (Exception e) {
            logger.error("///获取token失败");
        }

        HttpHeaders headers = new HttpHeaders();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String key = headerNames.nextElement();
            String value = request.getHeader(key);
            headers.set(key, value);
        }
        boolean authorization = headers.containsKey("Authorization");
        if (!authorization && StringUtils.isNotEmpty(token)) {
            headers.set("Authorization", token);
        }
        String requestURI = request.getRequestURI();
        MultiValueMap<String, String> paraMap = new LinkedMultiValueMap<String, String>();
        headers.set("checkUrl", requestURI);
        //  paraMap.set("checkUrl", requestURI);
        HttpEntity<MultiValueMap<String, String>> httpEntity = new HttpEntity<>(paraMap, headers);
        ResponseEntity<String> postForEntity = permissionTemplate.postForEntity(checkPermissionUrl, httpEntity, String.class);
        ResultData<String> resultData = JSONObject.parseObject(postForEntity.getBody(), ResultData.class);
        return resultData;
    }

    /**
     * 返回错误信息
     *
     * @param response 响应头
     */
    private void handlerError(HttpServletResponse response, ResultData<?> resultData) {
        try {
            String resultJSON = JsonUtils.toJSONString(resultData);
            logger.error("handle error , return content :  {}", resultJSON);
            response.setCharacterEncoding("UTF-8");
            PrintWriter out = response.getWriter();
            response.setHeader("Content-Type ", "application/json;charset=UTF-8");
            out.print(resultJSON);
            out.close();
        } catch (IOException e) {
            logger.error("//// sessionTimeOutFilter.hasPermission exception case:" + e.getMessage());
            throw new ApecRuntimeException("sessionTimeOutFilter.hasPermission exception case:", e);
        }
    }

    /**
     * 获取token
     *
     * @throws UnsupportedEncodingException
     */
    public String getToken(HttpServletRequest request) throws UnsupportedEncodingException {
        String authorization = request.getHeader("Authorization");
        if (StringUtils.isNotEmpty(authorization)) {
            return authorization;
        }

        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if ("Authorization".equals(cookie.getName())) {
                    authorization = cookie.getValue();
                    authorization = URLDecoder.decode(authorization, "UTF-8");
                    break;
                }
            }
        }
        return authorization;

    }

    private JSONObject parseToken(String token) throws UnsupportedEncodingException {
        Claims claims = Jwts.parser().setSigningKey(jwtTokenKey.getBytes("UTF-8"))
                .parseClaimsJws(token).getBody();
        JSONObject claimsJson = JSONObject.parseObject(JSONObject.toJSONString(claims));
        return claimsJson;
    }

    private String getScope(JSONObject jsonObject) {
        JSONArray ja = jsonObject.getJSONArray("scope");
        return ja.get(0).toString();
    }

    private String getUserName(JSONObject jsonObject) {
        return jsonObject.getString(jwtTokenHeader);
    }

    private String getClientId(JSONObject jsonObject) {
        return jsonObject.getString("client_id");
    }

    private List<String> getAuthorities(JSONObject jsonObject) {
        JSONArray ja = jsonObject.getJSONArray("authorities");
        //ja为空的情况一般是client模式
        if (ja == null) {
            return new ArrayList<>();
        }
        return ja.toJavaList(String.class);
    }

    private String getUserId(JSONObject jsonObject) {
        return jsonObject.getString("userId");
    }

    private String getAccountId(JSONObject jsonObject) {
        return jsonObject.getString("accountId");
    }

    private String getLoginUserType(JSONObject jsonObject) {
        return jsonObject.getString("userType");
    }

    private boolean checkAllowUrl(List<String> oauthAllowUrls, String requestURI) {
        if (CollectionUtils.isNotEmpty(oauthAllowUrls)) {
            for (String allowUrl : oauthAllowUrls) {
                if (allowUrl.toLowerCase().equals(requestURI.toLowerCase())) {
                    return true;
                }
            }
        }
        return false;
    }


    /**
     * 返回错误信息
     *
     * @param response 响应头
     */
    private void handlerError(HttpServletResponse response, ResultData<String> resultData, String msg) {
        try {
            response.setCharacterEncoding("UTF-8");
            PrintWriter out = response.getWriter();
            response.setHeader("Content-Type ", "application/json;charset=UTF-8");
            out.print(JsonUtils.toJSONString(resultData));
            out.close();
        } catch (IOException e) {
            logger.error("//// sessionTimeOutFilter.{} exception.", msg);
            throw new ApecRuntimeException("sessionTimeOutFilter.{} exception case:", msg, e);
        }
    }

    private boolean checkUriLimitWhileIpLegal(HttpServletRequest servletRequest) {
        String requestURI = servletRequest.getRequestURI();
        String ip = IpUtils.getRemoteIpPlus(servletRequest);

        String whileIpURI = cacheService.get(CacheConsts.CACHE_DISPATCH_WHITE_IP_URL);
        if (StringUtils.isNotBlank(whileIpURI) && StringUtils.isNotBlank(ip)) {
            if (whileIpURI.toLowerCase().indexOf(requestURI.toLowerCase()) != -1) {
                logger.debug("checkUriLimitWhileIpLegal ip({}) request URI({}) , ****** system check uri in limitIpUrl:{} .",
                        ip, requestURI, whileIpURI);
//                 String urlWhileIpStr = ",**************,************,";
                String urlWhileIpStr = cacheService.get(CacheConsts.CACHE_DISPATCH_URI_WHITE_IP_PREFIX + requestURI.toLowerCase());

                if (urlWhileIpStr.toLowerCase().indexOf(ip.toLowerCase()) != -1) {
                    logger.info("****** system allow this ip, white ip list:{}.", urlWhileIpStr);
                    return true;
                } else {
                    logger.error(">>>>>> system limit this ip, white ip list:{}.", urlWhileIpStr);
                    return false;
                }
            } else {
                logger.debug("skip (checkUriLimitWhileIpLegal) , system check uri not in limit url.");
            }
        } else {
            logger.error("skip (checkUriLimitWhileIpLegal) , whileIpURI({}) or ip({}) is null.", whileIpURI, ip);
        }
        return true;
    }


    private class CheckRs {
        private boolean suc;
        private String msg;
        private Map<String, Object> map = new HashMap<>();

        public boolean isSuc() {
            return suc;
        }

        public void setSuc(boolean suc) {
            this.suc = suc;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }

        public Map<String, Object> getMap() {
            return map;
        }

        public void addParam(String k, Object v) {
            map.put(k, v);
        }
    }

}
