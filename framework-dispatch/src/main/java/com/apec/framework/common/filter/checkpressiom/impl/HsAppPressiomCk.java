package com.apec.framework.common.filter.checkpressiom.impl;

import com.alibaba.fastjson.JSONObject;
import com.apec.framework.common.config.HsPessiomConfig;
import com.apec.framework.common.filter.checkpressiom.AbsCkPressiom;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Auther: liliwei
 * @Date: 2018/5/14 19:10
 * @Description:
 */
@Component("hsAppPressiomCk")
public class HsAppPressiomCk extends AbsCkPressiom
{

    @Autowired
    private HsPessiomConfig hsPessiomConfig;

    public boolean doPressiomFilter(HttpServletRequest request,HttpServletResponse response){
        String path = request.getServletPath();
        String checkType=hsPessiomConfig.getSessionTypeMap().get(path);
        String functionId=hsPessiomConfig.getFunctionTypeMap().get(path);
        if("token_id".equals(checkType)){
            JSONObject result=(JSONObject)request.getAttribute("app_request");
            String tokenId=result.get(checkType).toString();
            return isPass(request,functionId);
        }
        return  true;
    }

    public boolean isPass(HttpServletRequest request,String functionId){
        /*JsonObject app_pressim = (JsonObject)request.getAttribute("app_pressim");
        String code = hsPessiomConfig.getFuctionOperationPower(functionId);
        if(checkOperationAuthority(code,app_pressim)){
            return true;
        }else{
            return false;
        }*/
        return true;
    }

    private boolean checkOperationAuthority(String fund_code,JSONObject cachedate){
        boolean bl = false;
        /*if(cachedate.containsKey("permissions_data")){
            String permissions_data  = cachedate.getString("permissions_data");
            if(permissions_data.contains(fund_code)){
                bl = true;
            }
        }*/
        return bl;
    }
}
