package com.apec.framework.common.config;

import org.apache.commons.lang.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Auther: liliwei
 * @Date: 2018/5/11 15:15
 * @Description:hs App 类型区分，后期权限校验
 */

@ConfigurationProperties(prefix = "hs")
@Configuration
@PropertySource(value = "classpath:hs-app-config.properties")
public class HsPessiomConfig
{
    private Map<String, String> sessionTypeMap;

    private Map<String, String > functionTypeMap;

    private String checkAccount;

    private boolean userIdOpen;

    private String userIdDefault;

    private Map<String ,String> userIdMap;

    private String  decipherOpen;

    private String decipherConfig;

    private String userIdSpecial;

    private String userIdSupply;

    private String ifNoSendT2;

   // private  Map<String, List<Param>> paramMap = new HashMap<>();
    private  Map<String,String> operationMap = new HashMap<>();

    public String getFuctionOperationPower(String function){
        String str = "";
        if(operationMap.containsKey(function)){
            str = operationMap.get(function);
        }
        return str;
    }

    public Map<String, String> getOperationMap()
    {
        return operationMap;
    }

    public void setOperationMap(Map<String, String> operationMap)
    {
        this.operationMap = operationMap;
    }

    public Map<String, String> getSessionTypeMap()
    {
        return sessionTypeMap;
    }

    public void setSessionTypeMap(Map<String, String> sessionTypeMap)
    {
        this.sessionTypeMap = sessionTypeMap;
    }

    public Map<String, String> getFunctionTypeMap()
    {
        return functionTypeMap;
    }

    public void setFunctionTypeMap(Map<String, String> functionTypeMap)
    {
        this.functionTypeMap = functionTypeMap;
    }

    public String getCheckAccount()
    {
        return checkAccount;
    }

    public void setCheckAccount(String checkAccount)
    {
        this.checkAccount = checkAccount;
    }

    public boolean isUserIdOpen()
    {
        return userIdOpen;
    }

    public void setUserIdOpen(boolean userIdOpen)
    {
        this.userIdOpen = userIdOpen;
    }

    public String getUserIdDefault()
    {
        return userIdDefault;
    }

    public void setUserIdDefault(String userIdDefault)
    {
        this.userIdDefault = userIdDefault;
    }

    public Map<String, String> getUserIdMap()
    {
        return userIdMap;
    }

    public void setUserIdMap(Map<String, String> userIdMap)
    {
        this.userIdMap = userIdMap;
    }

    public String getDecipherOpen()
    {
        return decipherOpen;
    }

    public void setDecipherOpen(String decipherOpen)
    {
        this.decipherOpen = decipherOpen;
    }

    public String getDecipherConfig()
    {
        return decipherConfig;
    }

    public void setDecipherConfig(String decipherConfig)
    {
        this.decipherConfig = decipherConfig;
    }

    public String getUserIdSpecial()
    {
        return userIdSpecial;
    }

    public void setUserIdSpecial(String userIdSpecial)
    {
        this.userIdSpecial = userIdSpecial;
    }

    public String getUserIdSupply()
    {
        return userIdSupply;
    }

    public void setUserIdSupply(String userIdSupply)
    {
        this.userIdSupply = userIdSupply;
    }

    public String getIfNoSendT2()
    {
        return ifNoSendT2;
    }

    public void setIfNoSendT2(String ifNoSendT2)
    {
        this.ifNoSendT2 = ifNoSendT2;
    }

    public  boolean getUserIdOpen()
    {
        boolean bl = false;
        if ("true".equals(userIdOpen)) {
            bl = true;
        }
        return bl;
    }

    public  String getUserIdValue(String function)
    {
        if (StringUtils.isBlank(userIdDefault)) {
            userIdDefault = "userId";
        }
        String userId = userIdDefault;
        if ((StringUtils.isNotBlank(function)) && (userIdMap.containsKey(function))) {
            userId = (String)userIdMap.get(function);
        }
        return userId;
    }
}
