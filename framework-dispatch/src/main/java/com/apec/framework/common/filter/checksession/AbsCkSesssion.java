package com.apec.framework.common.filter.checksession;

import com.apec.framework.common.exception.ApecRuntimeException;
import com.apec.framework.common.model.ResultData;
import com.apec.framework.common.util.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * @Auther: liliwei
 * @Date: 2018/5/12 09:41
 * @Description:
 */
public abstract class AbsCkSesssion implements ICkSesssion
{

    public Logger log = LoggerFactory.getLogger(getClass());

    /**
     * 返回session超时信息
     * @param response 响应头
     */
    public void handlerError(HttpServletResponse response,ResultData<Object> resultData)
    {
        try
        {
            response.setCharacterEncoding("UTF-8");
            PrintWriter out = response.getWriter();
            response.setHeader("Content-Type ", "application/json;charset=UTF-8");
            out.print(JsonUtils.toJSONString(resultData));
            out.close();
        }
        catch (IOException e)
        {
            log.error("//// AbsFilter.handlerError exception case:" + e);
            throw new ApecRuntimeException("AbsFilter.handlerError exception case:", e);
        }
    }
}
