
package com.apec.framework.common.service.impl;

import com.apec.cache.base.CacheService;
import com.apec.framework.common.constant.ErrorCodeConsts;
import com.apec.framework.common.constant.FrameConsts;
import com.apec.framework.common.dto.UploadCfgDTO;
import com.apec.framework.common.dto.UploadNFSDTO;
import com.apec.framework.common.dto.UploadNFSOneFileDTO;
import com.apec.framework.common.enumtype.EnumFileUploadStatus;
import com.apec.framework.common.exception.ApecRuntimeException;
import com.apec.framework.common.exception.BusinessException;
import com.apec.framework.common.service.NFSFileUploadService;
import com.apec.framework.common.util.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.util.*;

/**
 * 类 编 号：
 * 类 名 称：NFSFileUploadServiceImpl
 * 内容摘要：NFS文件逻辑处理
 * 创建日期：2019/06/04
 * <AUTHOR>
 */
@Service
public class NFSFileUploadServiceImpl implements NFSFileUploadService
{
    private static final Logger logger = LoggerFactory.getLogger(NFSFileUploadServiceImpl.class );
    
    
    @Value("${nfs.basic.dir}")
    private String nfsBasicDir;


    @Resource(name = "redisService")
    private CacheService cacheService;
    
    @Override
    public UploadNFSDTO uploadFileNFS(HttpServletRequest request,String uploadCode)
    throws ApecRuntimeException
    {
        Map<String,String[]> paramMap = request.getParameterMap();
        logger.info("parameter:{}",JsonUtils.toJSONString(paramMap));

//        if(!paramMap.containsKey(FrameConsts.UPLOAD_CODE)
//                || null == paramMap.get(FrameConsts.UPLOAD_CODE)
//                || StringUtils.isBlank(paramMap.get(FrameConsts.UPLOAD_CODE)[0])){
//            logger.error("上传业务,缺少上传码参数:{}",FrameConsts.UPLOAD_CODE);
//            throw new ApecRuntimeException(ErrorCodeConsts.ERROR_UPLOAD_NFS_FAILD_LOAST_PARAM,Arrays.asList(FrameConsts.UPLOAD_CODE));
//        }
//        String uploadCodeParam = paramMap.get(FrameConsts.UPLOAD_CODE)[0];
//        if(!uploadCode.equals(uploadCodeParam)){
//            logger.error("请求路径上传码与请求参数的上传码不一致,请求路径上传码:{},请求参数上传码:{}",uploadCode,uploadCodeParam);
//            throw new ApecRuntimeException(ErrorCodeConsts.ERROR_UPLOAD_NFS_FAILD_LOAST_PARAM,Arrays.asList(FrameConsts.UPLOAD_CODE));
//        }
        UploadCfgDTO cfg = getUploadServiceCfg(uploadCode);

        logger.info("upload cfg:{}",JsonUtils.toJSONString(cfg));

        String batchSeq = DateUtils.formatDate( new Date(), "yyyyMMddHHmmssSSS" );
        String batchId = cfg.getCode() + "." +batchSeq;

        logger.info("batchId:{}",batchId);

        UploadNFSDTO rs = new UploadNFSDTO();
        rs.setBatchId(batchId);
        rs.setWriteStartDate(new Date());

        //判断该code是否需要用户登录。
        String userNo = (String)request.getAttribute(FrameConsts.USER_NO );
        if(StringUtils.isNotBlank(userNo)){
            logger.info("batchId:{}, userNo: {}" , batchId, userNo);
            rs.setUserNo(userNo);
        }else{
            logger.error("userNo is empty!");
        }
        
        Map<String, MultipartFile> multipartFileMap = ((MultipartHttpServletRequest)request).getFileMap();
        String basicDir = StringUtils.isNotBlank(nfsBasicDir) ? nfsBasicDir.trim() : "/home/<USER>/";
        rs.setCode(uploadCode);
        String batchItemFileDir = getNFSFilePath(basicDir,cfg);
        logger.info("batchItemFileDir:{}",batchItemFileDir);

        logger.info("batchItemFileSize:{}", CollectionUtils.isNotEmpty(multipartFileMap.keySet()) ? multipartFileMap.keySet().size() : 0);
        boolean writeFileFlag = true;
        if(CollectionUtils.isNotEmpty( multipartFileMap.keySet() ))
        {
            int fileIdx = 1;
            for(Object key : multipartFileMap.keySet())
            {
                UploadNFSOneFileDTO nfsRsDTO = new UploadNFSOneFileDTO();
                String batchItemFileId = batchId + "." + fileIdx;
                logger.info("write batchItemFileId:{}",batchItemFileId);
                nfsRsDTO.addMsg("开始处理...");
                try
                {
                    MultipartFile multipartFile = multipartFileMap.get( key );
                    //获取文件的名字
                    String originalFileName = multipartFile.getOriginalFilename();
                    nfsRsDTO.setNormalFileName(originalFileName);
                    //获取文件后缀名
                    String fileSuffix = StringUtils.substringAfterLast( originalFileName, "." );
                    //路径
                    String batchItemFileName =  batchItemFileId + "." + fileSuffix;

                    logger.info("write file,source file:{}, target file:{}",originalFileName,batchItemFileDir + batchItemFileName);

                    nfsRsDTO.addMsg("写入开始...");
                    logger.info("write start:{}",batchItemFileDir + batchItemFileName);
                    writeFile(multipartFile,batchItemFileDir,batchItemFileName);
                    logger.info("write finish:{}",batchItemFileDir + batchItemFileName);
                    nfsRsDTO.addMsg("写入完成。");

                    nfsRsDTO.setFlag(true);
                    nfsRsDTO.setBatchItemFileId(batchItemFileId);
                    nfsRsDTO.setBatchItemFilePath(batchItemFileDir + batchItemFileName);
                    nfsRsDTO.setBatchItemFileStatus(EnumFileUploadStatus.UPLOAD_SERVER.getCode());
                }
                catch (BusinessException e)
                {
                    nfsRsDTO.setFlag(false);
                    nfsRsDTO.setBatchItemFileStatus(EnumFileUploadStatus.UPLOAD_SERVER.getCode());
                    String msg = SpringUtils.getMessage(e.getErrorCode(), e.getArgs());
                    nfsRsDTO.addMsg(msg);
                    writeFileFlag = false;
                }
                rs.addOne(nfsRsDTO);
                fileIdx ++;
            }
        }else{
            logger.error("multipartFileMap is empty!");
        }

        rs.setWriteEndDate(new Date());
        rs.setFlag(writeFileFlag);
        setBatchCache(rs,cfg);

        return rs;
    }

    @Override
    public UploadCfgDTO getUploadServiceCfg(String uploadCode) {
        String cacheKey = "cache_nfs_service_cfg_" + uploadCode.toLowerCase();
        String val = cacheService.get(cacheKey);
        UploadCfgDTO rs = JsonUtils.parseObject(val,UploadCfgDTO.class);
        if(logger.isDebugEnabled()){
            logger.debug("{} upload config:{}",uploadCode,JsonUtils.toJSONString(rs));
        }
        return rs;
    }

    public String getCacheKey4FileUploadBatchData(String batchId)
    {
        return "cache_filemgt_batch_data_" + batchId.trim().toLowerCase();
    }

    private void setBatchCache(UploadNFSDTO uploadNFSDTO,UploadCfgDTO cfg)
    {
        String cacheKey = getCacheKey4FileUploadBatchData(uploadNFSDTO.getBatchId());
        cacheService.add(cacheKey,JsonUtils.toJSONString(uploadNFSDTO), null == cfg.getBatchCacheSecond() ? 600 : cfg.getBatchCacheSecond());
    }


    public static String getNFSFilePath(String basicDir,UploadCfgDTO cfg)
    {
        String tempStr = cfg.getOecdNo()
                + File.separatorChar + cfg.getSysCode()
                + File.separatorChar + cfg.getModuleCode()
                + File.separatorChar + cfg.getMethodCode()
                + File.separatorChar ;
        if(basicDir.endsWith(File.separator)) {
            return basicDir.trim() + tempStr;
        }
        else {
            return basicDir.trim() + File.separatorChar + tempStr;
        }
    }
    
    
    public static void writeFile(MultipartFile multipartFile,String nfsFileDir,String fileName)
    throws BusinessException
    {
        BufferedInputStream in = null;
        BufferedOutputStream out = null;
        try{
            File dir = new File(nfsFileDir);
            if(!dir.exists()){
                dir.mkdirs();
                logger.info("mkdirs:{}",nfsFileDir);
            }
            
            in = new BufferedInputStream(multipartFile.getInputStream());
            out = new BufferedOutputStream(new FileOutputStream(new File(nfsFileDir + File.separatorChar + fileName)));
            int len = -1;
            byte[] b = new byte[1024];
            while((len=in.read(b))!=-1){
                out.write(b,0,len);
            }
            in.close();
            out.close();
        }
        catch (IOException e)
        {
            logger.error( "It occurred error in executing writeFile,fileName:{}, Exception Message:{}", fileName, e.getMessage() );
            throw new BusinessException( ErrorCodeConsts.ERROR_FTP_UPLOAD_FAILD, e.getMessage() );
        }finally {
            try {
                if(null != in){
                    in.close();
                }
                if(null != out){
                    out.close();
                }
            } catch (IOException e) {
                logger.error("close BufferedStream Exception:{}",e.getMessage());
            }
        }
        
    }
    
}