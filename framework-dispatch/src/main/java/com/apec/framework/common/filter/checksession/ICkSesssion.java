package com.apec.framework.common.filter.checksession;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @Auther: liliwei
 * @Date: 2018/5/12 10:17
 * @Description:
 */
public interface ICkSesssion
{
    boolean doSessionFilter(HttpServletRequest servletRequest, HttpServletResponse servletResponse) throws IOException,
        ServletException;
}
