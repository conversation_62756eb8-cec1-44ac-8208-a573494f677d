
package com.apec.framework.common.service;

import com.alibaba.fastjson.JSONObject;
import com.apec.framework.common.dto.UploadCfgDTO;
import com.apec.framework.common.dto.UploadNFSDTO;
import com.apec.framework.common.dto.UploadNFSOneFileDTO;
import com.apec.framework.common.exception.ApecRuntimeException;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

public interface NFSFileUploadService
{
    /**
     * 上传文件到NFS
     * @param request 请求信息
     */
    UploadNFSDTO uploadFileNFS(HttpServletRequest request,String uploadCode)
    throws ApecRuntimeException;

    /**
     * 通过上传编码获取上传配置
     * @param uploadCode
     * @return
     */
    UploadCfgDTO getUploadServiceCfg(String uploadCode)
            throws ApecRuntimeException;
}