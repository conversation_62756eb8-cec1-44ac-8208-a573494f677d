package com.apec.framework.common.service;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.commons.net.util.Base64;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;

/**   
 * @ClassName:  HttpPostFormDataService   
 * @Description: 
 * 		http 携带basic auth请求
 * 		由于已重写resttemplate,已弃用
 * @author: goofly
 * @date:   2018年9月21日 下午4:39:23   
 *      
 */
@Deprecated
public class HttpPostFormDataService {

	@Value("${auth-server}")
	private String authServerUrl;

	@Value("${auth.client.id}")
	private String clientId;

	@Value("${auth.client.secret}")
	private String clientSecret;

	public String postFormData(Map<String, String> reqParam) throws ClientProtocolException, IOException {

		String responseContent = null;

		CredentialsProvider credsProvider = new BasicCredentialsProvider();
		credsProvider.setCredentials(new AuthScope(authServerUrl, 80),
				new UsernamePasswordCredentials(clientId, clientSecret));
		CloseableHttpClient httpclient = HttpClients.custom().setDefaultCredentialsProvider(credsProvider).build();

		HttpPost httppost = new HttpPost(authServerUrl + "authentication/form");

		// 装填参数
		List<NameValuePair> nvps = new ArrayList<NameValuePair>();
		if (reqParam != null) {
			for (Entry<String, String> entry : reqParam.entrySet()) {
				nvps.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
			}
		}
		UrlEncodedFormEntity reqEntity = new UrlEncodedFormEntity(nvps, "utf-8");
		httppost.setEntity(reqEntity);
		httppost.setHeader("Authorization", getHeader());

		// 执行post请求.
		CloseableHttpResponse response = httpclient.execute(httppost);

		if (response.getStatusLine().getStatusCode() == 200) {
			HttpEntity entity = response.getEntity();
			responseContent = EntityUtils.toString(entity, "UTF-8");
		}

		if (response != null)
			response.close();
		if (httpclient != null)
			httpclient.close();

		return responseContent;

	}

	private String getHeader() {
		String auth = clientId + ":" + clientSecret;
		byte[] encodedAuth = Base64.encodeBase64(auth.getBytes(Charset.forName("US-ASCII")));
		String authHeader = "Basic " + new String(encodedAuth);
		return authHeader; 
	}
}