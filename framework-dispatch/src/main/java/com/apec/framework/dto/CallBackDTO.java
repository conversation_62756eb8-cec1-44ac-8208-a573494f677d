package com.apec.framework.dto;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CallBackDTO {
    /**
     * 系统编码
     */
    private String systemCode;
    /**
     * 服务编码
     */
    private String serviceCode;
    /**
     * 方法编码
     */
    private String methodCode;
    /**
     * 请求参数
     */
    private String reqData;
    /**
     * header 请求头
     */
    private Map<String,String> header;

    /**
     * 属性
     */
    private Map<String, Object> attrMap;

    /**
     * uri后面的参数
     */
    private Map<String, Object> uriMap;

}
