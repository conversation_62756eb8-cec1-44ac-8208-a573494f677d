package com.apec.framework.dto;

import com.apec.framework.common.util.JsonUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * @Auther: liliwei
 * @Date: 2018/11/5 11:25
 * @Description:
 */
public class ApiRequest
{

    /**
     * 功能号
     */
    private String functionId;

    /**
     * 版本号
     */
    private String version;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 编码格式
     */
    private String charset;

    /**
     * 签名
     */
    private String sign;

    /**
     * 发送时间
     */
    private String timestamp;

    /**
     * 请求序号
     */
    private String reqSerial;

    /**
     * 请求内容
     */
    private String bizContent;

    public static ApiRequest from(Map<String, String[]> reqMap) {
        ApiRequest request = new ApiRequest();

        request.setFunctionId(findValue("function_id", reqMap));
        request.setVersion(findValue("version", reqMap));
        request.setAppId(findValue("app_id", reqMap));
        request.setCharset(StringUtils.defaultIfEmpty(findValue("charset", reqMap), "UTF-8"));
        request.setSign(findValue("sign", reqMap));
        request.setTimestamp(findValue("timestamp", reqMap));
        request.setReqSerial(findValue("req_serial", reqMap));
        request.setBizContent(findValue("biz_content", reqMap));

        return request;
    }

    public static ApiRequest jsonBody(String json) {

        ApiRequest request = JsonUtils.parseObject(json, ApiRequest.class);
        return request;
    }

    /**
     * 从请求参数中取值，如果有多个值，只取第一个
     *
     * @param key
     * @param reqMap
     * @return
     */
    private static String findValue(String key, Map<String, String[]> reqMap) {

        String[] val = reqMap.get(key);
        if (val != null && val.length > 0) {
            return val[0];
        }
        return null;
    }

    public String toString() {
        return "ApiRequest(functionId=" + getFunctionId() + ", version=" + getVersion() + ", appId=" + getAppId()
               + ", charset=" + getCharset() + ", sign=" + getSign() + ", timestamp=" + getTimestamp() + ", reqSerial="
               + getReqSerial() + ", bizContent=" + getBizContent() + ")";
    }

    @Override
    public int hashCode() {
        int result = 1;
        Object $functionId = getFunctionId();
        result = result * 59 + ($functionId == null ? 43 : $functionId.hashCode());
        Object $version = getVersion();
        result = result * 59 + ($version == null ? 43 : $version.hashCode());
        Object $appId = getAppId();
        result = result * 59 + ($appId == null ? 43 : $appId.hashCode());
        Object $charset = getCharset();
        result = result * 59 + ($charset == null ? 43 : $charset.hashCode());
        Object $sign = getSign();
        result = result * 59 + ($sign == null ? 43 : $sign.hashCode());
        Object $timestamp = getTimestamp();
        result = result * 59 + ($timestamp == null ? 43 : $timestamp.hashCode());
        Object $reqSerial = getReqSerial();
        result = result * 59 + ($reqSerial == null ? 43 : $reqSerial.hashCode());
        Object $bizContent = getBizContent();
        result = result * 59 + ($bizContent == null ? 43 : $bizContent.hashCode());
        return result;
    }

    protected boolean canEqual(Object other) {
        return other instanceof ApiRequest;
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof ApiRequest)) {
            return false;
        }
        ApiRequest other = (ApiRequest) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$functionId = getFunctionId();
        Object other$functionId = other.getFunctionId();
        if (this$functionId == null ? other$functionId != null : !this$functionId.equals(other$functionId)) {
            return false;
        }
        Object this$version = getVersion();
        Object other$version = other.getVersion();
        if (this$version == null ? other$version != null : !this$version.equals(other$version)) {
            return false;
        }
        Object this$appId = getAppId();
        Object other$appId = other.getAppId();
        if (this$appId == null ? other$appId != null : !this$appId.equals(other$appId)) {
            return false;
        }
        Object this$charset = getCharset();
        Object other$charset = other.getCharset();
        if (this$charset == null ? other$charset != null : !this$charset.equals(other$charset)) {
            return false;
        }
        Object this$sign = getSign();
        Object other$sign = other.getSign();
        if (this$sign == null ? other$sign != null : !this$sign.equals(other$sign)) {
            return false;
        }
        Object this$timestamp = getTimestamp();
        Object other$timestamp = other.getTimestamp();
        if (this$timestamp == null ? other$timestamp != null : !this$timestamp.equals(other$timestamp)) {
            return false;
        }
        Object this$reqSerial = getReqSerial();
        Object other$reqSerial = other.getReqSerial();
        if (this$reqSerial == null ? other$reqSerial != null : !this$reqSerial.equals(other$reqSerial)) {
            return false;
        }
        Object this$bizContent = getBizContent();
        Object other$bizContent = other.getBizContent();
        return this$bizContent == null ? other$bizContent == null : this$bizContent.equals(other$bizContent);
    }

    public void setBizContent(String bizContent) {
        this.bizContent = bizContent;
    }

    public void setReqSerial(String reqSerial) {
        this.reqSerial = reqSerial;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public void setCharset(String charset) {
        this.charset = charset;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public void setFunctionId(String functionId) {
        this.functionId = functionId;
    }

    public String getBizContent() {
        return this.bizContent;
    }

    public String getReqSerial() {
        return this.reqSerial;
    }

    public String getTimestamp() {
        return this.timestamp;
    }

    public String getSign() {
        return this.sign;
    }

    public String getCharset() {
        return this.charset;
    }

    public String getAppId() {
        return this.appId;
    }

    public String getVersion() {
        return this.version;
    }

    public String getFunctionId() {
        return this.functionId;
    }
}

