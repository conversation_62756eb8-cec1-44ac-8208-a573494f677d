/*
package com.apec.framework.auth;
import com.apec.framework.auth.filter.HeaderFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configurers.ResourceServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter;
import org.springframework.security.oauth2.provider.expression.OAuth2WebSecurityExpressionHandler;

import com.apec.framework.auth.handle.ApecAccessDeineHandler;
import com.apec.framework.auth.handle.ApecAuthenticationEntryPoint;
import com.apec.framework.auth.rdba.AuthorizeConfigProvider;
import org.springframework.security.web.access.intercept.FilterSecurityInterceptor;
import org.springframework.security.web.header.HeaderWriterFilter;

import java.util.ArrayList;
import java.util.List;

*/
/**
 * @ClassName:  OAuth2ResourceServerConfigJwt   
 * @Description:资源配置
 * @author: goofly
 * @date:   2018年8月20日 下午1:49:02   
 *      
 *//*

@Configuration
@EnableResourceServer
public class ResourceServerConfig extends ResourceServerConfigurerAdapter {
	
	@Value("${oauth_allow_url}")
	private String[] allowUrl;
	
    @Autowired
    private AuthorizeConfigProvider authorizeConfigProvider;
	
    @Autowired
    private OAuth2WebSecurityExpressionHandler expressionHandler;

    @Autowired
    private HeaderFilter headerFilter;
	
    @Override
    public void configure(HttpSecurity http) throws Exception {
        http.sessionManagement()
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                .and()
                .authorizeRequests().antMatchers(allowUrl).permitAll()
                    .anyRequest().authenticated();
        http.addFilterBefore(headerFilter,HeaderWriterFilter.class);
        authorizeConfigProvider.config(http.authorizeRequests());
    }


    
    @Override
    public void configure(ResourceServerSecurityConfigurer resources) throws Exception {
        resources.authenticationEntryPoint(new ApecAuthenticationEntryPoint()).accessDeniedHandler(new ApecAccessDeineHandler());
        resources.expressionHandler(expressionHandler);
    }
    
    @Bean
    public OAuth2WebSecurityExpressionHandler oAuth2WebSecurityExpressionHandler(ApplicationContext applicationContext) {
        OAuth2WebSecurityExpressionHandler expressionHandler = new OAuth2WebSecurityExpressionHandler();
        expressionHandler.setApplicationContext(applicationContext);
        return expressionHandler;
    }

}*/
