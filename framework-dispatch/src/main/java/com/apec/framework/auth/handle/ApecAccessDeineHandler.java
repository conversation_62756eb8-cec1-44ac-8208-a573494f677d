/*
package com.apec.framework.auth.handle;

import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.apec.framework.common.constant.OauthExceptionConstants;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;

import com.alibaba.fastjson.JSONObject;
import com.apec.framework.common.model.ResultData;

*/
/**
 * 403异常处理   
 * @author: goofly
 *      
 *//*

public class ApecAccessDeineHandler implements AccessDeniedHandler {

    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response,
            AccessDeniedException accessDeniedException) throws IOException, ServletException {
    	
    	
        Map<String,String> map = new HashMap<>();
        map.put("path", request.getServletPath());
        map.put("timestamp", String.valueOf(new Date().getTime()));
        map.put("desc", "没有访问权限!");

        ResultData<Map<String,String>> result = new ResultData<>();
        result.setSucceed(false);
        result.setErrorCode(OauthExceptionConstants.NOT_HAS_PERMISSION_ERROR);
        result.setErrorMsg("你没有访问权限");
        // result.setData(map);

        response.setContentType("application/json");
        response.setStatus(HttpServletResponse.SC_OK);
        response.setCharacterEncoding("utf-8");
        
        response.getWriter().print(JSONObject.toJSONString(result));
    }

}*/
