package com.apec.framework.auth;

import java.util.Arrays;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.support.BasicAuthorizationInterceptor;
import org.springframework.http.converter.FormHttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.client.RestTemplate;

/**      
 * @Description: 认证resttemplate
 * @author: <EMAIL>
 * @date:   2018年9月4日 上午11:20:38   
 */
@Configuration
public class RestConfig {

	@Value("${web.auth.client.id}")
	private String webClientId;

	@Value("${web.auth.client.secret}")
	private String webClientSecret;
	
	@Value("${app.auth.client.id}")
	private String appClientId;
	
	@Value("${app.auth.client.secret}")
	private String appClientSecret;

	@Bean("webRestTemplate")
	@LoadBalanced
	public RestTemplate webRestTemplate() {
		final RestTemplate restTemplate = new RestTemplate();
		restTemplate
				.setMessageConverters(Arrays.asList(new FormHttpMessageConverter(), new StringHttpMessageConverter()));
		restTemplate.getInterceptors().add(new BasicAuthorizationInterceptor(webClientId, webClientSecret));
		return restTemplate;
	}
	
	@Bean("appRestTemplate")
	@LoadBalanced
	public RestTemplate appRestTemplate() {
		final RestTemplate restTemplate = new RestTemplate();
		restTemplate
		.setMessageConverters(Arrays.asList(new FormHttpMessageConverter(), new StringHttpMessageConverter()));
		restTemplate.getInterceptors().add(new BasicAuthorizationInterceptor(appClientId, appClientSecret));
		return restTemplate;
	}


	@Bean("permissionTemplate")
	@LoadBalanced
	public RestTemplate permissionTemplate() {
		final RestTemplate restTemplate = new RestTemplate();
		return restTemplate;
	}
}
