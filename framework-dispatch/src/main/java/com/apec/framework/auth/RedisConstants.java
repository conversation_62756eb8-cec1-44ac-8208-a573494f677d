package com.apec.framework.auth;

public interface RedisConstants {

	String ROLE_PERMISSION_KEY="role_permission_key_";

	String APP_MAGPIE_USER_KEY ="app_magpie_user_id_";

	String APP_MAGPIE_USER_ACCOUNT_KEY ="app_magpie_user_account_";

	String APP_MAGPIE_USER_NAME ="app_magpie_user_name_";

	String PC_MAGPIE_USER_KEY ="pc_magpie_user_id_";

	String APP_MAGPIE_ACCOUNT_KEY ="app_magpie_account_id_";

	String PC_MAGPIE_USER_NAME ="pc_magpie_user_name_";

	String ROLE_RESOURCE_KEY="role_resource_key_";

	/**
	 * 白名单URL
	 */
	String DISPATCH_WHITE_LIST_URL = "dispatch:white:list:url";

	/**
	 * 白名单前缀
	 */
	String DISPATCH_WHITE_LIST_URL_PREFIX = "dispatch:white:list:url:prefix";
	String MAGPIE_OPEN_QYWX_KEY="qywx_magpie_user_id_";
	String MAGPIE_OPEN_QYWX_NAME="qywx_magpie_user_name_";

	/** 与帐号权限与状态相关的,一定要保证key一致! */
	String UC_USER_DISABLE_ = "uc_user_disable_";
	String ACCOUNT_ROLES = "account_roles_";
	String ACCOUNT_DISABLE = "account_disable_";

	String UUMS_USER_DISABLE_ = "uums_user_disable_";
	String UUMS_USER_ROLES = "uums_user_roles_";

}
