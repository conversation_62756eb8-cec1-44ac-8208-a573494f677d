package com.apec.framework.auth.filter;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.soap.MimeHeaders;
import java.io.IOException;
import java.lang.reflect.Field;
import java.net.URLDecoder;

/**
 * @Auther: liliwei
 * @Date: 2019/1/14 16:16
 * @Description:
 */
@Component
public class HeaderFilter implements Filter
{
    @Override
    public void init(FilterConfig filterConfig) throws ServletException
    {

    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
        throws IOException, ServletException
    {
        HttpServletRequest request=(HttpServletRequest)servletRequest;
        HttpServletResponse response=(HttpServletResponse)servletResponse;
        String authorization = request.getHeader("Authorization");
        if(StringUtils.isEmpty(authorization)) {
            Cookie[] cookies = request.getCookies();
            if(cookies != null) {
                for (Cookie cookie : cookies) {
                    if("Authorization".equals(cookie.getName())) {
                        authorization = cookie.getValue();
                        authorization = URLDecoder.decode(authorization, "UTF-8");
                    }
                }
            }
            if(StringUtils.isNotEmpty(authorization)){
                ParameterRequestWrapper requestWrapper = new ParameterRequestWrapper((HttpServletRequest)request);
                requestWrapper.addParameter("access_token" , authorization.split("bearer")[1].trim());
                filterChain.doFilter(requestWrapper, response);
            }else {
                filterChain.doFilter(servletRequest, response);
            }
        }else {
            filterChain.doFilter(servletRequest, response);
        }
        /*BodyReaderHttpServletRequestWrapper bodyRequest=new BodyReaderHttpServletRequestWrapper(request);
        bodyRequest.putHeader("Authorization",authorization);*/
    }

    @Override
    public void destroy()
    {

    }
}
