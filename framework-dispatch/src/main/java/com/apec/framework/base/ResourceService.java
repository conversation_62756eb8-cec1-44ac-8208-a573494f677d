package com.apec.framework.base;

import org.springframework.http.HttpHeaders;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;

/**
 * <AUTHOR> liuc
 * @date : 2019/2/19 17:21
 * @description : 提供二进制流
 */
public interface ResourceService {

    FileDownload invokeRestful(String serviceUrl, String jsonStr, HttpHeaders reqHttpHeaders);

    FileDownload service(String serverName, String methodName, HttpServletRequest req, HttpServletResponse response);

}
