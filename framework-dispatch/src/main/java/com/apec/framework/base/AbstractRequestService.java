package com.apec.framework.base;

import java.io.IOException;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import com.apec.framework.common.util.HttpRequestUtils;


import com.apec.framework.common.constant.ConfigConsts;
import com.apec.framework.common.constant.FrameConsts;
import com.apec.framework.common.util.JsonUtils;
import com.apec.framework.common.util.JssbUtils;
import com.apec.framework.common.util.StringUtils;
import com.apec.redis.common.util.StringUtil;
import com.google.common.base.Strings;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 类 编 号：
 * 类 名 称：AbstractRequestService
 * 内容摘要：抽象请求类,用于处理页面传递过来的request
 * 完成日期：
 * 编码作者：import org.apache.log4j.Logger;
 */
public abstract class AbstractRequestService
{
    private static Logger log = LoggerFactory.getLogger( AbstractRequestService.class );

    /**
     * 前置空方法,扩展类实现
     */
    protected void preHandle(HttpServletRequest request)
    {

    }

    /**
     * 后置空方法,扩展类实现
     */
    protected void afterCompletion(HttpServletRequest request)
    {

    }

    /**
     * 解析所有request请求属性并缓存
     * @param request 请求信息
     */
    protected Map<String, Object> parseRequestAttribute(HttpServletRequest request)
    {
        // 取出所有参数
        Map<String, Object> requestAttrMap = new HashMap<>();
        try
        {
            Enumeration<String> paramNames = request.getAttributeNames();
            if(null != paramNames)
            {
                while (paramNames.hasMoreElements())
                {
                    String attrName = paramNames.nextElement();
                    if(!attrName.contains( "." ))
                    {
                        Object attrValue = request.getAttribute( attrName );
                        requestAttrMap.put( attrName, attrValue );
                    }
                }
            }
            else
            {
                log.info( "Request request without any attribute!" );
            }
        }
        catch (Exception e)
        {
            log.error( "It occured error in executing  AbstractRequestService.parseRequestAttribute,Cause:"
                       + e.getMessage() );
        }
        parseSessionAttribute( request, requestAttrMap );
        return requestAttrMap;
    }

    /**
     * 解析session中属性并缓存
     * @param request 请求信息
     */
    private void parseSessionAttribute(HttpServletRequest request, Map<String, Object> requestAttrMap)
    {
        try
        {
            HttpSession httpSession = request.getSession();
            if(null != httpSession)
            {
                Enumeration paramNames = httpSession.getAttributeNames();
                if(null != paramNames)
                {
                    while (paramNames.hasMoreElements())
                    {
                        String attrName = (String)paramNames.nextElement();
                        Object attrValue = httpSession.getAttribute( attrName );
                        requestAttrMap.put( attrName, attrValue );
                    }
                }
            }
            else
            {
                log.info( "No attribute at session!" );
            }
        }
        catch (Exception e)
        {
            log.error( "It occured error in executing  AbstractRequestService.parseRequestAttribute,Cause:"
                       + e.getMessage() );
        }
    }

    /**
     *
     * @param request 经过spring处理过的request，请求输入流已经没有数据
     * @return formJson里面的数据
     */
    protected String parseRequest(HttpServletRequest request)
    {
        //表单方式请求
        Map<String, String[]> parameterMap = request.getParameterMap();
        // uri后面的参数
        Map<String, Object> uriMap = new HashMap<>();
        // 流数据，此处数据格式是json
        String requestJson = null;
        if(null != parameterMap && !parameterMap.isEmpty())
        {
            for(Map.Entry<String, String[]> entry : parameterMap.entrySet())
            {
                String key = entry.getKey();
                String[] value = entry.getValue();
                // 判断官方经过转换的流式json
                if(null == requestJson && null != key && key.startsWith( "{" ) && key
                    .endsWith( "}" ))
                {
                    requestJson = key;
                }
                else
                {
                    if(null != value && value.length == 1)
                    {
                        uriMap.put( key, value[0] );
                    }
                    else if(null != value && value.length > 1)
                    {
                        uriMap.put( key, value );
                    }
                }
            }
        }

        // 纯json请求处理
        boolean contentTypeIsNullOrEmpty = Strings.isNullOrEmpty(request.getContentType());
        if(!contentTypeIsNullOrEmpty)
        {
            if(
                ConfigConsts.APPLICATION_JSON.equalsIgnoreCase( request.getContentType() ) ||
                 ConfigConsts.APPLICATION_JSON_CHARSET.equalsIgnoreCase( request.getContentType().replaceAll(" ",""))
            )
            {
                //获取流数据
                requestJson = HttpRequestUtils.getPostJSONData( request );
            }
            else if(request.getContentType().contains("multipart/form-data")){
                requestJson = "{}";
            }
            else if(request.getContentType().equals("application/x-www-form-urlencoded") && null != uriMap) {
                requestJson = JsonUtils.toJSONString(uriMap);
            }
        }
        
        // requestJson仍然为空就再尝试从流中获取一次
        if(null == requestJson)
        {
            requestJson = HttpRequestUtils.getPostJSONData( request );
        }
        Map<String, Object> attrMap = parseRequestAttribute( request );
        String pageJson = JssbUtils.assemblRequestJSON( requestJson, JsonUtils.toJSONString( uriMap ),
                                                       JsonUtils.toJSONString( attrMap ) );
        attrMap = null;
        uriMap = null;
        parameterMap = null;
        // log.info(pageJson);
        return pageJson;
    }


    /**
     * 描述:获取 post 请求的 byte[] 数组
     * <pre>
     * 举例：
     * </pre>
     * @param request
     * @return
     * @throws IOException
     */
    public static byte[] getRequestPostBytes(HttpServletRequest request)
        throws IOException
    {
        int contentLength = request.getContentLength();
        if(contentLength < 0)
        {
            return null;
        }
        byte buffer[] = new byte[contentLength];
        for(int i = 0; i < contentLength; )
        {

            int readlen = request.getInputStream().read( buffer, i,
                                                         contentLength - i );
            if(readlen == -1)
            {
                break;
            }
            i += readlen;
        }
        return buffer;
    }

    /**
     * 描述:获取 post 请求内容
     * <pre>
     * 举例：
     * </pre>
     * @param request
     * @return
     * @throws IOException
     */
    public static String getRequestPostStr(HttpServletRequest request)
    {
        try
        {
            byte buffer[] = getRequestPostBytes( request );
            String charEncoding = request.getCharacterEncoding();
            if(charEncoding == null)
            {
                charEncoding = "UTF-8";
            }
            return new String( buffer, charEncoding );
        }
        catch (IOException e)
        {
            e.printStackTrace();
        }

        return null;
    }

    /**
     * 取出分页表起始页页码
     * @return 起始页页码
     */
    protected int getPageNum()
    {
        String pageNum = null/*getParameter( Constants.PAGE_NUM )*/;
        pageNum = (StringUtils.isInvalidStr( pageNum ) || "".equals( pageNum )) ? FrameConsts.DEFAULT_RANGESTART : pageNum;
        int rangeStart = StringUtils.convertStringToInt( pageNum );
        return rangeStart;
    }

    /**
     * 取出分页表每页容纳数据条数
     * @return 数据条数
     */
    protected int getPageSize()
    {
        String pageSize = null/*getParameter( Constants.PAGE_SIZE )*/;
        pageSize = FrameConsts.DEFAULT_FETCHSIZE;
        int fetchSize = StringUtils.convertStringToInt( pageSize );
        return fetchSize;
    }

    
    /**
    *
    * @param request 经过spring处理过的request，请求输入流已经没有数据
    * @return formJson里面的数据
    */
   protected Map<String, Object> parseRequestGet(HttpServletRequest request)
   {
       //表单方式请求
       Map<String, String[]> parameterMap = request.getParameterMap();
       Map<String, Object> uriMap = new HashMap<String, Object>();// uri后面的参数
       if(null != parameterMap && !parameterMap.isEmpty())
       {
           for(Map.Entry<String, String[]> entry : parameterMap.entrySet())
           {
               String key = entry.getKey();
               String[] value = entry.getValue();
                   if(null != value && value.length == 1)
                   {
                       uriMap.put( key, value[0] );
                   }
                   else if(null != value && value.length > 1)
                   {
                       uriMap.put( key, value );
                   }
           }
       }
      return uriMap;
   }
   
   
}
