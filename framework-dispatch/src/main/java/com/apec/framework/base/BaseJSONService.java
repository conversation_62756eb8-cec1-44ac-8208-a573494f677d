package com.apec.framework.base;

import com.apec.framework.common.constant.ErrorCodeConsts;
import com.apec.framework.common.constant.FrameConsts;
import com.apec.framework.common.exception.DispatchException;
import com.apec.framework.common.model.ResultData;
import com.apec.framework.common.tools.UuidGenerator;
import com.apec.framework.common.util.HttpRequestUtils;
import com.apec.framework.common.util.JsonUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Enumeration;
import java.util.List;
import java.util.function.Function;

/**
 * 类 编 号：
 * 类 名 称：BaseJSONService
 * 内容摘要：服务请求分发基类,其它特殊要求处理的必须继承此类
 * 完成日期：
 * 编码作者：
 */
public abstract class BaseJSONService extends AbstractRequestService implements IJSONService
{

    @Autowired
    @Qualifier("clientTemplate")
    private RestTemplate restTemplate;

    @Value("#{'${req.exclude.headers}'.split(',')}")
    private List<String> excludeHeaders;

    private static Logger log = LoggerFactory.getLogger(BaseJSONService.class);

    /**
     * 处理页面过来的请求
     * @param req 请求信息
     * @return 请求返回结果
     */
    @Override
    public String  service(String serverName, String methodName, HttpServletRequest req, HttpServletResponse response)
    {
        String jsonStr = super.parseRequest(req);
        String serviceUrl = HttpRequestUtils.getRequestServiceUrl(serverName, methodName, StringUtils.EMPTY);
        log.debug("//// 请求URL:{} || Content-type: {} || 参数:{}", serviceUrl,req.getContentType() ,jsonStr);
        preHandle(req);
        String ret;
        HttpHeaders reqHttpHeaders = getHeaders(req);
        try
        {
            ResponseEntity<String> responseEntity = invokeRestful(serviceUrl, jsonStr, reqHttpHeaders);
            HttpHeaders respHttpHeaders = responseEntity.getHeaders();
            setResponseHeaders(response, respHttpHeaders);
            ret = responseEntity.getBody();
        }
        catch (Exception e)
        {
            log.error("//// 请求URL:{} || 头信息:{} || 参数:{}", serviceUrl, JsonUtils.toJSONString(reqHttpHeaders), jsonStr, e);
            throw new DispatchException(ErrorCodeConsts.REQ_SERVER_EXCEPTION, e.getMessage());
        }
        afterCompletion(req);
        // 返回重复提交的值
        ret = setRepeatAct(req, ret);
        log.debug("//// 请求URL:{} || 头信息:{} || 参数:{} || 结果:{} ", serviceUrl, JsonUtils.toJSONString(reqHttpHeaders),
                  jsonStr, substr(ret,256,"","..."));
        return ret;
    }

    /**
     * 设置重复提交的值
     * @param req 请求信息
     * @param ret 返回信息
     */
    private String setRepeatAct(HttpServletRequest req, String ret)
    {
        try
        {
            ResultData resultData = JsonUtils.parseObject(ret, ResultData.class);
            resultData.setRepeatAct(UuidGenerator.getUuidWithLine());
            ret = JsonUtils.toJSONString(resultData);
        }catch (Exception e){
            log.warn("////RepeatAct 重复提交的值无法转换ResultData："+e);
        }
        return ret;
    }

    /**
     * 发送rest服务到后台
     * @param serviceUrl 服务uri
     * @param jsonStr 请求json
     * @return 请求返回结果
     */
    @Override
    public String invokeRestful(String serviceUrl, String jsonStr)
    {
//        String ret = restTemplate.postForObject(serviceUrl, jsonStr, String.class);
//        return ret;
        //更换为如下方式，这样就设置了字符集。
        try
        {
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.set("Content-Type", "application/json;charset=UTF-8");
            ResponseEntity<String> responseEntity = restTemplate
                    .postForEntity(serviceUrl.trim(), new HttpEntity<>(jsonStr, httpHeaders), String.class);
            String ret = responseEntity.getBody();
            // TODO 此处后续可能要做处理
            return ret;
        }
        catch (Exception e)
        {
            log.error("//// 请求URL:{} || 参数:{}", serviceUrl, jsonStr, e);
            throw new DispatchException(ErrorCodeConsts.REQ_SERVER_EXCEPTION, e.getMessage());
        }
    }

    /**
     * 重新发送http请求，并附带头信息
     * @param serviceUrl serviceUrl
     * @param jsonStr jsonStr
     * @param reqHttpHeaders reqHttpHeaders
     * @return ResponseEntity<String>
     */
    private ResponseEntity<String> invokeRestful(String serviceUrl, String jsonStr, HttpHeaders reqHttpHeaders)
    {
        reqHttpHeaders.set("Content-Type", "application/json;charset=UTF-8");
        ResponseEntity<String> responseEntity = restTemplate
            .postForEntity(serviceUrl.trim(), new HttpEntity<>(jsonStr, reqHttpHeaders), String.class);
        // TODO 此处后续可能要做处理
        return responseEntity;
    }

    /**
     * 获取http request header信息
     * @param req 请求信息
     * @return HttpHeaders
     */
    private HttpHeaders getHeaders(HttpServletRequest req)
    {
        HttpHeaders headers = new HttpHeaders();
        Enumeration<String> headerNames = req.getHeaderNames();
        while (headerNames.hasMoreElements())
        {
            String key = headerNames.nextElement();
            if(excludeHeaders.contains(key)){
                continue;
            }
            String value = req.getHeader(key);
            headers.set(key, value);
        }
        return headers;
    }

    private void setResponseHeaders(HttpServletResponse response, HttpHeaders httpHeaders)
    {
        log.debug("//// 服务返回的头信息:{}", JsonUtils.toJSONString(httpHeaders));
        if(httpHeaders.containsKey(FrameConsts.TOKEN))
        {
            response.setHeader(FrameConsts.TOKEN, httpHeaders.getFirst(FrameConsts.TOKEN));
        }
    }

    /**
     * 裁剪字符串，拼接前缀以及后缀
     */
    public String substr(String s,int size,String prefix,String suffix){
        if(s == null){
            return "null";
        }
        if(s.length() - 1 < size+1){
            return s;
        }
        Function<String,String> f = str -> str.substring(0,size+1);
        Function<String,String> f2 = str -> str+suffix;
        Function<String,String> f3 = str -> prefix+str;
        return substr(s,f.andThen(f3).andThen(f2));
    }

    /**
     * 裁剪字符串
     */
    public String substr(String s, Function<String,String> sa){
        return sa.apply(s);
    }
}
