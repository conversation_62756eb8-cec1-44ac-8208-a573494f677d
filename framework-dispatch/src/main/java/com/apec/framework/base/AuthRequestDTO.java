package com.apec.framework.base;

import org.hibernate.validator.constraints.NotEmpty;

import com.alibaba.fastjson.JSON;

import lombok.Data;

/**   
 * @Description:认证接口(配置做自定义异常MethodArgumentNotValidException统一处理)
 * @author: goofly
 * @date:   2018年9月21日 下午4:03:01   
 */

@Data
public class AuthRequestDTO {
	
	private String username;

	private String userKey;

	private String password;
	
	private String loginType;
	
	private String channelType;
	
	private Integer days;

	private String openId;

	private String deviceid;

	private String imageCode;

	private String aesKey;
	
	private String aesIv;

	//用于钉钉登录
	private String authCode;
	//2:钉钉登录只获取钉钉人员信息,1:获取钉钉人员的账号权限信息
	private String dtAuthType = "1";

	private String accountType;

	private String marketId;

	public String toJSONString() {
		return JSON.toJSONString(this);
	}
}
