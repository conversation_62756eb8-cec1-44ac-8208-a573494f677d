package com.apec.framework.base;

import com.apec.framework.common.constant.ErrorCodeConsts;
import com.apec.framework.common.exception.DispatchException;
import com.apec.framework.common.util.HttpRequestUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.ByteArrayHttpMessageConverter;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Enumeration;
import java.util.List;

/**
 * <AUTHOR> liuc
 * @date : 2019/2/19 17:24
 * @description :
 */
@Service
public class ResourceServiceImpl extends AbstractRequestService implements ResourceService {

    @Autowired
    @Qualifier("clientTemplate")
    private RestTemplate restTemplate;

    private static Logger log = LoggerFactory.getLogger(BaseJSONService.class);

    @Override
    public FileDownload invokeRestful(String serviceUrl, String jsonStr,HttpHeaders reqHttpHeaders) {
        //setConverters(restTemplate);
        try
        {
            /*HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.add("Content-Type", "application/force-download");
            httpHeaders.add("Content-Type", "application/vnd.ms-excel");
            httpHeaders.add("Content-Type", "application/octet-stream");*/
            ResponseEntity<byte[]> responseEntity = restTemplate
                    .postForEntity(serviceUrl.trim(), new HttpEntity<>(jsonStr, reqHttpHeaders), byte[].class);
            if(responseEntity.getStatusCode().equals(HttpStatus.OK)){
                FileDownload fd = new FileDownload();
                fd.setBytes(responseEntity.getBody());
                List<String> ctHeaders = responseEntity.getHeaders().get("Content-Type");
                fd.setContentTypes(ctHeaders);
                List<String> cdHeaders = responseEntity.getHeaders().get("Content-disposition");
                fd.setContentDispositions(cdHeaders);
                log.info("download file desc : contentTypes : {}, contentDispositions: {} ",fd.getContentTypes(),fd.getContentDispositions());
                return fd;
            }
            return null;
        }
        catch (Exception e)
        {
            log.error("//// 下载文件,请求URL:{} || 参数:{}", serviceUrl, jsonStr, e);
            throw new DispatchException(ErrorCodeConsts.REQ_SERVER_EXCEPTION, e.getMessage());
        }
    }

    @Override
    public FileDownload service(String serverName, String methodName, HttpServletRequest req, HttpServletResponse response) {
        String jsonStr = super.parseRequest(req);
        String serviceUrl = HttpRequestUtils.getRequestServiceUrl(serverName, methodName, StringUtils.EMPTY);
        log.debug("//// 下载文件---请求URL:{} || Content-type: {} || 参数:{}", serviceUrl,req.getContentType() ,jsonStr);
        preHandle(req);
        HttpHeaders reqHttpHeaders;
        try
        {
            reqHttpHeaders = getHeaders(req);
            return invokeRestful(serviceUrl, jsonStr, reqHttpHeaders);
        }
        catch (Exception e)
        {
            log.error("//// 下载文件---请求URL:{} || 参数:{}", serviceUrl, jsonStr, e);
            throw new DispatchException(ErrorCodeConsts.REQ_SERVER_EXCEPTION, e.getMessage());
        }
    }

    /**
     * 获取http request header信息
     * @param req 请求信息
     * @return HttpHeaders
     */
    private HttpHeaders getHeaders(HttpServletRequest req)
    {
        HttpHeaders headers = new HttpHeaders();
        Enumeration<String> headerNames = req.getHeaderNames();
        while (headerNames.hasMoreElements())
        {
            String key = headerNames.nextElement();
            String value = req.getHeader(key);
            headers.set(key, value);
        }
        return headers;
    }

    private void setConverters(RestTemplate restTemplate){
        List<HttpMessageConverter<?>> converters = restTemplate.getMessageConverters();
        if(!CollectionUtils.isEmpty(converters)){
            HttpMessageConverter byteArrConverter = converters.stream().filter(c -> c instanceof ByteArrayHttpMessageConverter).findFirst().orElse(null);
            if(byteArrConverter == null){
                converters.add(new ByteArrayHttpMessageConverter());
                restTemplate.setMessageConverters(converters);
            }
        }
    }

}
