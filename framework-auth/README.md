#  功能列表
## APP端登录

### 流程

 1. `AbstractAuthenticationProcessingFilter`
 用户请求url `/app/token`被`AppAuthenticationProcessingFilter`拦截器拦截,进入`attemptAuthentication`方法.  创建`AppAuthenticationToken`(未认证),然后交给`AppAuthenticationProvider`处理
 
 
 2. `AuthenticationProvider`
通过`AppUserDetailsService`调用客户服务接口校验用户账号密码是否正确.
若正确,则根据客户服务接口返回的信息并构造`User`对象;
若不正确,则直接抛出认证异常,与此同时返回`401`错误


 3. `SecurityConfigurerAdapter`
 配置过滤器、handle、认证器逻辑
 

``` java
        //配置过滤器
    	AppAuthenticationProcessingFilter appAuthenticationFilter = new AppAuthenticationProcessingFilter();
        appAuthenticationFilter.setAuthenticationManager(http.getSharedObject(AuthenticationManager.class));
        
        //配置handler
        appAuthenticationFilter.setAuthenticationSuccessHandler(authenticationSuccessHandler);
        appAuthenticationFilter.setAuthenticationFailureHandler(appAuthenticationFailureHandler);

        //配置认证器逻辑
        AppAuthenticationProvider appAuthenticationProvider = new AppAuthenticationProvider();
        appAuthenticationProvider.setUserDetailsService(userDetailsService);
        //在UsernamePasswordAuthenticationFilter过滤前执行
        http.authenticationProvider(appAuthenticationProvider)
                .addFilterAfter(appAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);
```

4.`AppAuthenticationSuccessHandler`
用户请求传过来的参数`loginType`判断是否返回json,默认均传json.
 - 校验client信息
 - redis取出用户信息
 - 创建token
 - 校验token有效性
 - 返回token

### dispatcher处理
APP端访问`/SSO-SERVER/authentication/form`后,具有如下设置:

 1. 设置渠道类型
 2. 转化请求参数
 3. 设置resttemplate的请求头
 4. resttemplate请求服务(需携带认证信息)


## PC端登录

1. `NormalAuthenticationProvider`
用户请求url `/authentication/form`被拦截器拦截,然后交由`NormalAuthenticationProvider`处理.
通过`PcUserDetailsService`调用客户服务接口校验用户账号密码是否正确.
若正确,则根据客户服务接口返回的信息并构造`User`对象;
若不正确,则直接抛出认证异常,与此同时返回`401`错误

2.`AppAuthenticationSuccessHandler`
用户请求传过来的参数`loginType`判断是否返回json,默认均传json.
 - 校验client信息
 - redis取出用户信息
 - 创建token
 - 校验token有效性
 - 返回token

### dispatcher处理

#### controller
APP端访问`/SSO-SERVER/authentication/form`后,具有如下设置:

 1. 设置渠道类型
 2. 转化请求参数
 3. 设置resttemplate的请求头
 4. resttemplate请求服务(需携带认证信息)

#### rdbc请求校验
` RbacService`负责处理一些白名单除外的请求.,如下:

 1. 判断用户是否登录
> 根据头信息Authorization是否有值来判断该用户是否登录.若该信息为空,则认为该用户没有登录,则返回错误码401,让其重新登录
>  获取Authorization头信息有2种方式,一个是从Request的header头部获取,另外一个是从Cookie中获取!   正常请求下前端会将Authorization存放在Request头部. 但是单点登录情况下并不能从Request的header中取值,故而只能从Cookie中取值!
	 **切记:不可只判断Request中的header是否有值从而下结论!**

 2. 校验用户是否拥有权限
 >  解析token后获取角色值authorities,然后从缓存中取出该角色对应的权限url. 并逐一判断是否与当前访问的url匹配,若匹配则放行;反之返回403错误码


## 短信登录
### 流程
同APP端登录.
**注意:**
由于短信登录并未投入使用,如后续需要接入短信服务,即只需要做如下操作:

更改`SmsCodeAuthenticationProvider`中信息查询接口即可:

``` java
public class SmsCodeAuthenticationProvider implements AuthenticationProvider {

    private UserDetailsService userDetailsService;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
    	
        SmsCodeAuthenticationToken authenticationToken = (SmsCodeAuthenticationToken) authentication;
        Object credentials = authenticationToken.getCredentials();
        
        // 如需要介入短信服务,则调用短信服务的接口即可
        UserDetails user = userDetailsService.loadUserByUsername((String) authenticationToken.getPrincipal());
        
        // 校验
        if (user == null || StringUtils.isEmpty(user.getUsername())) {
            throw new InternalAuthenticationServiceException("can not get userinfo");
        }
        if(!credentials.toString().equals(user.getPassword())) {
        	throw new BadCredentialsException("msgCode not match");
        }
        
        //构建认证信息
        SmsCodeAuthenticationToken authenticationResult = new SmsCodeAuthenticationToken(user, user.getAuthorities());
        authenticationResult.setDetails(authenticationToken.getDetails());

        return authenticationResult;
    }
	
	...
```

## APP微信绑定
### 流程
同APP端登录.
1. `OpenIdAuthenticationProcessingFilter`
用户请求url `/openId/token`被拦截器拦截,然后交由`OpenIdAuthenticationProvider`处理.

``` java
public void configure(HttpSecurity http) throws Exception {
    	
        //配置过滤器
    	OpenIdAuthenticationProcessingFilter appAuthenticationFilter = new OpenIdAuthenticationProcessingFilter();
        appAuthenticationFilter.setAuthenticationManager(http.getSharedObject(AuthenticationManager.class));

        //配置handler
        appAuthenticationFilter.setAuthenticationSuccessHandler(authenticationSuccessHandler);
        appAuthenticationFilter.setAuthenticationFailureHandler(appAuthenticationFailureHandler);

        //配置认证器逻辑
        OpenIdAuthenticationProvider appAuthenticationProvider = new OpenIdAuthenticationProvider();
        appAuthenticationProvider.setUserDetailsService(userDetailsService);
        //在UsernamePasswordAuthenticationFilter过滤前执行
        http.authenticationProvider(appAuthenticationProvider)
                .addFilterAfter(appAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);
    }
	
	...
```
详见http://192.168.7.28:8000/index.php?s=/13&page_id=2592

## 客户端模式
1. 给客户分配clientId和clientSecret
2. TokenEndpoint
 url `/oauth/token`交由TokenEndpoint处理,通过grant_type区分授权模式.
 ``` java
 @RequestMapping(value = "/SSO-SERVER/client/token", produces = "application/json;charset=UTF-8")
 	public String clientToken(HttpServletRequest request, HttpServletResponse response) {
 		String clientId=request.getParameter("clientId");
 		String clientSecret=request.getParameter("clientSecret");
 		MultiValueMap<String, String> paraMap = new LinkedMultiValueMap<String, String>();
 		paraMap.add("grant_type","client_credentials");
 		HttpEntity<MultiValueMap<String, String>> httpEntity = new HttpEntity<>(paraMap, headers);
 		if(restTemplate.getInterceptors().size()==1){
 			restTemplate.getInterceptors().add(0,new BasicAuthorizationInterceptor(clientId, clientSecret));
 		}else {
 			restTemplate.getInterceptors().set(0,new BasicAuthorizationInterceptor(clientId, clientSecret));
 		}
 		ResponseEntity<String> postForEntity = restTemplate.postForEntity(clientOauthUrl, httpEntity, String.class);
 		log.info(">>>>>>>>>>>>请求入参:{},出参:{}", httpEntity, postForEntity);
 		return postForEntity.getBody();
 	}
 	...
 ```
 详见http://192.168.7.28:8000/index.php?s=/13&page_id=2126
    
## 授权码模式
 

## token刷新
1. TokenEndpoint
 url `/oauth/token`交由TokenEndpoint处理,通过grant_type区分授权模式.
 ``` java
 @RequestMapping(value = "/SSO-SERVER/app/refreshtoken", produces = "application/json;charset=UTF-8")
 	public String refreshTokenApp(HttpServletRequest request, HttpServletResponse response) {
 		MultiValueMap<String, String> paraMap = new LinkedMultiValueMap<String, String>();
 		paraMap.add("grant_type","refresh_token");
 		paraMap.add("refresh_token",request.getParameter("refresh_token"));
 		HttpEntity<MultiValueMap<String, String>> httpEntity = new HttpEntity<>(paraMap, headers);
 		ResponseEntity<String> postForEntity = appRestTemplate.postForEntity(refreshTokenUrl, httpEntity, String.class);
 		log.info(">>>>>>>>>>>>请求入参:{},出参:{}", httpEntity, postForEntity);
 		return postForEntity.getBody();
 	}
 	...
 ```
 **注意:**
 token刷新会调用UserDetailsService的loadUserByUsername刷新用户的信息
 ，目前web端是有角色的，所以需要重写该方法，不然生成的token鉴权不通过
 

## 单点登录
### 支持
目前单点登录仅能够支持一级域名相同的情况,如:
order.ap88.com  mall.ap88.com  ap88.com 

不同域名环境下之间的单点登录暂不支持,如:a.com  b.com等.
由于认证服务器与请求中间隔了dispatcher服务,故而无法做出跨站点的单点登录.

### 设置
前端将token信息存储在Cookie中,name为Authorization,value为 "bearer" + token.

**注意**:由于空格是特殊字符串,需要用URLDecoder.decode解码!


## 验证码
**注意:**
目前所有的登入验证之前都会进行验证码校验，占时只是对大宗进行了开放；
1. `ValidateCodeFilter`

``` java
 @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
        throws ServletException, IOException
    {
        String requestURI = request.getRequestURI();
        if(!StringUtils.equals("/oauth/token_key",requestURI)){
            boolean type = getValidateCodeType(request);
            String systemType = request.getHeader("system-type");
            if(type&&StringUtils.equals(systemType,"001")){
                String deviceid = request.getParameter("deviceid");
                String imageCode = request.getParameter("imageCode");
                logger.info("校验请求(" + request.getRequestURI() + ")中的验证码,验证码唯一标志" + deviceid);
                String imageCodeCache = cacheService.get(RedisConstant.IMAGE_CODE_VALIDATE_KEY + ":" + deviceid);
                if(null==imageCodeCache||!StringUtils.equals(imageCode.toUpperCase(),imageCodeCache.toUpperCase())){
                    appAuthenticationFailureHandler.onAuthenticationFailure(request,response, new ApecAuthenticationException("验证码校验失败"));
                    return;
                }
            }
        }
        chain.doFilter(request, response);
    }
	
	...
```

## 优化建议

鉴权与dispatch耦合，不管修改dispatch还是鉴权都会影响双方环境，白名单也不是动态的加载，建议鉴权和dispatch拆分；
现在开发环境已经是拆分之后的，但是并没有进行大量测试，后期的需求也没有在这套下开发；要用这一套需要将微信绑定和验证码校验加上即可
